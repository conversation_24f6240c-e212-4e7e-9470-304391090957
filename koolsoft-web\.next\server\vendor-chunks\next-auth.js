"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-auth";
exports.ids = ["vendor-chunks/next-auth"];
exports.modules = {

/***/ "(ssr)/./node_modules/next-auth/client/_utils.js":
/*!*************************************************!*\
  !*** ./node_modules/next-auth/client/_utils.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.BroadcastChannel = BroadcastChannel;\nexports.apiBaseUrl = apiBaseUrl;\nexports.fetchData = fetchData;\nexports.now = now;\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(ssr)/./node_modules/@babel/runtime/regenerator/index.js\"));\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(ssr)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      (0, _defineProperty2.default)(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction fetchData(_x, _x2, _x3) {\n  return _fetchData.apply(this, arguments);\n}\nfunction _fetchData() {\n  _fetchData = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee(path, __NEXTAUTH, logger) {\n    var _ref,\n      ctx,\n      _ref$req,\n      req,\n      url,\n      _req$headers,\n      options,\n      res,\n      data,\n      _args = arguments;\n    return _regenerator.default.wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          _ref = _args.length > 3 && _args[3] !== undefined ? _args[3] : {}, ctx = _ref.ctx, _ref$req = _ref.req, req = _ref$req === void 0 ? ctx === null || ctx === void 0 ? void 0 : ctx.req : _ref$req;\n          url = \"\".concat(apiBaseUrl(__NEXTAUTH), \"/\").concat(path);\n          _context.prev = 2;\n          options = {\n            headers: _objectSpread({\n              \"Content-Type\": \"application/json\"\n            }, req !== null && req !== void 0 && (_req$headers = req.headers) !== null && _req$headers !== void 0 && _req$headers.cookie ? {\n              cookie: req.headers.cookie\n            } : {})\n          };\n          if (req !== null && req !== void 0 && req.body) {\n            options.body = JSON.stringify(req.body);\n            options.method = \"POST\";\n          }\n          _context.next = 7;\n          return fetch(url, options);\n        case 7:\n          res = _context.sent;\n          _context.next = 10;\n          return res.json();\n        case 10:\n          data = _context.sent;\n          if (res.ok) {\n            _context.next = 13;\n            break;\n          }\n          throw data;\n        case 13:\n          return _context.abrupt(\"return\", Object.keys(data).length > 0 ? data : null);\n        case 16:\n          _context.prev = 16;\n          _context.t0 = _context[\"catch\"](2);\n          logger.error(\"CLIENT_FETCH_ERROR\", {\n            error: _context.t0,\n            url: url\n          });\n          return _context.abrupt(\"return\", null);\n        case 20:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee, null, [[2, 16]]);\n  }));\n  return _fetchData.apply(this, arguments);\n}\nfunction apiBaseUrl(__NEXTAUTH) {\n  if (true) {\n    return \"\".concat(__NEXTAUTH.baseUrlServer).concat(__NEXTAUTH.basePathServer);\n  }\n  return __NEXTAUTH.basePath;\n}\nfunction now() {\n  return Math.floor(Date.now() / 1000);\n}\nfunction BroadcastChannel() {\n  var name = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"nextauth.message\";\n  return {\n    receive: function receive(onReceive) {\n      var handler = function handler(event) {\n        var _event$newValue;\n        if (event.key !== name) return;\n        var message = JSON.parse((_event$newValue = event.newValue) !== null && _event$newValue !== void 0 ? _event$newValue : \"{}\");\n        if ((message === null || message === void 0 ? void 0 : message.event) !== \"session\" || !(message !== null && message !== void 0 && message.data)) return;\n        onReceive(message);\n      };\n      window.addEventListener(\"storage\", handler);\n      return function () {\n        return window.removeEventListener(\"storage\", handler);\n      };\n    },\n    post: function post(message) {\n      if (true) return;\n      try {\n        localStorage.setItem(name, JSON.stringify(_objectSpread(_objectSpread({}, message), {}, {\n          timestamp: now()\n        })));\n      } catch (_unused) {}\n    }\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/client/_utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/core/errors.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/core/errors.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.UnsupportedStrategy = exports.UnknownError = exports.OAuthCallbackError = exports.MissingSecret = exports.MissingAuthorize = exports.MissingAdapterMethods = exports.MissingAdapter = exports.MissingAPIRoute = exports.InvalidCallbackUrl = exports.AccountNotLinkedError = void 0;\nexports.adapterErrorHandler = adapterErrorHandler;\nexports.capitalize = capitalize;\nexports.eventsErrorHandler = eventsErrorHandler;\nexports.upperSnake = upperSnake;\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(ssr)/./node_modules/@babel/runtime/regenerator/index.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(ssr)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/createClass.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ \"(ssr)/./node_modules/@babel/runtime/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ \"(ssr)/./node_modules/@babel/runtime/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/inherits.js\"));\nvar _wrapNativeSuper2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/wrapNativeSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/wrapNativeSuper.js\"));\nfunction _callSuper(t, o, e) {\n  return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e));\n}\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nvar UnknownError = exports.UnknownError = function (_Error) {\n  function UnknownError(error) {\n    var _message;\n    var _this;\n    (0, _classCallCheck2.default)(this, UnknownError);\n    _this = _callSuper(this, UnknownError, [(_message = error === null || error === void 0 ? void 0 : error.message) !== null && _message !== void 0 ? _message : error]);\n    _this.name = \"UnknownError\";\n    _this.code = error.code;\n    if (error instanceof Error) {\n      _this.stack = error.stack;\n    }\n    return _this;\n  }\n  (0, _inherits2.default)(UnknownError, _Error);\n  return (0, _createClass2.default)(UnknownError, [{\n    key: \"toJSON\",\n    value: function toJSON() {\n      return {\n        name: this.name,\n        message: this.message,\n        stack: this.stack\n      };\n    }\n  }]);\n}((0, _wrapNativeSuper2.default)(Error));\nvar OAuthCallbackError = exports.OAuthCallbackError = function (_UnknownError) {\n  function OAuthCallbackError() {\n    var _this2;\n    (0, _classCallCheck2.default)(this, OAuthCallbackError);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this2 = _callSuper(this, OAuthCallbackError, [].concat(args));\n    (0, _defineProperty2.default)(_this2, \"name\", \"OAuthCallbackError\");\n    return _this2;\n  }\n  (0, _inherits2.default)(OAuthCallbackError, _UnknownError);\n  return (0, _createClass2.default)(OAuthCallbackError);\n}(UnknownError);\nvar AccountNotLinkedError = exports.AccountNotLinkedError = function (_UnknownError2) {\n  function AccountNotLinkedError() {\n    var _this3;\n    (0, _classCallCheck2.default)(this, AccountNotLinkedError);\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    _this3 = _callSuper(this, AccountNotLinkedError, [].concat(args));\n    (0, _defineProperty2.default)(_this3, \"name\", \"AccountNotLinkedError\");\n    return _this3;\n  }\n  (0, _inherits2.default)(AccountNotLinkedError, _UnknownError2);\n  return (0, _createClass2.default)(AccountNotLinkedError);\n}(UnknownError);\nvar MissingAPIRoute = exports.MissingAPIRoute = function (_UnknownError3) {\n  function MissingAPIRoute() {\n    var _this4;\n    (0, _classCallCheck2.default)(this, MissingAPIRoute);\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    _this4 = _callSuper(this, MissingAPIRoute, [].concat(args));\n    (0, _defineProperty2.default)(_this4, \"name\", \"MissingAPIRouteError\");\n    (0, _defineProperty2.default)(_this4, \"code\", \"MISSING_NEXTAUTH_API_ROUTE_ERROR\");\n    return _this4;\n  }\n  (0, _inherits2.default)(MissingAPIRoute, _UnknownError3);\n  return (0, _createClass2.default)(MissingAPIRoute);\n}(UnknownError);\nvar MissingSecret = exports.MissingSecret = function (_UnknownError4) {\n  function MissingSecret() {\n    var _this5;\n    (0, _classCallCheck2.default)(this, MissingSecret);\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    _this5 = _callSuper(this, MissingSecret, [].concat(args));\n    (0, _defineProperty2.default)(_this5, \"name\", \"MissingSecretError\");\n    (0, _defineProperty2.default)(_this5, \"code\", \"NO_SECRET\");\n    return _this5;\n  }\n  (0, _inherits2.default)(MissingSecret, _UnknownError4);\n  return (0, _createClass2.default)(MissingSecret);\n}(UnknownError);\nvar MissingAuthorize = exports.MissingAuthorize = function (_UnknownError5) {\n  function MissingAuthorize() {\n    var _this6;\n    (0, _classCallCheck2.default)(this, MissingAuthorize);\n    for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n      args[_key5] = arguments[_key5];\n    }\n    _this6 = _callSuper(this, MissingAuthorize, [].concat(args));\n    (0, _defineProperty2.default)(_this6, \"name\", \"MissingAuthorizeError\");\n    (0, _defineProperty2.default)(_this6, \"code\", \"CALLBACK_CREDENTIALS_HANDLER_ERROR\");\n    return _this6;\n  }\n  (0, _inherits2.default)(MissingAuthorize, _UnknownError5);\n  return (0, _createClass2.default)(MissingAuthorize);\n}(UnknownError);\nvar MissingAdapter = exports.MissingAdapter = function (_UnknownError6) {\n  function MissingAdapter() {\n    var _this7;\n    (0, _classCallCheck2.default)(this, MissingAdapter);\n    for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n      args[_key6] = arguments[_key6];\n    }\n    _this7 = _callSuper(this, MissingAdapter, [].concat(args));\n    (0, _defineProperty2.default)(_this7, \"name\", \"MissingAdapterError\");\n    (0, _defineProperty2.default)(_this7, \"code\", \"EMAIL_REQUIRES_ADAPTER_ERROR\");\n    return _this7;\n  }\n  (0, _inherits2.default)(MissingAdapter, _UnknownError6);\n  return (0, _createClass2.default)(MissingAdapter);\n}(UnknownError);\nvar MissingAdapterMethods = exports.MissingAdapterMethods = function (_UnknownError7) {\n  function MissingAdapterMethods() {\n    var _this8;\n    (0, _classCallCheck2.default)(this, MissingAdapterMethods);\n    for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n      args[_key7] = arguments[_key7];\n    }\n    _this8 = _callSuper(this, MissingAdapterMethods, [].concat(args));\n    (0, _defineProperty2.default)(_this8, \"name\", \"MissingAdapterMethodsError\");\n    (0, _defineProperty2.default)(_this8, \"code\", \"MISSING_ADAPTER_METHODS_ERROR\");\n    return _this8;\n  }\n  (0, _inherits2.default)(MissingAdapterMethods, _UnknownError7);\n  return (0, _createClass2.default)(MissingAdapterMethods);\n}(UnknownError);\nvar UnsupportedStrategy = exports.UnsupportedStrategy = function (_UnknownError8) {\n  function UnsupportedStrategy() {\n    var _this9;\n    (0, _classCallCheck2.default)(this, UnsupportedStrategy);\n    for (var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {\n      args[_key8] = arguments[_key8];\n    }\n    _this9 = _callSuper(this, UnsupportedStrategy, [].concat(args));\n    (0, _defineProperty2.default)(_this9, \"name\", \"UnsupportedStrategyError\");\n    (0, _defineProperty2.default)(_this9, \"code\", \"CALLBACK_CREDENTIALS_JWT_ERROR\");\n    return _this9;\n  }\n  (0, _inherits2.default)(UnsupportedStrategy, _UnknownError8);\n  return (0, _createClass2.default)(UnsupportedStrategy);\n}(UnknownError);\nvar InvalidCallbackUrl = exports.InvalidCallbackUrl = function (_UnknownError9) {\n  function InvalidCallbackUrl() {\n    var _this10;\n    (0, _classCallCheck2.default)(this, InvalidCallbackUrl);\n    for (var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {\n      args[_key9] = arguments[_key9];\n    }\n    _this10 = _callSuper(this, InvalidCallbackUrl, [].concat(args));\n    (0, _defineProperty2.default)(_this10, \"name\", \"InvalidCallbackUrl\");\n    (0, _defineProperty2.default)(_this10, \"code\", \"INVALID_CALLBACK_URL_ERROR\");\n    return _this10;\n  }\n  (0, _inherits2.default)(InvalidCallbackUrl, _UnknownError9);\n  return (0, _createClass2.default)(InvalidCallbackUrl);\n}(UnknownError);\nfunction upperSnake(s) {\n  return s.replace(/([A-Z])/g, \"_$1\").toUpperCase();\n}\nfunction capitalize(s) {\n  return \"\".concat(s[0].toUpperCase()).concat(s.slice(1));\n}\nfunction eventsErrorHandler(methods, logger) {\n  return Object.keys(methods).reduce(function (acc, name) {\n    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee() {\n      var method,\n        _args = arguments;\n      return _regenerator.default.wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _context.prev = 0;\n            method = methods[name];\n            _context.next = 4;\n            return method.apply(void 0, _args);\n          case 4:\n            return _context.abrupt(\"return\", _context.sent);\n          case 7:\n            _context.prev = 7;\n            _context.t0 = _context[\"catch\"](0);\n            logger.error(\"\".concat(upperSnake(name), \"_EVENT_ERROR\"), _context.t0);\n          case 10:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee, null, [[0, 7]]);\n    }));\n    return acc;\n  }, {});\n}\nfunction adapterErrorHandler(adapter, logger) {\n  if (!adapter) return;\n  return Object.keys(adapter).reduce(function (acc, name) {\n    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee2() {\n      var _len10,\n        args,\n        _key10,\n        method,\n        e,\n        _args2 = arguments;\n      return _regenerator.default.wrap(function _callee2$(_context2) {\n        while (1) switch (_context2.prev = _context2.next) {\n          case 0:\n            _context2.prev = 0;\n            for (_len10 = _args2.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {\n              args[_key10] = _args2[_key10];\n            }\n            logger.debug(\"adapter_\".concat(name), {\n              args: args\n            });\n            method = adapter[name];\n            _context2.next = 6;\n            return method.apply(void 0, args);\n          case 6:\n            return _context2.abrupt(\"return\", _context2.sent);\n          case 9:\n            _context2.prev = 9;\n            _context2.t0 = _context2[\"catch\"](0);\n            logger.error(\"adapter_error_\".concat(name), _context2.t0);\n            e = new UnknownError(_context2.t0);\n            e.name = \"\".concat(capitalize(name), \"Error\");\n            throw e;\n          case 15:\n          case \"end\":\n            return _context2.stop();\n        }\n      }, _callee2, null, [[0, 9]]);\n    }));\n    return acc;\n  }, {});\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/core/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/react/index.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/react/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nvar _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/typeof.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nvar _exportNames = {\n  SessionContext: true,\n  useSession: true,\n  getSession: true,\n  getCsrfToken: true,\n  getProviders: true,\n  signIn: true,\n  signOut: true,\n  SessionProvider: true\n};\nexports.SessionContext = void 0;\nexports.SessionProvider = SessionProvider;\nexports.getCsrfToken = getCsrfToken;\nexports.getProviders = getProviders;\nexports.getSession = getSession;\nexports.signIn = signIn;\nexports.signOut = signOut;\nexports.useSession = useSession;\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(ssr)/./node_modules/@babel/runtime/regenerator/index.js\"));\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(ssr)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nvar _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/slicedToArray.js\"));\nvar React = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\"));\nvar _logger2 = _interopRequireWildcard(__webpack_require__(/*! ../utils/logger */ \"(ssr)/./node_modules/next-auth/utils/logger.js\"));\nvar _parseUrl = _interopRequireDefault(__webpack_require__(/*! ../utils/parse-url */ \"(ssr)/./node_modules/next-auth/utils/parse-url.js\"));\nvar _utils = __webpack_require__(/*! ../client/_utils */ \"(ssr)/./node_modules/next-auth/client/_utils.js\");\nvar _jsxRuntime = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\nvar _types = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/next-auth/react/types.js\");\nObject.keys(_types).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _types[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function get() {\n      return _types[key];\n    }\n  });\n});\nvar _process$env$NEXTAUTH, _ref, _process$env$NEXTAUTH2, _process$env$NEXTAUTH3, _React$createContext;\nfunction _getRequireWildcardCache(e) {\n  if (\"function\" != typeof WeakMap) return null;\n  var r = new WeakMap(),\n    t = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(e) {\n    return e ? t : r;\n  })(e);\n}\nfunction _interopRequireWildcard(e, r) {\n  if (!r && e && e.__esModule) return e;\n  if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return {\n    default: e\n  };\n  var t = _getRequireWildcardCache(r);\n  if (t && t.has(e)) return t.get(e);\n  var n = {\n      __proto__: null\n    },\n    a = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) {\n    var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;\n    i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];\n  }\n  return n.default = e, t && t.set(e, n), n;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      (0, _defineProperty2.default)(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nvar __NEXTAUTH = {\n  baseUrl: (0, _parseUrl.default)((_process$env$NEXTAUTH = process.env.NEXTAUTH_URL) !== null && _process$env$NEXTAUTH !== void 0 ? _process$env$NEXTAUTH : process.env.VERCEL_URL).origin,\n  basePath: (0, _parseUrl.default)(process.env.NEXTAUTH_URL).path,\n  baseUrlServer: (0, _parseUrl.default)((_ref = (_process$env$NEXTAUTH2 = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process$env$NEXTAUTH2 !== void 0 ? _process$env$NEXTAUTH2 : process.env.NEXTAUTH_URL) !== null && _ref !== void 0 ? _ref : process.env.VERCEL_URL).origin,\n  basePathServer: (0, _parseUrl.default)((_process$env$NEXTAUTH3 = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process$env$NEXTAUTH3 !== void 0 ? _process$env$NEXTAUTH3 : process.env.NEXTAUTH_URL).path,\n  _lastSync: 0,\n  _session: undefined,\n  _getSession: function _getSession() {}\n};\nvar broadcast = (0, _utils.BroadcastChannel)();\nvar logger = (0, _logger2.proxyLogger)(_logger2.default, __NEXTAUTH.basePath);\nfunction useOnline() {\n  var _React$useState = React.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false),\n    _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n    isOnline = _React$useState2[0],\n    setIsOnline = _React$useState2[1];\n  var setOnline = function setOnline() {\n    return setIsOnline(true);\n  };\n  var setOffline = function setOffline() {\n    return setIsOnline(false);\n  };\n  React.useEffect(function () {\n    window.addEventListener(\"online\", setOnline);\n    window.addEventListener(\"offline\", setOffline);\n    return function () {\n      window.removeEventListener(\"online\", setOnline);\n      window.removeEventListener(\"offline\", setOffline);\n    };\n  }, []);\n  return isOnline;\n}\nvar SessionContext = exports.SessionContext = (_React$createContext = React.createContext) === null || _React$createContext === void 0 ? void 0 : _React$createContext.call(React, undefined);\nfunction useSession(options) {\n  if (!SessionContext) {\n    throw new Error(\"React Context is unavailable in Server Components\");\n  }\n  var value = React.useContext(SessionContext);\n  if (!value && true) {\n    throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n  }\n  var _ref2 = options !== null && options !== void 0 ? options : {},\n    required = _ref2.required,\n    onUnauthenticated = _ref2.onUnauthenticated;\n  var requiredAndNotLoading = required && value.status === \"unauthenticated\";\n  React.useEffect(function () {\n    if (requiredAndNotLoading) {\n      var url = \"/api/auth/signin?\".concat(new URLSearchParams({\n        error: \"SessionRequired\",\n        callbackUrl: window.location.href\n      }));\n      if (onUnauthenticated) onUnauthenticated();else window.location.href = url;\n    }\n  }, [requiredAndNotLoading, onUnauthenticated]);\n  if (requiredAndNotLoading) {\n    return {\n      data: value.data,\n      update: value.update,\n      status: \"loading\"\n    };\n  }\n  return value;\n}\nfunction getSession(_x) {\n  return _getSession2.apply(this, arguments);\n}\nfunction _getSession2() {\n  _getSession2 = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee3(params) {\n    var _params$broadcast;\n    var session;\n    return _regenerator.default.wrap(function _callee3$(_context3) {\n      while (1) switch (_context3.prev = _context3.next) {\n        case 0:\n          _context3.next = 2;\n          return (0, _utils.fetchData)(\"session\", __NEXTAUTH, logger, params);\n        case 2:\n          session = _context3.sent;\n          if ((_params$broadcast = params === null || params === void 0 ? void 0 : params.broadcast) !== null && _params$broadcast !== void 0 ? _params$broadcast : true) {\n            broadcast.post({\n              event: \"session\",\n              data: {\n                trigger: \"getSession\"\n              }\n            });\n          }\n          return _context3.abrupt(\"return\", session);\n        case 5:\n        case \"end\":\n          return _context3.stop();\n      }\n    }, _callee3);\n  }));\n  return _getSession2.apply(this, arguments);\n}\nfunction getCsrfToken(_x2) {\n  return _getCsrfToken.apply(this, arguments);\n}\nfunction _getCsrfToken() {\n  _getCsrfToken = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee4(params) {\n    var response;\n    return _regenerator.default.wrap(function _callee4$(_context4) {\n      while (1) switch (_context4.prev = _context4.next) {\n        case 0:\n          _context4.next = 2;\n          return (0, _utils.fetchData)(\"csrf\", __NEXTAUTH, logger, params);\n        case 2:\n          response = _context4.sent;\n          return _context4.abrupt(\"return\", response === null || response === void 0 ? void 0 : response.csrfToken);\n        case 4:\n        case \"end\":\n          return _context4.stop();\n      }\n    }, _callee4);\n  }));\n  return _getCsrfToken.apply(this, arguments);\n}\nfunction getProviders() {\n  return _getProviders.apply(this, arguments);\n}\nfunction _getProviders() {\n  _getProviders = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee5() {\n    return _regenerator.default.wrap(function _callee5$(_context5) {\n      while (1) switch (_context5.prev = _context5.next) {\n        case 0:\n          _context5.next = 2;\n          return (0, _utils.fetchData)(\"providers\", __NEXTAUTH, logger);\n        case 2:\n          return _context5.abrupt(\"return\", _context5.sent);\n        case 3:\n        case \"end\":\n          return _context5.stop();\n      }\n    }, _callee5);\n  }));\n  return _getProviders.apply(this, arguments);\n}\nfunction signIn(_x3, _x4, _x5) {\n  return _signIn.apply(this, arguments);\n}\nfunction _signIn() {\n  _signIn = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee6(provider, options, authorizationParams) {\n    var _ref5, _ref5$callbackUrl, callbackUrl, _ref5$redirect, redirect, baseUrl, providers, isCredentials, isEmail, isSupportingReturn, signInUrl, _signInUrl, res, data, _data$url, url, error;\n    return _regenerator.default.wrap(function _callee6$(_context6) {\n      while (1) switch (_context6.prev = _context6.next) {\n        case 0:\n          _ref5 = options !== null && options !== void 0 ? options : {}, _ref5$callbackUrl = _ref5.callbackUrl, callbackUrl = _ref5$callbackUrl === void 0 ? window.location.href : _ref5$callbackUrl, _ref5$redirect = _ref5.redirect, redirect = _ref5$redirect === void 0 ? true : _ref5$redirect;\n          baseUrl = (0, _utils.apiBaseUrl)(__NEXTAUTH);\n          _context6.next = 4;\n          return getProviders();\n        case 4:\n          providers = _context6.sent;\n          if (providers) {\n            _context6.next = 8;\n            break;\n          }\n          window.location.href = \"\".concat(baseUrl, \"/error\");\n          return _context6.abrupt(\"return\");\n        case 8:\n          if (!(!provider || !(provider in providers))) {\n            _context6.next = 11;\n            break;\n          }\n          window.location.href = \"\".concat(baseUrl, \"/signin?\").concat(new URLSearchParams({\n            callbackUrl: callbackUrl\n          }));\n          return _context6.abrupt(\"return\");\n        case 11:\n          isCredentials = providers[provider].type === \"credentials\";\n          isEmail = providers[provider].type === \"email\";\n          isSupportingReturn = isCredentials || isEmail;\n          signInUrl = \"\".concat(baseUrl, \"/\").concat(isCredentials ? \"callback\" : \"signin\", \"/\").concat(provider);\n          _signInUrl = \"\".concat(signInUrl).concat(authorizationParams ? \"?\".concat(new URLSearchParams(authorizationParams)) : \"\");\n          _context6.t0 = fetch;\n          _context6.t1 = _signInUrl;\n          _context6.t2 = {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n          };\n          _context6.t3 = URLSearchParams;\n          _context6.t4 = _objectSpread;\n          _context6.t5 = _objectSpread({}, options);\n          _context6.t6 = {};\n          _context6.next = 25;\n          return getCsrfToken();\n        case 25:\n          _context6.t7 = _context6.sent;\n          _context6.t8 = callbackUrl;\n          _context6.t9 = {\n            csrfToken: _context6.t7,\n            callbackUrl: _context6.t8,\n            json: true\n          };\n          _context6.t10 = (0, _context6.t4)(_context6.t5, _context6.t6, _context6.t9);\n          _context6.t11 = new _context6.t3(_context6.t10);\n          _context6.t12 = {\n            method: \"post\",\n            headers: _context6.t2,\n            body: _context6.t11\n          };\n          _context6.next = 33;\n          return (0, _context6.t0)(_context6.t1, _context6.t12);\n        case 33:\n          res = _context6.sent;\n          _context6.next = 36;\n          return res.json();\n        case 36:\n          data = _context6.sent;\n          if (!(redirect || !isSupportingReturn)) {\n            _context6.next = 42;\n            break;\n          }\n          url = (_data$url = data.url) !== null && _data$url !== void 0 ? _data$url : callbackUrl;\n          window.location.href = url;\n          if (url.includes(\"#\")) window.location.reload();\n          return _context6.abrupt(\"return\");\n        case 42:\n          error = new URL(data.url).searchParams.get(\"error\");\n          if (!res.ok) {\n            _context6.next = 46;\n            break;\n          }\n          _context6.next = 46;\n          return __NEXTAUTH._getSession({\n            event: \"storage\"\n          });\n        case 46:\n          return _context6.abrupt(\"return\", {\n            error: error,\n            status: res.status,\n            ok: res.ok,\n            url: error ? null : data.url\n          });\n        case 47:\n        case \"end\":\n          return _context6.stop();\n      }\n    }, _callee6);\n  }));\n  return _signIn.apply(this, arguments);\n}\nfunction signOut(_x6) {\n  return _signOut.apply(this, arguments);\n}\nfunction _signOut() {\n  _signOut = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee7(options) {\n    var _options$redirect;\n    var _ref6, _ref6$callbackUrl, callbackUrl, baseUrl, fetchOptions, res, data, _data$url2, url;\n    return _regenerator.default.wrap(function _callee7$(_context7) {\n      while (1) switch (_context7.prev = _context7.next) {\n        case 0:\n          _ref6 = options !== null && options !== void 0 ? options : {}, _ref6$callbackUrl = _ref6.callbackUrl, callbackUrl = _ref6$callbackUrl === void 0 ? window.location.href : _ref6$callbackUrl;\n          baseUrl = (0, _utils.apiBaseUrl)(__NEXTAUTH);\n          _context7.t0 = {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n          };\n          _context7.t1 = URLSearchParams;\n          _context7.next = 6;\n          return getCsrfToken();\n        case 6:\n          _context7.t2 = _context7.sent;\n          _context7.t3 = callbackUrl;\n          _context7.t4 = {\n            csrfToken: _context7.t2,\n            callbackUrl: _context7.t3,\n            json: true\n          };\n          _context7.t5 = new _context7.t1(_context7.t4);\n          fetchOptions = {\n            method: \"post\",\n            headers: _context7.t0,\n            body: _context7.t5\n          };\n          _context7.next = 13;\n          return fetch(\"\".concat(baseUrl, \"/signout\"), fetchOptions);\n        case 13:\n          res = _context7.sent;\n          _context7.next = 16;\n          return res.json();\n        case 16:\n          data = _context7.sent;\n          broadcast.post({\n            event: \"session\",\n            data: {\n              trigger: \"signout\"\n            }\n          });\n          if (!((_options$redirect = options === null || options === void 0 ? void 0 : options.redirect) !== null && _options$redirect !== void 0 ? _options$redirect : true)) {\n            _context7.next = 23;\n            break;\n          }\n          url = (_data$url2 = data.url) !== null && _data$url2 !== void 0 ? _data$url2 : callbackUrl;\n          window.location.href = url;\n          if (url.includes(\"#\")) window.location.reload();\n          return _context7.abrupt(\"return\");\n        case 23:\n          _context7.next = 25;\n          return __NEXTAUTH._getSession({\n            event: \"storage\"\n          });\n        case 25:\n          return _context7.abrupt(\"return\", data);\n        case 26:\n        case \"end\":\n          return _context7.stop();\n      }\n    }, _callee7);\n  }));\n  return _signOut.apply(this, arguments);\n}\nfunction SessionProvider(props) {\n  if (!SessionContext) {\n    throw new Error(\"React Context is unavailable in Server Components\");\n  }\n  var children = props.children,\n    basePath = props.basePath,\n    refetchInterval = props.refetchInterval,\n    refetchWhenOffline = props.refetchWhenOffline;\n  if (basePath) __NEXTAUTH.basePath = basePath;\n  var hasInitialSession = props.session !== undefined;\n  __NEXTAUTH._lastSync = hasInitialSession ? (0, _utils.now)() : 0;\n  var _React$useState3 = React.useState(function () {\n      if (hasInitialSession) __NEXTAUTH._session = props.session;\n      return props.session;\n    }),\n    _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),\n    session = _React$useState4[0],\n    setSession = _React$useState4[1];\n  var _React$useState5 = React.useState(!hasInitialSession),\n    _React$useState6 = (0, _slicedToArray2.default)(_React$useState5, 2),\n    loading = _React$useState6[0],\n    setLoading = _React$useState6[1];\n  React.useEffect(function () {\n    __NEXTAUTH._getSession = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee() {\n      var _ref4,\n        event,\n        storageEvent,\n        _args = arguments;\n      return _regenerator.default.wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _ref4 = _args.length > 0 && _args[0] !== undefined ? _args[0] : {}, event = _ref4.event;\n            _context.prev = 1;\n            storageEvent = event === \"storage\";\n            if (!(storageEvent || __NEXTAUTH._session === undefined)) {\n              _context.next = 10;\n              break;\n            }\n            __NEXTAUTH._lastSync = (0, _utils.now)();\n            _context.next = 7;\n            return getSession({\n              broadcast: !storageEvent\n            });\n          case 7:\n            __NEXTAUTH._session = _context.sent;\n            setSession(__NEXTAUTH._session);\n            return _context.abrupt(\"return\");\n          case 10:\n            if (!(!event || __NEXTAUTH._session === null || (0, _utils.now)() < __NEXTAUTH._lastSync)) {\n              _context.next = 12;\n              break;\n            }\n            return _context.abrupt(\"return\");\n          case 12:\n            __NEXTAUTH._lastSync = (0, _utils.now)();\n            _context.next = 15;\n            return getSession();\n          case 15:\n            __NEXTAUTH._session = _context.sent;\n            setSession(__NEXTAUTH._session);\n            _context.next = 22;\n            break;\n          case 19:\n            _context.prev = 19;\n            _context.t0 = _context[\"catch\"](1);\n            logger.error(\"CLIENT_SESSION_ERROR\", _context.t0);\n          case 22:\n            _context.prev = 22;\n            setLoading(false);\n            return _context.finish(22);\n          case 25:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee, null, [[1, 19, 22, 25]]);\n    }));\n    __NEXTAUTH._getSession();\n    return function () {\n      __NEXTAUTH._lastSync = 0;\n      __NEXTAUTH._session = undefined;\n      __NEXTAUTH._getSession = function () {};\n    };\n  }, []);\n  React.useEffect(function () {\n    var unsubscribe = broadcast.receive(function () {\n      return __NEXTAUTH._getSession({\n        event: \"storage\"\n      });\n    });\n    return function () {\n      return unsubscribe();\n    };\n  }, []);\n  React.useEffect(function () {\n    var _props$refetchOnWindo = props.refetchOnWindowFocus,\n      refetchOnWindowFocus = _props$refetchOnWindo === void 0 ? true : _props$refetchOnWindo;\n    var visibilityHandler = function visibilityHandler() {\n      if (refetchOnWindowFocus && document.visibilityState === \"visible\") __NEXTAUTH._getSession({\n        event: \"visibilitychange\"\n      });\n    };\n    document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n    return function () {\n      return document.removeEventListener(\"visibilitychange\", visibilityHandler, false);\n    };\n  }, [props.refetchOnWindowFocus]);\n  var isOnline = useOnline();\n  var shouldRefetch = refetchWhenOffline !== false || isOnline;\n  React.useEffect(function () {\n    if (refetchInterval && shouldRefetch) {\n      var refetchIntervalTimer = setInterval(function () {\n        if (__NEXTAUTH._session) {\n          __NEXTAUTH._getSession({\n            event: \"poll\"\n          });\n        }\n      }, refetchInterval * 1000);\n      return function () {\n        return clearInterval(refetchIntervalTimer);\n      };\n    }\n  }, [refetchInterval, shouldRefetch]);\n  var value = React.useMemo(function () {\n    return {\n      data: session,\n      status: loading ? \"loading\" : session ? \"authenticated\" : \"unauthenticated\",\n      update: function update(data) {\n        return (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee2() {\n          var newSession;\n          return _regenerator.default.wrap(function _callee2$(_context2) {\n            while (1) switch (_context2.prev = _context2.next) {\n              case 0:\n                if (!(loading || !session)) {\n                  _context2.next = 2;\n                  break;\n                }\n                return _context2.abrupt(\"return\");\n              case 2:\n                setLoading(true);\n                _context2.t0 = _utils.fetchData;\n                _context2.t1 = __NEXTAUTH;\n                _context2.t2 = logger;\n                _context2.next = 8;\n                return getCsrfToken();\n              case 8:\n                _context2.t3 = _context2.sent;\n                _context2.t4 = data;\n                _context2.t5 = {\n                  csrfToken: _context2.t3,\n                  data: _context2.t4\n                };\n                _context2.t6 = {\n                  body: _context2.t5\n                };\n                _context2.t7 = {\n                  req: _context2.t6\n                };\n                _context2.next = 15;\n                return (0, _context2.t0)(\"session\", _context2.t1, _context2.t2, _context2.t7);\n              case 15:\n                newSession = _context2.sent;\n                setLoading(false);\n                if (newSession) {\n                  setSession(newSession);\n                  broadcast.post({\n                    event: \"session\",\n                    data: {\n                      trigger: \"getSession\"\n                    }\n                  });\n                }\n                return _context2.abrupt(\"return\", newSession);\n              case 19:\n              case \"end\":\n                return _context2.stop();\n            }\n          }, _callee2);\n        }))();\n      }\n    };\n  }, [session, loading]);\n  return (0, _jsxRuntime.jsx)(SessionContext.Provider, {\n    value: value,\n    children: children\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/react/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/react/types.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/react/types.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3JlYWN0L3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViQSw4Q0FBNkM7RUFDM0NHLEtBQUssRUFBRTtBQUNULENBQUMsRUFBQyIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXG5leHQtYXV0aFxccmVhY3RcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pOyJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/react/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/utils/logger.js":
/*!************************************************!*\
  !*** ./node_modules/next-auth/utils/logger.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nexports.proxyLogger = proxyLogger;\nexports.setLogger = setLogger;\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(ssr)/./node_modules/@babel/runtime/regenerator/index.js\"));\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(ssr)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nvar _errors = __webpack_require__(/*! ../core/errors */ \"(ssr)/./node_modules/next-auth/core/errors.js\");\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      (0, _defineProperty2.default)(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction formatError(o) {\n  if (o instanceof Error && !(o instanceof _errors.UnknownError)) {\n    return {\n      message: o.message,\n      stack: o.stack,\n      name: o.name\n    };\n  }\n  if (hasErrorProperty(o)) {\n    var _o$message;\n    o.error = formatError(o.error);\n    o.message = (_o$message = o.message) !== null && _o$message !== void 0 ? _o$message : o.error.message;\n  }\n  return o;\n}\nfunction hasErrorProperty(x) {\n  return !!(x !== null && x !== void 0 && x.error);\n}\nvar _logger = {\n  error: function error(code, metadata) {\n    metadata = formatError(metadata);\n    console.error(\"[next-auth][error][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/errors#\".concat(code.toLowerCase()), metadata.message, metadata);\n  },\n  warn: function warn(code) {\n    console.warn(\"[next-auth][warn][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/warnings#\".concat(code.toLowerCase()));\n  },\n  debug: function debug(code, metadata) {\n    console.log(\"[next-auth][debug][\".concat(code, \"]\"), metadata);\n  }\n};\nfunction setLogger() {\n  var newLogger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var debug = arguments.length > 1 ? arguments[1] : undefined;\n  if (!debug) _logger.debug = function () {};\n  if (newLogger.error) _logger.error = newLogger.error;\n  if (newLogger.warn) _logger.warn = newLogger.warn;\n  if (newLogger.debug) _logger.debug = newLogger.debug;\n}\nvar _default = exports[\"default\"] = _logger;\nfunction proxyLogger() {\n  var logger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _logger;\n  var basePath = arguments.length > 1 ? arguments[1] : undefined;\n  try {\n    if (true) {\n      return logger;\n    }\n    var clientLogger = {};\n    var _loop = function _loop(level) {\n      clientLogger[level] = function () {\n        var _ref = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee(code, metadata) {\n          var url, body;\n          return _regenerator.default.wrap(function _callee$(_context) {\n            while (1) switch (_context.prev = _context.next) {\n              case 0:\n                _logger[level](code, metadata);\n                if (level === \"error\") {\n                  metadata = formatError(metadata);\n                }\n                ;\n                metadata.client = true;\n                url = \"\".concat(basePath, \"/_log\");\n                body = new URLSearchParams(_objectSpread({\n                  level: level,\n                  code: code\n                }, metadata));\n                if (!navigator.sendBeacon) {\n                  _context.next = 8;\n                  break;\n                }\n                return _context.abrupt(\"return\", navigator.sendBeacon(url, body));\n              case 8:\n                _context.next = 10;\n                return fetch(url, {\n                  method: \"POST\",\n                  body: body,\n                  keepalive: true\n                });\n              case 10:\n                return _context.abrupt(\"return\", _context.sent);\n              case 11:\n              case \"end\":\n                return _context.stop();\n            }\n          }, _callee);\n        }));\n        return function (_x, _x2) {\n          return _ref.apply(this, arguments);\n        };\n      }();\n    };\n    for (var level in logger) {\n      _loop(level);\n    }\n    return clientLogger;\n  } catch (_unused) {\n    return _logger;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/utils/logger.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/utils/parse-url.js":
/*!***************************************************!*\
  !*** ./node_modules/next-auth/utils/parse-url.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = parseUrl;\nfunction parseUrl(url) {\n  var _url2;\n  const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n  if (url && !url.startsWith(\"http\")) {\n    url = `https://${url}`;\n  }\n  const _url = new URL((_url2 = url) !== null && _url2 !== void 0 ? _url2 : defaultUrl);\n  const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname).replace(/\\/$/, \"\");\n  const base = `${_url.origin}${path}`;\n  return {\n    origin: _url.origin,\n    host: _url.host,\n    path,\n    base,\n    toString: () => base\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3V0aWxzL3BhcnNlLXVybC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYkEsOENBQTZDO0VBQzNDRyxLQUFLLEVBQUU7QUFDVCxDQUFDLEVBQUM7QUFDRkQsa0JBQWUsR0FBR0csUUFBUTtBQUMxQixTQUFTQSxRQUFRQSxDQUFDQyxHQUFHLEVBQUU7RUFDckIsSUFBSUMsS0FBSztFQUNULE1BQU1DLFVBQVUsR0FBRyxJQUFJQyxHQUFHLENBQUMsZ0NBQWdDLENBQUM7RUFDNUQsSUFBSUgsR0FBRyxJQUFJLENBQUNBLEdBQUcsQ0FBQ0ksVUFBVSxDQUFDLE1BQU0sQ0FBQyxFQUFFO0lBQ2xDSixHQUFHLEdBQUksV0FBVUEsR0FBSSxFQUFDO0VBQ3hCO0VBQ0EsTUFBTUssSUFBSSxHQUFHLElBQUlGLEdBQUcsQ0FBQyxDQUFDRixLQUFLLEdBQUdELEdBQUcsTUFBTSxJQUFJLElBQUlDLEtBQUssS0FBSyxLQUFLLENBQUMsR0FBR0EsS0FBSyxHQUFHQyxVQUFVLENBQUM7RUFDckYsTUFBTUksSUFBSSxHQUFHLENBQUNELElBQUksQ0FBQ0UsUUFBUSxLQUFLLEdBQUcsR0FBR0wsVUFBVSxDQUFDSyxRQUFRLEdBQUdGLElBQUksQ0FBQ0UsUUFBUSxFQUFFQyxPQUFPLENBQUMsS0FBSyxFQUFFLEVBQUUsQ0FBQztFQUM3RixNQUFNQyxJQUFJLEdBQUksR0FBRUosSUFBSSxDQUFDSyxNQUFPLEdBQUVKLElBQUssRUFBQztFQUNwQyxPQUFPO0lBQ0xJLE1BQU0sRUFBRUwsSUFBSSxDQUFDSyxNQUFNO0lBQ25CQyxJQUFJLEVBQUVOLElBQUksQ0FBQ00sSUFBSTtJQUNmTCxJQUFJO0lBQ0pHLElBQUk7SUFDSkcsUUFBUSxFQUFFQSxDQUFBLEtBQU1IO0VBQ2xCLENBQUM7QUFDSCIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXG5leHQtYXV0aFxcdXRpbHNcXHBhcnNlLXVybC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuZGVmYXVsdCA9IHBhcnNlVXJsO1xuZnVuY3Rpb24gcGFyc2VVcmwodXJsKSB7XG4gIHZhciBfdXJsMjtcbiAgY29uc3QgZGVmYXVsdFVybCA9IG5ldyBVUkwoXCJodHRwOi8vbG9jYWxob3N0OjMwMDAvYXBpL2F1dGhcIik7XG4gIGlmICh1cmwgJiYgIXVybC5zdGFydHNXaXRoKFwiaHR0cFwiKSkge1xuICAgIHVybCA9IGBodHRwczovLyR7dXJsfWA7XG4gIH1cbiAgY29uc3QgX3VybCA9IG5ldyBVUkwoKF91cmwyID0gdXJsKSAhPT0gbnVsbCAmJiBfdXJsMiAhPT0gdm9pZCAwID8gX3VybDIgOiBkZWZhdWx0VXJsKTtcbiAgY29uc3QgcGF0aCA9IChfdXJsLnBhdGhuYW1lID09PSBcIi9cIiA/IGRlZmF1bHRVcmwucGF0aG5hbWUgOiBfdXJsLnBhdGhuYW1lKS5yZXBsYWNlKC9cXC8kLywgXCJcIik7XG4gIGNvbnN0IGJhc2UgPSBgJHtfdXJsLm9yaWdpbn0ke3BhdGh9YDtcbiAgcmV0dXJuIHtcbiAgICBvcmlnaW46IF91cmwub3JpZ2luLFxuICAgIGhvc3Q6IF91cmwuaG9zdCxcbiAgICBwYXRoLFxuICAgIGJhc2UsXG4gICAgdG9TdHJpbmc6ICgpID0+IGJhc2VcbiAgfTtcbn0iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJkZWZhdWx0IiwicGFyc2VVcmwiLCJ1cmwiLCJfdXJsMiIsImRlZmF1bHRVcmwiLCJVUkwiLCJzdGFydHNXaXRoIiwiX3VybCIsInBhdGgiLCJwYXRobmFtZSIsInJlcGxhY2UiLCJiYXNlIiwib3JpZ2luIiwiaG9zdCIsInRvU3RyaW5nIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/utils/parse-url.js\n");

/***/ })

};
;