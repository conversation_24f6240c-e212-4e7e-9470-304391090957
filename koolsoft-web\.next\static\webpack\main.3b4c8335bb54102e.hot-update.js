"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/router.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/router.js ***!
  \************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// tslint:disable:no-console\n\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  createKey: function () {\n    return createKey;\n  },\n  default: function () {\n    return Router;\n  },\n  matchesMiddleware: function () {\n    return matchesMiddleware;\n  }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _removetrailingslash = __webpack_require__(/*! ./utils/remove-trailing-slash */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/remove-trailing-slash.js\");\nconst _routeloader = __webpack_require__(/*! ../../../client/route-loader */ \"(pages-dir-browser)/./node_modules/next/dist/client/route-loader.js\");\nconst _script = __webpack_require__(/*! ../../../client/script */ \"(pages-dir-browser)/./node_modules/next/dist/client/script.js\");\nconst _iserror = /*#__PURE__*/_interop_require_wildcard._(__webpack_require__(/*! ../../../lib/is-error */ \"(pages-dir-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _denormalizepagepath = __webpack_require__(/*! ../page-path/denormalize-page-path */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js\");\nconst _normalizelocalepath = __webpack_require__(/*! ../i18n/normalize-locale-path */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/i18n/normalize-locale-path.js\");\nconst _mitt = /*#__PURE__*/_interop_require_default._(__webpack_require__(/*! ../mitt */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/mitt.js\"));\nconst _utils = __webpack_require__(/*! ../utils */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _isdynamic = __webpack_require__(/*! ./utils/is-dynamic */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\");\nconst _parserelativeurl = __webpack_require__(/*! ./utils/parse-relative-url */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/parse-relative-url.js\");\nconst _resolverewrites = /*#__PURE__*/_interop_require_default._(__webpack_require__(/*! ./utils/resolve-rewrites */ \"?506d\"));\nconst _routematcher = __webpack_require__(/*! ./utils/route-matcher */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/route-matcher.js\");\nconst _routeregex = __webpack_require__(/*! ./utils/route-regex */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/route-regex.js\");\nconst _formaturl = __webpack_require__(/*! ./utils/format-url */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _detectdomainlocale = __webpack_require__(/*! ../../../client/detect-domain-locale */ \"(pages-dir-browser)/./node_modules/next/dist/client/detect-domain-locale.js\");\nconst _parsepath = __webpack_require__(/*! ./utils/parse-path */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/parse-path.js\");\nconst _addlocale = __webpack_require__(/*! ../../../client/add-locale */ \"(pages-dir-browser)/./node_modules/next/dist/client/add-locale.js\");\nconst _removelocale = __webpack_require__(/*! ../../../client/remove-locale */ \"(pages-dir-browser)/./node_modules/next/dist/client/remove-locale.js\");\nconst _removebasepath = __webpack_require__(/*! ../../../client/remove-base-path */ \"(pages-dir-browser)/./node_modules/next/dist/client/remove-base-path.js\");\nconst _addbasepath = __webpack_require__(/*! ../../../client/add-base-path */ \"(pages-dir-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../client/has-base-path */ \"(pages-dir-browser)/./node_modules/next/dist/client/has-base-path.js\");\nconst _resolvehref = __webpack_require__(/*! ../../../client/resolve-href */ \"(pages-dir-browser)/./node_modules/next/dist/client/resolve-href.js\");\nconst _isapiroute = __webpack_require__(/*! ../../../lib/is-api-route */ \"(pages-dir-browser)/./node_modules/next/dist/lib/is-api-route.js\");\nconst _getnextpathnameinfo = __webpack_require__(/*! ./utils/get-next-pathname-info */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/get-next-pathname-info.js\");\nconst _formatnextpathnameinfo = __webpack_require__(/*! ./utils/format-next-pathname-info */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/format-next-pathname-info.js\");\nconst _comparestates = __webpack_require__(/*! ./utils/compare-states */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/compare-states.js\");\nconst _islocalurl = __webpack_require__(/*! ./utils/is-local-url */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _isbot = __webpack_require__(/*! ./utils/is-bot */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _omit = __webpack_require__(/*! ./utils/omit */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/omit.js\");\nconst _interpolateas = __webpack_require__(/*! ./utils/interpolate-as */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/interpolate-as.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ./utils/handle-smooth-scroll */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nconst _constants = __webpack_require__(/*! ../../../lib/constants */ \"(pages-dir-browser)/./node_modules/next/dist/lib/constants.js\");\nfunction buildCancellationError() {\n  return Object.assign(Object.defineProperty(new Error('Route Cancelled'), \"__NEXT_ERROR_CODE\", {\n    value: \"E315\",\n    enumerable: false,\n    configurable: true\n  }), {\n    cancelled: true\n  });\n}\nasync function matchesMiddleware(options) {\n  const matchers = await Promise.resolve(options.router.pageLoader.getMiddleware());\n  if (!matchers) return false;\n  const {\n    pathname: asPathname\n  } = (0, _parsepath.parsePath)(options.asPath);\n  // remove basePath first since path prefix has to be in the order of `/${basePath}/${locale}`\n  const cleanedAs = (0, _hasbasepath.hasBasePath)(asPathname) ? (0, _removebasepath.removeBasePath)(asPathname) : asPathname;\n  const asWithBasePathAndLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(cleanedAs, options.locale));\n  // Check only path match on client. Matching \"has\" should be done on server\n  // where we can access more info such as headers, HttpOnly cookie, etc.\n  return matchers.some(m => new RegExp(m.regexp).test(asWithBasePathAndLocale));\n}\nfunction stripOrigin(url) {\n  const origin = (0, _utils.getLocationOrigin)();\n  return url.startsWith(origin) ? url.substring(origin.length) : url;\n}\nfunction prepareUrlAs(router, url, as) {\n  // If url and as provided as an object representation,\n  // we'll format them into the string version here.\n  let [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(router, url, true);\n  const origin = (0, _utils.getLocationOrigin)();\n  const hrefWasAbsolute = resolvedHref.startsWith(origin);\n  const asWasAbsolute = resolvedAs && resolvedAs.startsWith(origin);\n  resolvedHref = stripOrigin(resolvedHref);\n  resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs;\n  const preparedUrl = hrefWasAbsolute ? resolvedHref : (0, _addbasepath.addBasePath)(resolvedHref);\n  const preparedAs = as ? stripOrigin((0, _resolvehref.resolveHref)(router, as)) : resolvedAs || resolvedHref;\n  return {\n    url: preparedUrl,\n    as: asWasAbsolute ? preparedAs : (0, _addbasepath.addBasePath)(preparedAs)\n  };\n}\nfunction resolveDynamicRoute(pathname, pages) {\n  const cleanPathname = (0, _removetrailingslash.removeTrailingSlash)((0, _denormalizepagepath.denormalizePagePath)(pathname));\n  if (cleanPathname === '/404' || cleanPathname === '/_error') {\n    return pathname;\n  }\n  // handle resolving href for dynamic routes\n  if (!pages.includes(cleanPathname)) {\n    // eslint-disable-next-line array-callback-return\n    pages.some(page => {\n      if ((0, _isdynamic.isDynamicRoute)(page) && (0, _routeregex.getRouteRegex)(page).re.test(cleanPathname)) {\n        pathname = page;\n        return true;\n      }\n    });\n  }\n  return (0, _removetrailingslash.removeTrailingSlash)(pathname);\n}\nfunction getMiddlewareData(source, response, options) {\n  const nextConfig = {\n    basePath: options.router.basePath,\n    i18n: {\n      locales: options.router.locales\n    },\n    trailingSlash: Boolean(false)\n  };\n  const rewriteHeader = response.headers.get('x-nextjs-rewrite');\n  let rewriteTarget = rewriteHeader || response.headers.get('x-nextjs-matched-path');\n  const matchedPath = response.headers.get(_constants.MATCHED_PATH_HEADER);\n  if (matchedPath && !rewriteTarget && !matchedPath.includes('__next_data_catchall') && !matchedPath.includes('/_error') && !matchedPath.includes('/404')) {\n    // leverage x-matched-path to detect next.config.js rewrites\n    rewriteTarget = matchedPath;\n  }\n  if (rewriteTarget) {\n    if (rewriteTarget.startsWith('/') || false) {\n      const parsedRewriteTarget = (0, _parserelativeurl.parseRelativeUrl)(rewriteTarget);\n      const pathnameInfo = (0, _getnextpathnameinfo.getNextPathnameInfo)(parsedRewriteTarget.pathname, {\n        nextConfig,\n        parseData: true\n      });\n      let fsPathname = (0, _removetrailingslash.removeTrailingSlash)(pathnameInfo.pathname);\n      return Promise.all([options.router.pageLoader.getPageList(), (0, _routeloader.getClientBuildManifest)()]).then(param => {\n        let [pages, {\n          __rewrites: rewrites\n        }] = param;\n        let as = (0, _addlocale.addLocale)(pathnameInfo.pathname, pathnameInfo.locale);\n        if ((0, _isdynamic.isDynamicRoute)(as) || !rewriteHeader && pages.includes((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(as), options.router.locales).pathname)) {\n          const parsedSource = (0, _getnextpathnameinfo.getNextPathnameInfo)((0, _parserelativeurl.parseRelativeUrl)(source).pathname, {\n            nextConfig:  false ? 0 : nextConfig,\n            parseData: true\n          });\n          as = (0, _addbasepath.addBasePath)(parsedSource.pathname);\n          parsedRewriteTarget.pathname = as;\n        }\n        if (false) {} else if (!pages.includes(fsPathname)) {\n          const resolvedPathname = resolveDynamicRoute(fsPathname, pages);\n          if (resolvedPathname !== fsPathname) {\n            fsPathname = resolvedPathname;\n          }\n        }\n        const resolvedHref = !pages.includes(fsPathname) ? resolveDynamicRoute((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(parsedRewriteTarget.pathname), options.router.locales).pathname, pages) : fsPathname;\n        if ((0, _isdynamic.isDynamicRoute)(resolvedHref)) {\n          const matches = (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(resolvedHref))(as);\n          Object.assign(parsedRewriteTarget.query, matches || {});\n        }\n        return {\n          type: 'rewrite',\n          parsedAs: parsedRewriteTarget,\n          resolvedHref\n        };\n      });\n    }\n    const src = (0, _parsepath.parsePath)(source);\n    const pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)({\n      ...(0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n        nextConfig,\n        parseData: true\n      }),\n      defaultLocale: options.router.defaultLocale,\n      buildId: ''\n    });\n    return Promise.resolve({\n      type: 'redirect-external',\n      destination: \"\" + pathname + src.query + src.hash\n    });\n  }\n  const redirectTarget = response.headers.get('x-nextjs-redirect');\n  if (redirectTarget) {\n    if (redirectTarget.startsWith('/')) {\n      const src = (0, _parsepath.parsePath)(redirectTarget);\n      const pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)({\n        ...(0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n          nextConfig,\n          parseData: true\n        }),\n        defaultLocale: options.router.defaultLocale,\n        buildId: ''\n      });\n      return Promise.resolve({\n        type: 'redirect-internal',\n        newAs: \"\" + pathname + src.query + src.hash,\n        newUrl: \"\" + pathname + src.query + src.hash\n      });\n    }\n    return Promise.resolve({\n      type: 'redirect-external',\n      destination: redirectTarget\n    });\n  }\n  return Promise.resolve({\n    type: 'next'\n  });\n}\nasync function withMiddlewareEffects(options) {\n  const matches = await matchesMiddleware(options);\n  if (!matches || !options.fetchData) {\n    return null;\n  }\n  const data = await options.fetchData();\n  const effect = await getMiddlewareData(data.dataHref, data.response, options);\n  return {\n    dataHref: data.dataHref,\n    json: data.json,\n    response: data.response,\n    text: data.text,\n    cacheKey: data.cacheKey,\n    effect\n  };\n}\nconst manualScrollRestoration =  false && 0;\nconst SSG_DATA_NOT_FOUND = Symbol('SSG_DATA_NOT_FOUND');\nfunction fetchRetry(url, attempts, options) {\n  return fetch(url, {\n    // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n    // Cookies may also be required for `getServerSideProps`.\n    //\n    // > `fetch` won’t send cookies, unless you set the credentials init\n    // > option.\n    // https://developer.mozilla.org/docs/Web/API/Fetch_API/Using_Fetch\n    //\n    // > For maximum browser compatibility when it comes to sending &\n    // > receiving cookies, always supply the `credentials: 'same-origin'`\n    // > option instead of relying on the default.\n    // https://github.com/github/fetch#caveats\n    credentials: 'same-origin',\n    method: options.method || 'GET',\n    headers: Object.assign({}, options.headers, {\n      'x-nextjs-data': '1'\n    })\n  }).then(response => {\n    return !response.ok && attempts > 1 && response.status >= 500 ? fetchRetry(url, attempts - 1, options) : response;\n  });\n}\nfunction tryToParseAsJSON(text) {\n  try {\n    return JSON.parse(text);\n  } catch (error) {\n    return null;\n  }\n}\nfunction fetchNextData(param) {\n  let {\n    dataHref,\n    inflightCache,\n    isPrefetch,\n    hasMiddleware,\n    isServerRender,\n    parseJSON,\n    persistCache,\n    isBackground,\n    unstable_skipClientCache\n  } = param;\n  const {\n    href: cacheKey\n  } = new URL(dataHref, window.location.href);\n  const getData = params => {\n    var _params_method;\n    return fetchRetry(dataHref, isServerRender ? 3 : 1, {\n      headers: Object.assign({}, isPrefetch ? {\n        purpose: 'prefetch'\n      } : {}, isPrefetch && hasMiddleware ? {\n        'x-middleware-prefetch': '1'\n      } : {},  false ? 0 : {}),\n      method: (_params_method = params == null ? void 0 : params.method) != null ? _params_method : 'GET'\n    }).then(response => {\n      if (response.ok && (params == null ? void 0 : params.method) === 'HEAD') {\n        return {\n          dataHref,\n          response,\n          text: '',\n          json: {},\n          cacheKey\n        };\n      }\n      return response.text().then(text => {\n        if (!response.ok) {\n          /**\n          * When the data response is a redirect because of a middleware\n          * we do not consider it an error. The headers must bring the\n          * mapped location.\n          * TODO: Change the status code in the handler.\n          */\n          if (hasMiddleware && [301, 302, 307, 308].includes(response.status)) {\n            return {\n              dataHref,\n              response,\n              text,\n              json: {},\n              cacheKey\n            };\n          }\n          if (response.status === 404) {\n            var _tryToParseAsJSON;\n            if ((_tryToParseAsJSON = tryToParseAsJSON(text)) == null ? void 0 : _tryToParseAsJSON.notFound) {\n              return {\n                dataHref,\n                json: {\n                  notFound: SSG_DATA_NOT_FOUND\n                },\n                response,\n                text,\n                cacheKey\n              };\n            }\n          }\n          const error = Object.defineProperty(new Error(\"Failed to load static props\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E124\",\n            enumerable: false,\n            configurable: true\n          });\n          /**\n          * We should only trigger a server-side transition if this was\n          * caused on a client-side transition. Otherwise, we'd get into\n          * an infinite loop.\n          */\n          if (!isServerRender) {\n            (0, _routeloader.markAssetError)(error);\n          }\n          throw error;\n        }\n        return {\n          dataHref,\n          json: parseJSON ? tryToParseAsJSON(text) : null,\n          response,\n          text,\n          cacheKey\n        };\n      });\n    }).then(data => {\n      if (!persistCache || true || 0) {\n        delete inflightCache[cacheKey];\n      }\n      return data;\n    }).catch(err => {\n      if (!unstable_skipClientCache) {\n        delete inflightCache[cacheKey];\n      }\n      if (\n      // chrome\n      err.message === 'Failed to fetch' ||\n      // firefox\n      err.message === 'NetworkError when attempting to fetch resource.' ||\n      // safari\n      err.message === 'Load failed') {\n        (0, _routeloader.markAssetError)(err);\n      }\n      throw err;\n    });\n  };\n  // when skipping client cache we wait to update\n  // inflight cache until successful data response\n  // this allows racing click event with fetching newer data\n  // without blocking navigation when stale data is available\n  if (unstable_skipClientCache && persistCache) {\n    return getData({}).then(data => {\n      if (data.response.headers.get('x-middleware-cache') !== 'no-cache') {\n        // only update cache if not marked as no-cache\n        inflightCache[cacheKey] = Promise.resolve(data);\n      }\n      return data;\n    });\n  }\n  if (inflightCache[cacheKey] !== undefined) {\n    return inflightCache[cacheKey];\n  }\n  return inflightCache[cacheKey] = getData(isBackground ? {\n    method: 'HEAD'\n  } : {});\n}\nfunction createKey() {\n  return Math.random().toString(36).slice(2, 10);\n}\nfunction handleHardNavigation(param) {\n  let {\n    url,\n    router\n  } = param;\n  // ensure we don't trigger a hard navigation to the same\n  // URL as this can end up with an infinite refresh\n  if (url === (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(router.asPath, router.locale))) {\n    throw Object.defineProperty(new Error(\"Invariant: attempted to hard navigate to the same URL \" + url + \" \" + location.href), \"__NEXT_ERROR_CODE\", {\n      value: \"E282\",\n      enumerable: false,\n      configurable: true\n    });\n  }\n  window.location.href = url;\n}\nconst getCancelledHandler = param => {\n  let {\n    route,\n    router\n  } = param;\n  let cancelled = false;\n  const cancel = router.clc = () => {\n    cancelled = true;\n  };\n  const handleCancelled = () => {\n    if (cancelled) {\n      const error = Object.defineProperty(new Error('Abort fetching component for route: \"' + route + '\"'), \"__NEXT_ERROR_CODE\", {\n        value: \"E483\",\n        enumerable: false,\n        configurable: true\n      });\n      error.cancelled = true;\n      throw error;\n    }\n    if (cancel === router.clc) {\n      router.clc = null;\n    }\n  };\n  return handleCancelled;\n};\nclass Router {\n  reload() {\n    window.location.reload();\n  }\n  /**\n  * Go back in history\n  */\n  back() {\n    window.history.back();\n  }\n  /**\n  * Go forward in history\n  */\n  forward() {\n    window.history.forward();\n  }\n  /**\n  * Performs a `pushState` with arguments\n  * @param url of the route\n  * @param as masks `url` for the browser\n  * @param options object you can define `shallow` and other options\n  */\n  push(url, as, options) {\n    if (options === void 0) options = {};\n    if (false) {}\n    ;\n    ({\n      url,\n      as\n    } = prepareUrlAs(this, url, as));\n    return this.change('pushState', url, as, options);\n  }\n  /**\n  * Performs a `replaceState` with arguments\n  * @param url of the route\n  * @param as masks `url` for the browser\n  * @param options object you can define `shallow` and other options\n  */\n  replace(url, as, options) {\n    if (options === void 0) options = {};\n    ;\n    ({\n      url,\n      as\n    } = prepareUrlAs(this, url, as));\n    return this.change('replaceState', url, as, options);\n  }\n  async _bfl(as, resolvedAs, locale, skipNavigate) {\n    if (true) {\n      if (!this._bfl_s && !this._bfl_d) {\n        const {\n          BloomFilter\n        } = __webpack_require__(/*! ../../lib/bloom-filter */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/bloom-filter.js\");\n        let staticFilterData;\n        let dynamicFilterData;\n        try {\n          ;\n          ({\n            __routerFilterStatic: staticFilterData,\n            __routerFilterDynamic: dynamicFilterData\n          } = await (0, _routeloader.getClientBuildManifest)());\n        } catch (err) {\n          // failed to load build manifest hard navigate\n          // to be safe\n          console.error(err);\n          if (skipNavigate) {\n            return true;\n          }\n          handleHardNavigation({\n            url: (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, locale || this.locale, this.defaultLocale)),\n            router: this\n          });\n          return new Promise(() => {});\n        }\n        const routerFilterSValue = {\"numItems\":114,\"errorRate\":0.0001,\"numBits\":2186,\"numHashes\":14,\"bitArray\":[0,0,0,1,1,0,1,0,0,1,1,1,0,1,1,0,1,1,1,1,1,1,1,0,1,0,1,1,1,0,1,1,1,0,1,0,1,0,0,0,0,1,0,1,1,1,0,1,0,0,1,0,0,0,0,0,1,1,1,0,1,0,1,1,0,1,1,1,1,0,1,1,1,0,1,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,1,0,1,1,1,0,1,1,1,1,0,0,0,0,1,0,1,0,0,0,1,1,1,0,1,0,1,0,1,1,0,1,0,1,0,0,1,1,0,0,0,1,0,1,1,0,1,0,1,1,1,0,1,0,1,1,1,0,1,1,1,0,1,0,0,1,0,1,0,1,0,0,0,0,1,1,0,0,0,0,1,1,1,1,1,0,0,1,1,1,0,1,0,1,1,1,0,1,0,1,0,0,1,1,0,1,1,0,0,0,0,0,1,1,0,0,1,1,0,0,1,0,1,0,0,0,0,1,0,0,1,1,0,0,0,1,0,1,1,1,1,1,0,1,0,0,1,1,0,1,0,1,1,1,1,0,1,1,1,1,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,1,0,0,1,0,0,0,0,0,1,0,1,0,0,1,1,0,1,1,0,0,1,1,0,0,0,0,0,1,1,0,1,0,0,0,0,1,0,1,1,1,0,1,0,0,1,1,0,0,0,0,0,0,1,1,1,0,0,0,1,1,0,1,0,0,1,1,1,0,1,1,0,1,1,0,0,1,1,1,1,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,0,0,1,1,1,1,1,1,0,1,0,0,1,0,0,0,1,0,1,1,1,0,0,1,0,1,1,1,1,1,1,1,1,0,0,1,1,1,0,0,0,0,1,1,1,0,1,1,0,0,1,1,1,1,0,1,1,1,1,0,1,0,0,1,1,1,0,0,1,1,0,0,0,1,0,0,1,1,1,1,0,1,1,0,0,1,0,0,1,0,1,0,0,1,1,1,1,1,0,1,1,0,0,0,1,0,0,0,1,0,0,1,0,0,0,0,1,1,1,0,0,1,0,1,0,1,1,1,0,1,1,1,0,0,1,1,1,1,0,1,0,0,1,1,1,1,0,0,0,0,0,0,0,1,1,1,0,0,1,0,1,0,0,1,0,1,1,1,1,0,1,0,1,0,0,0,1,1,0,0,0,0,1,1,1,0,0,0,1,0,1,1,1,0,0,1,0,0,1,0,0,0,1,0,1,1,1,0,1,1,0,1,0,0,1,1,0,1,0,0,0,0,1,1,0,1,1,0,1,1,0,1,0,0,1,0,1,1,0,1,1,0,0,1,1,0,1,1,0,0,0,0,1,1,1,0,1,0,1,0,1,1,0,0,0,0,1,1,0,0,1,1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,0,0,1,0,1,0,1,0,0,0,0,0,1,0,0,1,1,1,0,1,0,0,1,0,0,1,0,1,1,1,1,0,0,0,0,0,0,1,0,1,1,1,0,0,1,1,0,1,0,0,1,1,1,0,0,1,0,0,0,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,0,0,1,1,1,0,1,0,0,1,0,1,0,1,0,1,0,1,1,0,1,1,0,1,1,1,0,1,0,0,0,1,1,0,1,0,0,0,1,0,1,1,1,0,1,0,1,0,1,1,0,0,0,0,1,0,0,1,0,0,1,0,1,0,0,1,1,0,0,1,1,1,1,0,0,1,1,0,0,0,1,1,1,0,1,1,0,1,1,1,0,0,1,1,0,1,1,1,1,0,1,1,0,1,0,0,0,1,1,1,1,1,1,1,0,1,1,0,0,0,0,0,0,0,1,0,0,1,0,1,0,0,1,0,1,0,1,1,1,1,1,1,0,0,1,0,1,0,1,0,1,0,1,1,1,0,0,1,1,1,0,1,0,0,1,0,1,0,1,1,1,0,1,1,1,0,0,1,0,0,1,1,0,1,0,0,0,0,0,1,0,1,1,0,0,0,0,0,1,1,0,0,1,0,0,0,1,0,0,1,1,1,1,1,1,1,0,1,1,1,1,0,0,0,0,0,0,1,1,0,0,0,0,1,0,1,0,1,0,1,0,1,0,0,0,1,1,1,1,0,0,1,1,0,0,1,1,0,0,0,1,1,1,0,0,1,1,0,1,1,1,1,1,1,0,1,0,1,1,1,0,0,0,1,1,1,1,1,0,1,0,0,0,1,1,1,0,1,0,0,1,0,1,0,1,1,0,0,1,0,1,0,0,1,0,1,1,0,1,1,0,1,0,1,1,0,1,0,1,0,1,1,1,0,0,0,1,1,0,1,1,1,0,1,0,1,1,1,0,0,1,1,1,1,0,0,0,1,1,0,1,1,1,0,0,0,1,0,1,0,1,0,0,1,1,0,1,1,0,1,0,0,1,1,0,1,1,0,0,1,0,0,1,1,1,0,0,0,1,1,0,0,0,1,0,1,0,1,1,0,0,1,1,0,1,0,0,1,1,1,0,1,1,0,1,0,0,1,1,1,0,0,1,0,1,1,0,0,1,1,1,1,0,0,1,0,1,0,0,0,0,0,0,1,0,1,1,0,1,0,0,1,0,0,1,0,0,0,0,0,1,0,0,0,1,0,1,0,1,1,0,1,0,1,0,1,0,0,1,0,1,1,0,1,1,1,0,0,0,1,1,0,0,0,1,0,1,0,1,1,1,0,1,0,1,0,1,1,1,1,0,0,1,1,0,1,1,1,1,1,0,0,1,0,0,1,1,1,1,0,1,0,1,1,1,0,0,1,0,0,1,1,0,1,0,1,0,1,1,0,0,1,1,0,0,1,1,0,0,1,0,0,1,1,0,1,1,0,0,1,1,0,1,0,0,0,0,0,0,1,0,0,1,1,0,0,0,0,1,1,1,0,1,1,0,0,1,1,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,0,1,0,1,0,0,0,1,1,1,1,0,1,1,1,0,0,0,0,1,0,1,0,1,0,0,1,0,1,1,1,1,0,1,0,0,1,0,1,1,1,0,1,1,0,1,1,1,0,1,0,0,0,1,0,0,1,1,1,0,1,1,1,0,1,1,1,1,1,0,1,0,1,1,1,1,0,0,0,1,1,0,0,1,0,1,1,0,0,1,1,1,1,1,1,1,1,0,0,1,1,1,1,1,0,1,0,0,0,0,1,0,0,1,1,0,0,1,0,0,1,0,1,0,1,0,0,0,1,0,1,0,1,1,0,1,0,0,0,1,0,0,0,0,0,0,1,1,1,0,1,0,0,1,1,1,0,1,0,1,0,1,1,0,0,0,1,0,1,1,1,1,1,1,0,1,0,1,0,0,1,0,0,1,1,1,0,0,1,1,0,0,1,0,1,0,0,0,0,0,0,1,0,0,1,1,1,0,1,1,0,0,0,1,1,1,1,1,0,1,0,1,1,0,0,1,1,1,1,1,0,1,0,1,1,1,0,1,0,0,0,0,1,0,0,0,0,0,0,1,1,1,0,1,1,1,0,1,1,1,1,1,1,0,0,1,0,0,0,0,0,1,0,0,1,0,0,1,1,0,1,1,1,0,0,0,1,0,1,1,0,1,1,1,0,1,0,0,1,1,0,1,0,1,0,1,1,1,0,0,0,1,0,0,0,1,1,0,0,0,0,1,1,0,1,0,1,0,1,1,1,1,0,1,0,0,0,1,0,1,1,0,0,0,1,1,0,1,0,0,1,0,1,0,0,1,0,1,0,0,1,0,1,1,1,1,1,1,0,1,0,0,1,1,1,0,1,1,1,1,1,1,1,0,1,0,1,0,1,1,1,0,0,1,1,0,1,1,1,1,0,1,1,1,1,1,1,0,0,0,1,0,1,1,0,1,1,0,0,1,0,1,1,0,0,1,1,1,0,1,1,0,0,1,0,1,1,1,1,0,1,1,0,1,1,1,1,0,1,1,1,1,1,0,1,0,1,0,0,0,1,1,0,0,0,0,0,1,1,1,1,0,1,1,0,1,1,0,1,1,0,0,1,0,0,1,0,1,0,0,1,0,1,1,0,0,0,1,0,0,0,1,1,1,0,0,0,0,1,1,0,1,0,0,0,1,1,0,1,1,1,0,0,0,0,1,0,0,1,0,0,0,1,1,0,1,1,0,0,1,1,1,0,0,1,1,1,0,1,0,1,1,0,0,1,0,1,1,0,1,1,1,1,1,1,0,1,1,0,0,0,0,0,1,1,0,0,0,0,0,0,1,1,1,0,1,1,0,1,0,1,1,1,1,0,0,0,1,0,1,0,1,1,1,0,1,0,0,1,0,1,1,0,1,1,1,0,1,0,0,1,0,0,0,1,0,0,1,1,0,1,0,1,1,1,1,1,0,0,1,1,0,0,0,0,1,0,0,0,1,1,1,0,1,0,1,1,0,0,0,1,0,0,0,0,1,0,0,1,0,1,1,0,0,1,1,0,0,0,1,0,0,0,1,1,0,0,1,0,1,0,0,0,1,0,1,0,1,0,1,1,0,0,0,1,1,1,0,1,0,0,0,1,1,0,1,1,0,0,0,0,1,1,0,1,0,0,0,0,1,0,1,1,1,1,1,0,0,0,0,0,1,1,1,1,0,0,1,0,1,0,1,0,1,0,1,0,0,1,0,0,1,0,1,0,0,0,0,0,1,1,0,1,0,1,0,0,1,0,1,0,0,0,1,0,1,0,1,1,0,0,0,0,1,1,1,0,1,1,1,0,1,0,1,0,1,1,0,0,0,1,1,1,1,1,1,0,0,1,0,1,1,1,1,0,1,0,1,0,1,0,1,1,1,1,1]};\n        if (!staticFilterData && routerFilterSValue) {\n          staticFilterData = routerFilterSValue ? routerFilterSValue : undefined;\n        }\n        const routerFilterDValue = {\"numItems\":35,\"errorRate\":0.0001,\"numBits\":671,\"numHashes\":14,\"bitArray\":[0,0,0,1,0,0,1,0,1,1,1,0,1,0,1,0,1,1,1,1,1,1,0,0,1,1,1,0,1,1,1,0,1,0,0,1,0,1,0,1,0,1,0,1,0,1,1,0,1,1,1,1,0,0,1,1,1,1,0,0,1,0,0,0,1,1,0,1,0,1,1,1,1,0,0,0,0,0,1,1,1,1,1,0,0,0,1,0,1,1,0,0,1,1,0,1,1,1,1,1,0,1,1,1,1,1,0,1,0,0,1,0,1,1,1,1,0,0,0,1,1,1,1,1,0,0,0,0,1,0,1,0,1,0,0,1,0,0,1,1,0,0,0,1,1,1,1,1,0,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,0,0,0,1,1,1,1,1,0,1,1,1,0,1,1,0,1,0,1,0,0,0,1,1,1,0,0,1,1,0,1,0,0,0,1,1,1,0,1,1,1,1,1,0,1,0,1,0,0,1,0,1,0,0,1,0,0,1,0,1,1,1,1,1,0,0,1,0,0,1,1,1,0,1,0,1,0,1,0,1,1,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,0,1,0,1,1,1,1,1,0,0,1,0,1,0,1,0,1,0,1,1,1,0,0,1,1,0,1,1,1,1,1,1,0,1,0,0,0,0,0,0,1,0,0,0,0,0,1,1,0,1,0,0,0,1,0,1,0,0,0,1,1,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,1,1,0,1,1,0,0,0,0,0,1,1,0,0,1,0,0,1,0,1,1,1,1,0,0,0,1,1,0,1,1,0,0,1,0,1,0,1,0,1,0,0,0,0,1,0,1,1,0,1,0,0,1,1,1,1,1,1,0,1,0,0,1,0,1,1,1,0,1,1,0,1,0,1,0,1,0,0,0,1,1,1,1,1,1,0,1,0,0,0,0,0,1,1,1,1,1,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,1,1,1,1,0,1,0,0,1,1,1,1,1,1,0,0,0,1,1,0,0,0,1,0,1,1,0,0,0,0,1,1,0,1,1,1,0,0,1,1,0,0,1,0,1,0,1,0,0,1,1,1,0,0,0,1,1,0,0,0,1,1,0,0,1,0,1,1,0,0,1,0,0,0,1,1,0,0,0,0,1,1,1,0,1,0,1,1,1,1,0,0,0,1,1,0,0,0,0,0,1,1,0,1,1,1,1,0,1,0,0,1,1,1,0,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,0,1,0,1,0,1,0,0,0,0,0,1,1,1,1,1,0,1,1,0,0,0,1,0,0,0,0,0,0,1,0,1,1,0,1,1,0,0,0,0,0,1,1,0,0,1,0,0,1,1,0,0,1,1,0,1,0,1,1,1,1,0,0,1,1,1,0,0,0,0,0,1,1]};\n        if (!dynamicFilterData && routerFilterDValue) {\n          dynamicFilterData = routerFilterDValue ? routerFilterDValue : undefined;\n        }\n        if (staticFilterData == null ? void 0 : staticFilterData.numHashes) {\n          this._bfl_s = new BloomFilter(staticFilterData.numItems, staticFilterData.errorRate);\n          this._bfl_s.import(staticFilterData);\n        }\n        if (dynamicFilterData == null ? void 0 : dynamicFilterData.numHashes) {\n          this._bfl_d = new BloomFilter(dynamicFilterData.numItems, dynamicFilterData.errorRate);\n          this._bfl_d.import(dynamicFilterData);\n        }\n      }\n      let matchesBflStatic = false;\n      let matchesBflDynamic = false;\n      const pathsToCheck = [{\n        as\n      }, {\n        as: resolvedAs\n      }];\n      for (const {\n        as: curAs,\n        allowMatchCurrent\n      } of pathsToCheck) {\n        if (curAs) {\n          const asNoSlash = (0, _removetrailingslash.removeTrailingSlash)(new URL(curAs, 'http://n').pathname);\n          const asNoSlashLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(asNoSlash, locale || this.locale));\n          if (allowMatchCurrent || asNoSlash !== (0, _removetrailingslash.removeTrailingSlash)(new URL(this.asPath, 'http://n').pathname)) {\n            var _this__bfl_s, _this__bfl_s1;\n            matchesBflStatic = matchesBflStatic || !!((_this__bfl_s = this._bfl_s) == null ? void 0 : _this__bfl_s.contains(asNoSlash)) || !!((_this__bfl_s1 = this._bfl_s) == null ? void 0 : _this__bfl_s1.contains(asNoSlashLocale));\n            for (const normalizedAS of [asNoSlash, asNoSlashLocale]) {\n              // if any sub-path of as matches a dynamic filter path\n              // it should be hard navigated\n              const curAsParts = normalizedAS.split('/');\n              for (let i = 0; !matchesBflDynamic && i < curAsParts.length + 1; i++) {\n                var _this__bfl_d;\n                const currentPart = curAsParts.slice(0, i).join('/');\n                if (currentPart && ((_this__bfl_d = this._bfl_d) == null ? void 0 : _this__bfl_d.contains(currentPart))) {\n                  matchesBflDynamic = true;\n                  break;\n                }\n              }\n            }\n            // if the client router filter is matched then we trigger\n            // a hard navigation\n            if (matchesBflStatic || matchesBflDynamic) {\n              if (skipNavigate) {\n                return true;\n              }\n              handleHardNavigation({\n                url: (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, locale || this.locale, this.defaultLocale)),\n                router: this\n              });\n              return new Promise(() => {});\n            }\n          }\n        }\n      }\n    }\n    return false;\n  }\n  async change(method, url, as, options, forcedScroll) {\n    var _this_components_pathname;\n    if (!(0, _islocalurl.isLocalURL)(url)) {\n      handleHardNavigation({\n        url,\n        router: this\n      });\n      return false;\n    }\n    // WARNING: `_h` is an internal option for handing Next.js client-side\n    // hydration. Your app should _never_ use this property. It may change at\n    // any time without notice.\n    const isQueryUpdating = options._h === 1;\n    if (!isQueryUpdating && !options.shallow) {\n      await this._bfl(as, undefined, options.locale);\n    }\n    let shouldResolveHref = isQueryUpdating || options._shouldResolveHref || (0, _parsepath.parsePath)(url).pathname === (0, _parsepath.parsePath)(as).pathname;\n    const nextState = {\n      ...this.state\n    };\n    // for static pages with query params in the URL we delay\n    // marking the router ready until after the query is updated\n    // or a navigation has occurred\n    const readyStateChange = this.isReady !== true;\n    this.isReady = true;\n    const isSsr = this.isSsr;\n    if (!isQueryUpdating) {\n      this.isSsr = false;\n    }\n    // if a route transition is already in progress before\n    // the query updating is triggered ignore query updating\n    if (isQueryUpdating && this.clc) {\n      return false;\n    }\n    const prevLocale = nextState.locale;\n    if (false) { var _this_locales; }\n    // marking route changes as a navigation start entry\n    if (_utils.ST) {\n      performance.mark('routeChange');\n    }\n    const {\n      shallow = false,\n      scroll = true\n    } = options;\n    const routeProps = {\n      shallow\n    };\n    if (this._inFlightRoute && this.clc) {\n      if (!isSsr) {\n        Router.events.emit('routeChangeError', buildCancellationError(), this._inFlightRoute, routeProps);\n      }\n      this.clc();\n      this.clc = null;\n    }\n    as = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, options.locale, this.defaultLocale));\n    const cleanedAs = (0, _removelocale.removeLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, nextState.locale);\n    this._inFlightRoute = as;\n    const localeChange = prevLocale !== nextState.locale;\n    // If the url change is only related to a hash change\n    // We should not proceed. We should only change the state.\n    if (!isQueryUpdating && this.onlyAHashChange(cleanedAs) && !localeChange) {\n      nextState.asPath = cleanedAs;\n      Router.events.emit('hashChangeStart', as, routeProps);\n      // TODO: do we need the resolved href when only a hash change?\n      this.changeState(method, url, as, {\n        ...options,\n        scroll: false\n      });\n      if (scroll) {\n        this.scrollToHash(cleanedAs);\n      }\n      try {\n        await this.set(nextState, this.components[nextState.route], null);\n      } catch (err) {\n        if ((0, _iserror.default)(err) && err.cancelled) {\n          Router.events.emit('routeChangeError', err, cleanedAs, routeProps);\n        }\n        throw err;\n      }\n      Router.events.emit('hashChangeComplete', as, routeProps);\n      return true;\n    }\n    let parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n    let {\n      pathname,\n      query\n    } = parsed;\n    // The build manifest needs to be loaded before auto-static dynamic pages\n    // get their query parameters to allow ensuring they can be parsed properly\n    // when rewritten to\n    let pages, rewrites;\n    try {\n      ;\n      [pages, {\n        __rewrites: rewrites\n      }] = await Promise.all([this.pageLoader.getPageList(), (0, _routeloader.getClientBuildManifest)(), this.pageLoader.getMiddleware()]);\n    } catch (err) {\n      // If we fail to resolve the page list or client-build manifest, we must\n      // do a server-side transition:\n      handleHardNavigation({\n        url: as,\n        router: this\n      });\n      return false;\n    }\n    // If asked to change the current URL we should reload the current page\n    // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n    // We also need to set the method = replaceState always\n    // as this should not go into the history (That's how browsers work)\n    // We should compare the new asPath to the current asPath, not the url\n    if (!this.urlIsNew(cleanedAs) && !localeChange) {\n      method = 'replaceState';\n    }\n    // we need to resolve the as value using rewrites for dynamic SSG\n    // pages to allow building the data URL correctly\n    let resolvedAs = as;\n    // url and as should always be prefixed with basePath by this\n    // point by either next/link or router.push/replace so strip the\n    // basePath from the pathname to match the pages dir 1-to-1\n    pathname = pathname ? (0, _removetrailingslash.removeTrailingSlash)((0, _removebasepath.removeBasePath)(pathname)) : pathname;\n    let route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n    const parsedAsPathname = as.startsWith('/') && (0, _parserelativeurl.parseRelativeUrl)(as).pathname;\n    // if we detected the path as app route during prefetching\n    // trigger hard navigation\n    if ((_this_components_pathname = this.components[pathname]) == null ? void 0 : _this_components_pathname.__appRouter) {\n      handleHardNavigation({\n        url: as,\n        router: this\n      });\n      return new Promise(() => {});\n    }\n    const isMiddlewareRewrite = !!(parsedAsPathname && route !== parsedAsPathname && (!(0, _isdynamic.isDynamicRoute)(route) || !(0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(route))(parsedAsPathname)));\n    // we don't attempt resolve asPath when we need to execute\n    // middleware as the resolving will occur server-side\n    const isMiddlewareMatch = !options.shallow && (await matchesMiddleware({\n      asPath: as,\n      locale: nextState.locale,\n      router: this\n    }));\n    if (isQueryUpdating && isMiddlewareMatch) {\n      shouldResolveHref = false;\n    }\n    if (shouldResolveHref && pathname !== '/_error') {\n      ;\n      options._shouldResolveHref = true;\n      if (false) {} else {\n        parsed.pathname = resolveDynamicRoute(pathname, pages);\n        if (parsed.pathname !== pathname) {\n          pathname = parsed.pathname;\n          parsed.pathname = (0, _addbasepath.addBasePath)(pathname);\n          if (!isMiddlewareMatch) {\n            url = (0, _formaturl.formatWithValidation)(parsed);\n          }\n        }\n      }\n    }\n    if (!(0, _islocalurl.isLocalURL)(as)) {\n      if (true) {\n        throw Object.defineProperty(new Error('Invalid href: \"' + url + '\" and as: \"' + as + '\", received relative href and external as' + \"\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as\"), \"__NEXT_ERROR_CODE\", {\n          value: \"E380\",\n          enumerable: false,\n          configurable: true\n        });\n      }\n      handleHardNavigation({\n        url: as,\n        router: this\n      });\n      return false;\n    }\n    resolvedAs = (0, _removelocale.removeLocale)((0, _removebasepath.removeBasePath)(resolvedAs), nextState.locale);\n    route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n    let routeMatch = false;\n    if ((0, _isdynamic.isDynamicRoute)(route)) {\n      const parsedAs = (0, _parserelativeurl.parseRelativeUrl)(resolvedAs);\n      const asPathname = parsedAs.pathname;\n      const routeRegex = (0, _routeregex.getRouteRegex)(route);\n      routeMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(asPathname);\n      const shouldInterpolate = route === asPathname;\n      const interpolatedAs = shouldInterpolate ? (0, _interpolateas.interpolateAs)(route, asPathname, query) : {};\n      if (!routeMatch || shouldInterpolate && !interpolatedAs.result) {\n        const missingParams = Object.keys(routeRegex.groups).filter(param => !query[param] && !routeRegex.groups[param].optional);\n        if (missingParams.length > 0 && !isMiddlewareMatch) {\n          if (true) {\n            console.warn(\"\" + (shouldInterpolate ? \"Interpolating href\" : \"Mismatching `as` and `href`\") + \" failed to manually provide \" + (\"the params: \" + missingParams.join(', ') + \" in the `href`'s `query`\"));\n          }\n          throw Object.defineProperty(new Error((shouldInterpolate ? \"The provided `href` (\" + url + \") value is missing query values (\" + missingParams.join(', ') + \") to be interpolated properly. \" : \"The provided `as` value (\" + asPathname + \") is incompatible with the `href` value (\" + route + \"). \") + (\"Read more: https://nextjs.org/docs/messages/\" + (shouldInterpolate ? 'href-interpolation-failed' : 'incompatible-href-as'))), \"__NEXT_ERROR_CODE\", {\n            value: \"E344\",\n            enumerable: false,\n            configurable: true\n          });\n        }\n      } else if (shouldInterpolate) {\n        as = (0, _formaturl.formatWithValidation)(Object.assign({}, parsedAs, {\n          pathname: interpolatedAs.result,\n          query: (0, _omit.omit)(query, interpolatedAs.params)\n        }));\n      } else {\n        // Merge params into `query`, overwriting any specified in search\n        Object.assign(query, routeMatch);\n      }\n    }\n    if (!isQueryUpdating) {\n      Router.events.emit('routeChangeStart', as, routeProps);\n    }\n    const isErrorRoute = this.pathname === '/404' || this.pathname === '/_error';\n    try {\n      var _self___NEXT_DATA___props_pageProps, _self___NEXT_DATA___props, _routeInfo_props;\n      let routeInfo = await this.getRouteInfo({\n        route,\n        pathname,\n        query,\n        as,\n        resolvedAs,\n        routeProps,\n        locale: nextState.locale,\n        isPreview: nextState.isPreview,\n        hasMiddleware: isMiddlewareMatch,\n        unstable_skipClientCache: options.unstable_skipClientCache,\n        isQueryUpdating: isQueryUpdating && !this.isFallback,\n        isMiddlewareRewrite\n      });\n      if (!isQueryUpdating && !options.shallow) {\n        await this._bfl(as, 'resolvedAs' in routeInfo ? routeInfo.resolvedAs : undefined, nextState.locale);\n      }\n      if ('route' in routeInfo && isMiddlewareMatch) {\n        pathname = routeInfo.route || route;\n        route = pathname;\n        if (!routeProps.shallow) {\n          query = Object.assign({}, routeInfo.query || {}, query);\n        }\n        const cleanedParsedPathname = (0, _hasbasepath.hasBasePath)(parsed.pathname) ? (0, _removebasepath.removeBasePath)(parsed.pathname) : parsed.pathname;\n        if (routeMatch && pathname !== cleanedParsedPathname) {\n          Object.keys(routeMatch).forEach(key => {\n            if (routeMatch && query[key] === routeMatch[key]) {\n              delete query[key];\n            }\n          });\n        }\n        if ((0, _isdynamic.isDynamicRoute)(pathname)) {\n          const prefixedAs = !routeProps.shallow && routeInfo.resolvedAs ? routeInfo.resolvedAs : (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(new URL(as, location.href).pathname, nextState.locale), true);\n          let rewriteAs = prefixedAs;\n          if ((0, _hasbasepath.hasBasePath)(rewriteAs)) {\n            rewriteAs = (0, _removebasepath.removeBasePath)(rewriteAs);\n          }\n          if (false) {}\n          const routeRegex = (0, _routeregex.getRouteRegex)(pathname);\n          const curRouteMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(new URL(rewriteAs, location.href).pathname);\n          if (curRouteMatch) {\n            Object.assign(query, curRouteMatch);\n          }\n        }\n      }\n      // If the routeInfo brings a redirect we simply apply it.\n      if ('type' in routeInfo) {\n        if (routeInfo.type === 'redirect-internal') {\n          return this.change(method, routeInfo.newUrl, routeInfo.newAs, options);\n        } else {\n          handleHardNavigation({\n            url: routeInfo.destination,\n            router: this\n          });\n          return new Promise(() => {});\n        }\n      }\n      const component = routeInfo.Component;\n      if (component && component.unstable_scriptLoader) {\n        const scripts = [].concat(component.unstable_scriptLoader());\n        scripts.forEach(script => {\n          (0, _script.handleClientScriptLoad)(script.props);\n        });\n      }\n      // handle redirect on client-transition\n      if ((routeInfo.__N_SSG || routeInfo.__N_SSP) && routeInfo.props) {\n        if (routeInfo.props.pageProps && routeInfo.props.pageProps.__N_REDIRECT) {\n          // Use the destination from redirect without adding locale\n          options.locale = false;\n          const destination = routeInfo.props.pageProps.__N_REDIRECT;\n          // check if destination is internal (resolves to a page) and attempt\n          // client-navigation if it is falling back to hard navigation if\n          // it's not\n          if (destination.startsWith('/') && routeInfo.props.pageProps.__N_REDIRECT_BASE_PATH !== false) {\n            const parsedHref = (0, _parserelativeurl.parseRelativeUrl)(destination);\n            parsedHref.pathname = resolveDynamicRoute(parsedHref.pathname, pages);\n            const {\n              url: newUrl,\n              as: newAs\n            } = prepareUrlAs(this, destination, destination);\n            return this.change(method, newUrl, newAs, options);\n          }\n          handleHardNavigation({\n            url: destination,\n            router: this\n          });\n          return new Promise(() => {});\n        }\n        nextState.isPreview = !!routeInfo.props.__N_PREVIEW;\n        // handle SSG data 404\n        if (routeInfo.props.notFound === SSG_DATA_NOT_FOUND) {\n          let notFoundRoute;\n          try {\n            await this.fetchComponent('/404');\n            notFoundRoute = '/404';\n          } catch (_) {\n            notFoundRoute = '/_error';\n          }\n          routeInfo = await this.getRouteInfo({\n            route: notFoundRoute,\n            pathname: notFoundRoute,\n            query,\n            as,\n            resolvedAs,\n            routeProps: {\n              shallow: false\n            },\n            locale: nextState.locale,\n            isPreview: nextState.isPreview,\n            isNotFound: true\n          });\n          if ('type' in routeInfo) {\n            throw Object.defineProperty(new Error(\"Unexpected middleware effect on /404\"), \"__NEXT_ERROR_CODE\", {\n              value: \"E158\",\n              enumerable: false,\n              configurable: true\n            });\n          }\n        }\n      }\n      if (isQueryUpdating && this.pathname === '/_error' && ((_self___NEXT_DATA___props = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps = _self___NEXT_DATA___props.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps.statusCode) === 500 && ((_routeInfo_props = routeInfo.props) == null ? void 0 : _routeInfo_props.pageProps)) {\n        // ensure statusCode is still correct for static 500 page\n        // when updating query information\n        routeInfo.props.pageProps.statusCode = 500;\n      }\n      var _routeInfo_route;\n      // shallow routing is only allowed for same page URL changes.\n      const isValidShallowRoute = options.shallow && nextState.route === ((_routeInfo_route = routeInfo.route) != null ? _routeInfo_route : route);\n      var _options_scroll;\n      const shouldScroll = (_options_scroll = options.scroll) != null ? _options_scroll : !isQueryUpdating && !isValidShallowRoute;\n      const resetScroll = shouldScroll ? {\n        x: 0,\n        y: 0\n      } : null;\n      const upcomingScrollState = forcedScroll != null ? forcedScroll : resetScroll;\n      // the new state that the router gonna set\n      const upcomingRouterState = {\n        ...nextState,\n        route,\n        pathname,\n        query,\n        asPath: cleanedAs,\n        isFallback: false\n      };\n      // When the page being rendered is the 404 page, we should only update the\n      // query parameters. Route changes here might add the basePath when it\n      // wasn't originally present. This is also why this block is before the\n      // below `changeState` call which updates the browser's history (changing\n      // the URL).\n      if (isQueryUpdating && isErrorRoute) {\n        var _self___NEXT_DATA___props_pageProps1, _self___NEXT_DATA___props1, _routeInfo_props1;\n        routeInfo = await this.getRouteInfo({\n          route: this.pathname,\n          pathname: this.pathname,\n          query,\n          as,\n          resolvedAs,\n          routeProps: {\n            shallow: false\n          },\n          locale: nextState.locale,\n          isPreview: nextState.isPreview,\n          isQueryUpdating: isQueryUpdating && !this.isFallback\n        });\n        if ('type' in routeInfo) {\n          throw Object.defineProperty(new Error(\"Unexpected middleware effect on \" + this.pathname), \"__NEXT_ERROR_CODE\", {\n            value: \"E225\",\n            enumerable: false,\n            configurable: true\n          });\n        }\n        if (this.pathname === '/_error' && ((_self___NEXT_DATA___props1 = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps1 = _self___NEXT_DATA___props1.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps1.statusCode) === 500 && ((_routeInfo_props1 = routeInfo.props) == null ? void 0 : _routeInfo_props1.pageProps)) {\n          // ensure statusCode is still correct for static 500 page\n          // when updating query information\n          routeInfo.props.pageProps.statusCode = 500;\n        }\n        try {\n          await this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n        } catch (err) {\n          if ((0, _iserror.default)(err) && err.cancelled) {\n            Router.events.emit('routeChangeError', err, cleanedAs, routeProps);\n          }\n          throw err;\n        }\n        return true;\n      }\n      Router.events.emit('beforeHistoryChange', as, routeProps);\n      this.changeState(method, url, as, options);\n      // for query updates we can skip it if the state is unchanged and we don't\n      // need to scroll\n      // https://github.com/vercel/next.js/issues/37139\n      const canSkipUpdating = isQueryUpdating && !upcomingScrollState && !readyStateChange && !localeChange && (0, _comparestates.compareRouterStates)(upcomingRouterState, this.state);\n      if (!canSkipUpdating) {\n        try {\n          await this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n        } catch (e) {\n          if (e.cancelled) routeInfo.error = routeInfo.error || e;else throw e;\n        }\n        if (routeInfo.error) {\n          if (!isQueryUpdating) {\n            Router.events.emit('routeChangeError', routeInfo.error, cleanedAs, routeProps);\n          }\n          throw routeInfo.error;\n        }\n        if (false) {}\n        if (!isQueryUpdating) {\n          Router.events.emit('routeChangeComplete', as, routeProps);\n        }\n        // A hash mark # is the optional last part of a URL\n        const hashRegex = /#.+$/;\n        if (shouldScroll && hashRegex.test(as)) {\n          this.scrollToHash(as);\n        }\n      }\n      return true;\n    } catch (err) {\n      if ((0, _iserror.default)(err) && err.cancelled) {\n        return false;\n      }\n      throw err;\n    }\n  }\n  changeState(method, url, as, options) {\n    if (options === void 0) options = {};\n    if (true) {\n      if (typeof window.history === 'undefined') {\n        console.error(\"Warning: window.history is not available.\");\n        return;\n      }\n      if (typeof window.history[method] === 'undefined') {\n        console.error(\"Warning: window.history.\" + method + \" is not available\");\n        return;\n      }\n    }\n    if (method !== 'pushState' || (0, _utils.getURL)() !== as) {\n      this._shallow = options.shallow;\n      window.history[method]({\n        url,\n        as,\n        options,\n        __N: true,\n        key: this._key = method !== 'pushState' ? this._key : createKey()\n      },\n      // Most browsers currently ignores this parameter, although they may use it in the future.\n      // Passing the empty string here should be safe against future changes to the method.\n      // https://developer.mozilla.org/docs/Web/API/History/replaceState\n      '', as);\n    }\n  }\n  async handleRouteInfoError(err, pathname, query, as, routeProps, loadErrorFail) {\n    if (err.cancelled) {\n      // bubble up cancellation errors\n      throw err;\n    }\n    if ((0, _routeloader.isAssetError)(err) || loadErrorFail) {\n      Router.events.emit('routeChangeError', err, as, routeProps);\n      // If we can't load the page it could be one of following reasons\n      //  1. Page doesn't exists\n      //  2. Page does exist in a different zone\n      //  3. Internal error while loading the page\n      // So, doing a hard reload is the proper way to deal with this.\n      handleHardNavigation({\n        url: as,\n        router: this\n      });\n      // Changing the URL doesn't block executing the current code path.\n      // So let's throw a cancellation error stop the routing logic.\n      throw buildCancellationError();\n    }\n    console.error(err);\n    try {\n      let props;\n      const {\n        page: Component,\n        styleSheets\n      } = await this.fetchComponent('/_error');\n      const routeInfo = {\n        props,\n        Component,\n        styleSheets,\n        err,\n        error: err\n      };\n      if (!routeInfo.props) {\n        try {\n          routeInfo.props = await this.getInitialProps(Component, {\n            err,\n            pathname,\n            query\n          });\n        } catch (gipErr) {\n          console.error('Error in error page `getInitialProps`: ', gipErr);\n          routeInfo.props = {};\n        }\n      }\n      return routeInfo;\n    } catch (routeInfoErr) {\n      return this.handleRouteInfoError((0, _iserror.default)(routeInfoErr) ? routeInfoErr : Object.defineProperty(new Error(routeInfoErr + ''), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n      }), pathname, query, as, routeProps, true);\n    }\n  }\n  async getRouteInfo(param) {\n    let {\n      route: requestedRoute,\n      pathname,\n      query,\n      as,\n      resolvedAs,\n      routeProps,\n      locale,\n      hasMiddleware,\n      isPreview,\n      unstable_skipClientCache,\n      isQueryUpdating,\n      isMiddlewareRewrite,\n      isNotFound\n    } = param;\n    /**\n    * This `route` binding can change if there's a rewrite\n    * so we keep a reference to the original requested route\n    * so we can store the cache for it and avoid re-requesting every time\n    * for shallow routing purposes.\n    */\n    let route = requestedRoute;\n    try {\n      var _data_effect, _data_effect1, _data_effect2, _data_response;\n      let existingInfo = this.components[route];\n      if (routeProps.shallow && existingInfo && this.route === route) {\n        return existingInfo;\n      }\n      const handleCancelled = getCancelledHandler({\n        route,\n        router: this\n      });\n      if (hasMiddleware) {\n        existingInfo = undefined;\n      }\n      let cachedRouteInfo = existingInfo && !('initial' in existingInfo) && false ? 0 : undefined;\n      const isBackground = isQueryUpdating;\n      const fetchNextDataParams = {\n        dataHref: this.pageLoader.getDataHref({\n          href: (0, _formaturl.formatWithValidation)({\n            pathname,\n            query\n          }),\n          skipInterpolation: true,\n          asPath: isNotFound ? '/404' : resolvedAs,\n          locale\n        }),\n        hasMiddleware: true,\n        isServerRender: this.isSsr,\n        parseJSON: true,\n        inflightCache: isBackground ? this.sbc : this.sdc,\n        persistCache: !isPreview,\n        isPrefetch: false,\n        unstable_skipClientCache,\n        isBackground\n      };\n      let data = isQueryUpdating && !isMiddlewareRewrite ? null : await withMiddlewareEffects({\n        fetchData: () => fetchNextData(fetchNextDataParams),\n        asPath: isNotFound ? '/404' : resolvedAs,\n        locale: locale,\n        router: this\n      }).catch(err => {\n        // we don't hard error during query updating\n        // as it's un-necessary and doesn't need to be fatal\n        // unless it is a fallback route and the props can't\n        // be loaded\n        if (isQueryUpdating) {\n          return null;\n        }\n        throw err;\n      });\n      // when rendering error routes we don't apply middleware\n      // effects\n      if (data && (pathname === '/_error' || pathname === '/404')) {\n        data.effect = undefined;\n      }\n      if (isQueryUpdating) {\n        if (!data) {\n          data = {\n            json: self.__NEXT_DATA__.props\n          };\n        } else {\n          data.json = self.__NEXT_DATA__.props;\n        }\n      }\n      handleCancelled();\n      if ((data == null ? void 0 : (_data_effect = data.effect) == null ? void 0 : _data_effect.type) === 'redirect-internal' || (data == null ? void 0 : (_data_effect1 = data.effect) == null ? void 0 : _data_effect1.type) === 'redirect-external') {\n        return data.effect;\n      }\n      if ((data == null ? void 0 : (_data_effect2 = data.effect) == null ? void 0 : _data_effect2.type) === 'rewrite') {\n        const resolvedRoute = (0, _removetrailingslash.removeTrailingSlash)(data.effect.resolvedHref);\n        const pages = await this.pageLoader.getPageList();\n        // during query updating the page must match although during\n        // client-transition a redirect that doesn't match a page\n        // can be returned and this should trigger a hard navigation\n        // which is valid for incremental migration\n        if (!isQueryUpdating || pages.includes(resolvedRoute)) {\n          route = resolvedRoute;\n          pathname = data.effect.resolvedHref;\n          query = {\n            ...query,\n            ...data.effect.parsedAs.query\n          };\n          resolvedAs = (0, _removebasepath.removeBasePath)((0, _normalizelocalepath.normalizeLocalePath)(data.effect.parsedAs.pathname, this.locales).pathname);\n          // Check again the cache with the new destination.\n          existingInfo = this.components[route];\n          if (routeProps.shallow && existingInfo && this.route === route && !hasMiddleware) {\n            // If we have a match with the current route due to rewrite,\n            // we can copy the existing information to the rewritten one.\n            // Then, we return the information along with the matched route.\n            return {\n              ...existingInfo,\n              route\n            };\n          }\n        }\n      }\n      if ((0, _isapiroute.isAPIRoute)(route)) {\n        handleHardNavigation({\n          url: as,\n          router: this\n        });\n        return new Promise(() => {});\n      }\n      const routeInfo = cachedRouteInfo || (await this.fetchComponent(route).then(res => ({\n        Component: res.page,\n        styleSheets: res.styleSheets,\n        __N_SSG: res.mod.__N_SSG,\n        __N_SSP: res.mod.__N_SSP\n      })));\n      if (true) {\n        const {\n          isValidElementType\n        } = __webpack_require__(/*! next/dist/compiled/react-is */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/react-is/index.js\");\n        if (!isValidElementType(routeInfo.Component)) {\n          throw Object.defineProperty(new Error('The default export is not a React Component in page: \"' + pathname + '\"'), \"__NEXT_ERROR_CODE\", {\n            value: \"E286\",\n            enumerable: false,\n            configurable: true\n          });\n        }\n      }\n      const wasBailedPrefetch = data == null ? void 0 : (_data_response = data.response) == null ? void 0 : _data_response.headers.get('x-middleware-skip');\n      const shouldFetchData = routeInfo.__N_SSG || routeInfo.__N_SSP;\n      // For non-SSG prefetches that bailed before sending data\n      // we clear the cache to fetch full response\n      if (wasBailedPrefetch && (data == null ? void 0 : data.dataHref)) {\n        delete this.sdc[data.dataHref];\n      }\n      const {\n        props,\n        cacheKey\n      } = await this._getData(async () => {\n        if (shouldFetchData) {\n          if ((data == null ? void 0 : data.json) && !wasBailedPrefetch) {\n            return {\n              cacheKey: data.cacheKey,\n              props: data.json\n            };\n          }\n          const dataHref = (data == null ? void 0 : data.dataHref) ? data.dataHref : this.pageLoader.getDataHref({\n            href: (0, _formaturl.formatWithValidation)({\n              pathname,\n              query\n            }),\n            asPath: resolvedAs,\n            locale\n          });\n          const fetched = await fetchNextData({\n            dataHref,\n            isServerRender: this.isSsr,\n            parseJSON: true,\n            inflightCache: wasBailedPrefetch ? {} : this.sdc,\n            persistCache: !isPreview,\n            isPrefetch: false,\n            unstable_skipClientCache\n          });\n          return {\n            cacheKey: fetched.cacheKey,\n            props: fetched.json || {}\n          };\n        }\n        return {\n          headers: {},\n          props: await this.getInitialProps(routeInfo.Component,\n          // we provide AppTree later so this needs to be `any`\n          {\n            pathname,\n            query,\n            asPath: as,\n            locale,\n            locales: this.locales,\n            defaultLocale: this.defaultLocale\n          })\n        };\n      });\n      // Only bust the data cache for SSP routes although\n      // middleware can skip cache per request with\n      // x-middleware-cache: no-cache as well\n      if (routeInfo.__N_SSP && fetchNextDataParams.dataHref && cacheKey) {\n        delete this.sdc[cacheKey];\n      }\n      // we kick off a HEAD request in the background\n      // when a non-prefetch request is made to signal revalidation\n      if (!this.isPreview && routeInfo.__N_SSG && false && 0) {}\n      props.pageProps = Object.assign({}, props.pageProps);\n      routeInfo.props = props;\n      routeInfo.route = route;\n      routeInfo.query = query;\n      routeInfo.resolvedAs = resolvedAs;\n      this.components[route] = routeInfo;\n      return routeInfo;\n    } catch (err) {\n      return this.handleRouteInfoError((0, _iserror.getProperError)(err), pathname, query, as, routeProps);\n    }\n  }\n  set(state, data, resetScroll) {\n    this.state = state;\n    return this.sub(data, this.components['/_app'].Component, resetScroll);\n  }\n  /**\n  * Callback to execute before replacing router state\n  * @param cb callback to be executed\n  */\n  beforePopState(cb) {\n    this._bps = cb;\n  }\n  onlyAHashChange(as) {\n    if (!this.asPath) return false;\n    const [oldUrlNoHash, oldHash] = this.asPath.split('#', 2);\n    const [newUrlNoHash, newHash] = as.split('#', 2);\n    // Makes sure we scroll to the provided hash if the url/hash are the same\n    if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n      return true;\n    }\n    // If the urls are change, there's more than a hash change\n    if (oldUrlNoHash !== newUrlNoHash) {\n      return false;\n    }\n    // If the hash has changed, then it's a hash only change.\n    // This check is necessary to handle both the enter and\n    // leave hash === '' cases. The identity case falls through\n    // and is treated as a next reload.\n    return oldHash !== newHash;\n  }\n  scrollToHash(as) {\n    const [, hash = ''] = as.split('#', 2);\n    (0, _handlesmoothscroll.handleSmoothScroll)(() => {\n      // Scroll to top if the hash is just `#` with no value or `#top`\n      // To mirror browsers\n      if (hash === '' || hash === 'top') {\n        window.scrollTo(0, 0);\n        return;\n      }\n      // Decode hash to make non-latin anchor works.\n      const rawHash = decodeURIComponent(hash);\n      // First we check if the element by id is found\n      const idEl = document.getElementById(rawHash);\n      if (idEl) {\n        idEl.scrollIntoView();\n        return;\n      }\n      // If there's no element with the id, we check the `name` property\n      // To mirror browsers\n      const nameEl = document.getElementsByName(rawHash)[0];\n      if (nameEl) {\n        nameEl.scrollIntoView();\n      }\n    }, {\n      onlyHashChange: this.onlyAHashChange(as)\n    });\n  }\n  urlIsNew(asPath) {\n    return this.asPath !== asPath;\n  }\n  /**\n  * Prefetch page code, you may wait for the data during page rendering.\n  * This feature only works in production!\n  * @param url the href of prefetched page\n  * @param asPath the as path of the prefetched page\n  */\n  async prefetch(url, asPath, options) {\n    if (asPath === void 0) asPath = url;\n    if (options === void 0) options = {};\n    // Prefetch is not supported in development mode because it would trigger on-demand-entries\n    if (true) {\n      return;\n    }\n    if ( true && (0, _isbot.isBot)(window.navigator.userAgent)) {\n      // No prefetches for bots that render the link since they are typically navigating\n      // links via the equivalent of a hard navigation and hence never utilize these\n      // prefetches.\n      return;\n    }\n    let parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n    const urlPathname = parsed.pathname;\n    let {\n      pathname,\n      query\n    } = parsed;\n    const originalPathname = pathname;\n    if (false) {}\n    const pages = await this.pageLoader.getPageList();\n    let resolvedAs = asPath;\n    const locale = typeof options.locale !== 'undefined' ? options.locale || undefined : this.locale;\n    const isMiddlewareMatch = await matchesMiddleware({\n      asPath: asPath,\n      locale: locale,\n      router: this\n    });\n    if (false) {}\n    parsed.pathname = resolveDynamicRoute(parsed.pathname, pages);\n    if ((0, _isdynamic.isDynamicRoute)(parsed.pathname)) {\n      pathname = parsed.pathname;\n      parsed.pathname = pathname;\n      Object.assign(query, (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(parsed.pathname))((0, _parsepath.parsePath)(asPath).pathname) || {});\n      if (!isMiddlewareMatch) {\n        url = (0, _formaturl.formatWithValidation)(parsed);\n      }\n    }\n    const data =  false ? 0 : await withMiddlewareEffects({\n      fetchData: () => fetchNextData({\n        dataHref: this.pageLoader.getDataHref({\n          href: (0, _formaturl.formatWithValidation)({\n            pathname: originalPathname,\n            query\n          }),\n          skipInterpolation: true,\n          asPath: resolvedAs,\n          locale\n        }),\n        hasMiddleware: true,\n        isServerRender: false,\n        parseJSON: true,\n        inflightCache: this.sdc,\n        persistCache: !this.isPreview,\n        isPrefetch: true\n      }),\n      asPath: asPath,\n      locale: locale,\n      router: this\n    });\n    /**\n    * If there was a rewrite we apply the effects of the rewrite on the\n    * current parameters for the prefetch.\n    */\n    if ((data == null ? void 0 : data.effect.type) === 'rewrite') {\n      parsed.pathname = data.effect.resolvedHref;\n      pathname = data.effect.resolvedHref;\n      query = {\n        ...query,\n        ...data.effect.parsedAs.query\n      };\n      resolvedAs = data.effect.parsedAs.pathname;\n      url = (0, _formaturl.formatWithValidation)(parsed);\n    }\n    /**\n    * If there is a redirect to an external destination then we don't have\n    * to prefetch content as it will be unused.\n    */\n    if ((data == null ? void 0 : data.effect.type) === 'redirect-external') {\n      return;\n    }\n    const route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n    if (await this._bfl(asPath, resolvedAs, options.locale, true)) {\n      this.components[urlPathname] = {\n        __appRouter: true\n      };\n    }\n    await Promise.all([this.pageLoader._isSsg(route).then(isSsg => {\n      return isSsg ? fetchNextData({\n        dataHref: (data == null ? void 0 : data.json) ? data == null ? void 0 : data.dataHref : this.pageLoader.getDataHref({\n          href: url,\n          asPath: resolvedAs,\n          locale: locale\n        }),\n        isServerRender: false,\n        parseJSON: true,\n        inflightCache: this.sdc,\n        persistCache: !this.isPreview,\n        isPrefetch: true,\n        unstable_skipClientCache: options.unstable_skipClientCache || options.priority && !!true\n      }).then(() => false).catch(() => false) : false;\n    }), this.pageLoader[options.priority ? 'loadPage' : 'prefetch'](route)]);\n  }\n  async fetchComponent(route) {\n    const handleCancelled = getCancelledHandler({\n      route,\n      router: this\n    });\n    try {\n      const componentResult = await this.pageLoader.loadPage(route);\n      handleCancelled();\n      return componentResult;\n    } catch (err) {\n      handleCancelled();\n      throw err;\n    }\n  }\n  _getData(fn) {\n    let cancelled = false;\n    const cancel = () => {\n      cancelled = true;\n    };\n    this.clc = cancel;\n    return fn().then(data => {\n      if (cancel === this.clc) {\n        this.clc = null;\n      }\n      if (cancelled) {\n        const err = Object.defineProperty(new Error('Loading initial props cancelled'), \"__NEXT_ERROR_CODE\", {\n          value: \"E405\",\n          enumerable: false,\n          configurable: true\n        });\n        err.cancelled = true;\n        throw err;\n      }\n      return data;\n    });\n  }\n  getInitialProps(Component, ctx) {\n    const {\n      Component: App\n    } = this.components['/_app'];\n    const AppTree = this._wrapApp(App);\n    ctx.AppTree = AppTree;\n    return (0, _utils.loadGetInitialProps)(App, {\n      AppTree,\n      Component,\n      router: this,\n      ctx\n    });\n  }\n  get route() {\n    return this.state.route;\n  }\n  get pathname() {\n    return this.state.pathname;\n  }\n  get query() {\n    return this.state.query;\n  }\n  get asPath() {\n    return this.state.asPath;\n  }\n  get locale() {\n    return this.state.locale;\n  }\n  get isFallback() {\n    return this.state.isFallback;\n  }\n  get isPreview() {\n    return this.state.isPreview;\n  }\n  constructor(pathname, query, as, {\n    initialProps,\n    pageLoader,\n    App,\n    wrapApp,\n    Component,\n    err,\n    subscription,\n    isFallback,\n    locale,\n    locales,\n    defaultLocale,\n    domainLocales,\n    isPreview\n  }) {\n    // Server Data Cache (full data requests)\n    this.sdc = {};\n    // Server Background Cache (HEAD requests)\n    this.sbc = {};\n    this.isFirstPopStateEvent = true;\n    this._key = createKey();\n    this.onPopState = e => {\n      const {\n        isFirstPopStateEvent\n      } = this;\n      this.isFirstPopStateEvent = false;\n      const state = e.state;\n      if (!state) {\n        // We get state as undefined for two reasons.\n        //  1. With older safari (< 8) and older chrome (< 34)\n        //  2. When the URL changed with #\n        //\n        // In the both cases, we don't need to proceed and change the route.\n        // (as it's already changed)\n        // But we can simply replace the state with the new changes.\n        // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n        // So, doing the following for (1) does no harm.\n        const {\n          pathname,\n          query\n        } = this;\n        this.changeState('replaceState', (0, _formaturl.formatWithValidation)({\n          pathname: (0, _addbasepath.addBasePath)(pathname),\n          query\n        }), (0, _utils.getURL)());\n        return;\n      }\n      // __NA is used to identify if the history entry can be handled by the app-router.\n      if (state.__NA) {\n        window.location.reload();\n        return;\n      }\n      if (!state.__N) {\n        return;\n      }\n      // Safari fires popstateevent when reopening the browser.\n      if (isFirstPopStateEvent && this.locale === state.options.locale && state.as === this.asPath) {\n        return;\n      }\n      let forcedScroll;\n      const {\n        url,\n        as,\n        options,\n        key\n      } = state;\n      if (false) {}\n      this._key = key;\n      const {\n        pathname\n      } = (0, _parserelativeurl.parseRelativeUrl)(url);\n      // Make sure we don't re-render on initial load,\n      // can be caused by navigating back from an external site\n      if (this.isSsr && as === (0, _addbasepath.addBasePath)(this.asPath) && pathname === (0, _addbasepath.addBasePath)(this.pathname)) {\n        return;\n      }\n      // If the downstream application returns falsy, return.\n      // They will then be responsible for handling the event.\n      if (this._bps && !this._bps(state)) {\n        return;\n      }\n      this.change('replaceState', url, as, Object.assign({}, options, {\n        shallow: options.shallow && this._shallow,\n        locale: options.locale || this.defaultLocale,\n        // @ts-ignore internal value not exposed on types\n        _h: 0\n      }), forcedScroll);\n    };\n    // represents the current component key\n    const route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n    // set up the component cache (by route keys)\n    this.components = {};\n    // We should not keep the cache, if there's an error\n    // Otherwise, this cause issues when when going back and\n    // come again to the errored page.\n    if (pathname !== '/_error') {\n      this.components[route] = {\n        Component,\n        initial: true,\n        props: initialProps,\n        err,\n        __N_SSG: initialProps && initialProps.__N_SSG,\n        __N_SSP: initialProps && initialProps.__N_SSP\n      };\n    }\n    this.components['/_app'] = {\n      Component: App,\n      styleSheets: []\n    };\n    // Backwards compat for Router.router.events\n    // TODO: Should be remove the following major version as it was never documented\n    this.events = Router.events;\n    this.pageLoader = pageLoader;\n    // if auto prerendered and dynamic route wait to update asPath\n    // until after mount to prevent hydration mismatch\n    const autoExportDynamic = (0, _isdynamic.isDynamicRoute)(pathname) && self.__NEXT_DATA__.autoExport;\n    this.basePath =  false || '';\n    this.sub = subscription;\n    this.clc = null;\n    this._wrapApp = wrapApp;\n    // make sure to ignore extra popState in safari on navigating\n    // back from external site\n    this.isSsr = true;\n    this.isLocaleDomain = false;\n    this.isReady = !!(self.__NEXT_DATA__.gssp || self.__NEXT_DATA__.gip || self.__NEXT_DATA__.isExperimentalCompile || self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp || !autoExportDynamic && !self.location.search && !false);\n    if (false) {}\n    this.state = {\n      route,\n      pathname,\n      query,\n      asPath: autoExportDynamic ? pathname : as,\n      isPreview: !!isPreview,\n      locale:  false ? 0 : undefined,\n      isFallback\n    };\n    this._initialMatchesMiddlewarePromise = Promise.resolve(false);\n    if (true) {\n      // make sure \"as\" doesn't start with double slashes or else it can\n      // throw an error as it's considered invalid\n      if (!as.startsWith('//')) {\n        // in order for `e.state` to work on the `onpopstate` event\n        // we have to register the initial route upon initialization\n        const options = {\n          locale\n        };\n        const asPath = (0, _utils.getURL)();\n        this._initialMatchesMiddlewarePromise = matchesMiddleware({\n          router: this,\n          locale,\n          asPath\n        }).then(matches => {\n          // if middleware matches we leave resolving to the change function\n          // as the server needs to resolve for correct priority\n          ;\n          options._shouldResolveHref = as !== pathname;\n          this.changeState('replaceState', matches ? asPath : (0, _formaturl.formatWithValidation)({\n            pathname: (0, _addbasepath.addBasePath)(pathname),\n            query\n          }), asPath, options);\n          return matches;\n        });\n      }\n      window.addEventListener('popstate', this.onPopState);\n      // enable custom scroll restoration handling when available\n      // otherwise fallback to browser's default handling\n      if (false) {}\n    }\n  }\n}\nRouter.events = (0, _mitt.default)();\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvcm91dGVyLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ2E7O0FBQ2JBLDhDQUE2QztFQUN6Q0csS0FBSyxFQUFFO0FBQ1gsQ0FBQyxFQUFDO0FBQ0YsQ0FBQyxLQUFLQyxDQUlMLENBQUM7QUFDRixTQUFTSSxPQUFPQSxDQUFDQyxNQUFNLEVBQUVDLEdBQUcsRUFBRTtFQUMxQixLQUFJLElBQUlDLElBQUksSUFBSUQsR0FBRyxFQUFDVixNQUFNLENBQUNDLGNBQWMsQ0FBQ1EsTUFBTSxFQUFFRSxJQUFJLEVBQUU7SUFDcERDLFVBQVUsRUFBRSxJQUFJO0lBQ2hCQyxHQUFHLEVBQUVILEdBQUcsQ0FBQ0MsSUFBSTtFQUNqQixDQUFDLENBQUM7QUFDTjtBQUNBSCxPQUFPLENBQUNOLE9BQU8sRUFBRTtFQUNiRyxTQUFTLEVBQUUsU0FBQUEsQ0FBQSxFQUFXO0lBQ2xCLE9BQU9BLFNBQVM7RUFDcEIsQ0FBQztFQUNEQyxPQUFPLEVBQUUsU0FBQUEsQ0FBQSxFQUFXO0lBQ2hCLE9BQU9RLE1BQU07RUFDakIsQ0FBQztFQUNEUCxpQkFBaUIsRUFBRSxTQUFBQSxDQUFBLEVBQVc7SUFDMUIsT0FBT0EsaUJBQWlCO0VBQzVCO0FBQ0osQ0FBQyxDQUFDO0FBQ0YsTUFBTVEsd0JBQXdCLEdBQUdDLG1CQUFPLENBQUMsZ0lBQXlDLENBQUM7QUFDbkYsTUFBTUMseUJBQXlCLEdBQUdELG1CQUFPLENBQUMsa0lBQTBDLENBQUM7QUFDckYsTUFBTUUsb0JBQW9CLEdBQUdGLG1CQUFPLENBQUMsb0lBQStCLENBQUM7QUFDckUsTUFBTUcsWUFBWSxHQUFHSCxtQkFBTyxDQUFDLHlHQUE4QixDQUFDO0FBQzVELE1BQU1JLE9BQU8sR0FBR0osbUJBQU8sQ0FBQyw2RkFBd0IsQ0FBQztBQUNqRCxNQUFNSyxRQUFRLEdBQUcsYUFBY0oseUJBQXlCLENBQUNLLENBQUMsQ0FBQ04sbUJBQU8sQ0FBQywyRkFBdUIsQ0FBQyxDQUFDO0FBQzVGLE1BQU1PLG9CQUFvQixHQUFHUCxtQkFBTyxDQUFDLHNJQUFvQyxDQUFDO0FBQzFFLE1BQU1RLG9CQUFvQixHQUFHUixtQkFBTyxDQUFDLDRIQUErQixDQUFDO0FBQ3JFLE1BQU1TLEtBQUssR0FBRyxhQUFjVix3QkFBd0IsQ0FBQ08sQ0FBQyxDQUFDTixtQkFBTyxDQUFDLGdGQUFTLENBQUMsQ0FBQztBQUMxRSxNQUFNVSxNQUFNLEdBQUdWLG1CQUFPLENBQUMsa0ZBQVUsQ0FBQztBQUNsQyxNQUFNVyxVQUFVLEdBQUdYLG1CQUFPLENBQUMsOEdBQW9CLENBQUM7QUFDaEQsTUFBTVksaUJBQWlCLEdBQUdaLG1CQUFPLENBQUMsOEhBQTRCLENBQUM7QUFDL0QsTUFBTWEsZ0JBQWdCLEdBQUcsYUFBY2Qsd0JBQXdCLENBQUNPLENBQUMsQ0FBQ04sbUJBQU8sQ0FBQyx1Q0FBMEIsQ0FBQyxDQUFDO0FBQ3RHLE1BQU1jLGFBQWEsR0FBR2QsbUJBQU8sQ0FBQyxvSEFBdUIsQ0FBQztBQUN0RCxNQUFNZSxXQUFXLEdBQUdmLG1CQUFPLENBQUMsZ0hBQXFCLENBQUM7QUFDbEQsTUFBTWdCLFVBQVUsR0FBR2hCLG1CQUFPLENBQUMsOEdBQW9CLENBQUM7QUFDaEQsTUFBTWlCLG1CQUFtQixHQUFHakIsbUJBQU8sQ0FBQyx5SEFBc0MsQ0FBQztBQUMzRSxNQUFNa0IsVUFBVSxHQUFHbEIsbUJBQU8sQ0FBQyw4R0FBb0IsQ0FBQztBQUNoRCxNQUFNbUIsVUFBVSxHQUFHbkIsbUJBQU8sQ0FBQyxxR0FBNEIsQ0FBQztBQUN4RCxNQUFNb0IsYUFBYSxHQUFHcEIsbUJBQU8sQ0FBQywyR0FBK0IsQ0FBQztBQUM5RCxNQUFNcUIsZUFBZSxHQUFHckIsbUJBQU8sQ0FBQyxpSEFBa0MsQ0FBQztBQUNuRSxNQUFNc0IsWUFBWSxHQUFHdEIsbUJBQU8sQ0FBQywyR0FBK0IsQ0FBQztBQUM3RCxNQUFNdUIsWUFBWSxHQUFHdkIsbUJBQU8sQ0FBQywyR0FBK0IsQ0FBQztBQUM3RCxNQUFNd0IsWUFBWSxHQUFHeEIsbUJBQU8sQ0FBQyx5R0FBOEIsQ0FBQztBQUM1RCxNQUFNeUIsV0FBVyxHQUFHekIsbUJBQU8sQ0FBQyxtR0FBMkIsQ0FBQztBQUN4RCxNQUFNMEIsb0JBQW9CLEdBQUcxQixtQkFBTyxDQUFDLHNJQUFnQyxDQUFDO0FBQ3RFLE1BQU0yQix1QkFBdUIsR0FBRzNCLG1CQUFPLENBQUMsNElBQW1DLENBQUM7QUFDNUUsTUFBTTRCLGNBQWMsR0FBRzVCLG1CQUFPLENBQUMsc0hBQXdCLENBQUM7QUFDeEQsTUFBTTZCLFdBQVcsR0FBRzdCLG1CQUFPLENBQUMsa0hBQXNCLENBQUM7QUFDbkQsTUFBTThCLE1BQU0sR0FBRzlCLG1CQUFPLENBQUMsc0dBQWdCLENBQUM7QUFDeEMsTUFBTStCLEtBQUssR0FBRy9CLG1CQUFPLENBQUMsa0dBQWMsQ0FBQztBQUNyQyxNQUFNZ0MsY0FBYyxHQUFHaEMsbUJBQU8sQ0FBQyxzSEFBd0IsQ0FBQztBQUN4RCxNQUFNaUMsbUJBQW1CLEdBQUdqQyxtQkFBTyxDQUFDLGtJQUE4QixDQUFDO0FBQ25FLE1BQU1rQyxVQUFVLEdBQUdsQyxtQkFBTyxDQUFDLDZGQUF3QixDQUFDO0FBQ3BELFNBQVNtQyxzQkFBc0JBLENBQUEsRUFBRztFQUM5QixPQUFPbkQsTUFBTSxDQUFDb0QsTUFBTSxDQUFDcEQsTUFBTSxDQUFDQyxjQUFjLENBQUMsSUFBSW9ELEtBQUssQ0FBQyxpQkFBaUIsQ0FBQyxFQUFFLG1CQUFtQixFQUFFO0lBQzFGbEQsS0FBSyxFQUFFLE1BQU07SUFDYlMsVUFBVSxFQUFFLEtBQUs7SUFDakIwQyxZQUFZLEVBQUU7RUFDbEIsQ0FBQyxDQUFDLEVBQUU7SUFDQUMsU0FBUyxFQUFFO0VBQ2YsQ0FBQyxDQUFDO0FBQ047QUFDQSxlQUFlaEQsaUJBQWlCQSxDQUFDaUQsT0FBTyxFQUFFO0VBQ3RDLE1BQU1DLFFBQVEsR0FBRyxNQUFNQyxPQUFPLENBQUNDLE9BQU8sQ0FBQ0gsT0FBTyxDQUFDSSxNQUFNLENBQUNDLFVBQVUsQ0FBQ0MsYUFBYSxDQUFDLENBQUMsQ0FBQztFQUNqRixJQUFJLENBQUNMLFFBQVEsRUFBRSxPQUFPLEtBQUs7RUFDM0IsTUFBTTtJQUFFTSxRQUFRLEVBQUVDO0VBQVcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFOUIsVUFBVSxDQUFDK0IsU0FBUyxFQUFFVCxPQUFPLENBQUNVLE1BQU0sQ0FBQztFQUMxRTtFQUNBLE1BQU1DLFNBQVMsR0FBRyxDQUFDLENBQUMsRUFBRTVCLFlBQVksQ0FBQzZCLFdBQVcsRUFBRUosVUFBVSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUzQixlQUFlLENBQUNnQyxjQUFjLEVBQUVMLFVBQVUsQ0FBQyxHQUFHQSxVQUFVO0VBQzFILE1BQU1NLHVCQUF1QixHQUFHLENBQUMsQ0FBQyxFQUFFaEMsWUFBWSxDQUFDaUMsV0FBVyxFQUFFLENBQUMsQ0FBQyxFQUFFcEMsVUFBVSxDQUFDcUMsU0FBUyxFQUFFTCxTQUFTLEVBQUVYLE9BQU8sQ0FBQ2lCLE1BQU0sQ0FBQyxDQUFDO0VBQ25IO0VBQ0E7RUFDQSxPQUFPaEIsUUFBUSxDQUFDaUIsSUFBSSxDQUFFQyxDQUFDLElBQUcsSUFBSUMsTUFBTSxDQUFDRCxDQUFDLENBQUNFLE1BQU0sQ0FBQyxDQUFDQyxJQUFJLENBQUNSLHVCQUF1QixDQUFDLENBQUM7QUFDakY7QUFDQSxTQUFTUyxXQUFXQSxDQUFDQyxHQUFHLEVBQUU7RUFDdEIsTUFBTUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxFQUFFdkQsTUFBTSxDQUFDd0QsaUJBQWlCLEVBQUUsQ0FBQztFQUM5QyxPQUFPRixHQUFHLENBQUNHLFVBQVUsQ0FBQ0YsTUFBTSxDQUFDLEdBQUdELEdBQUcsQ0FBQ0ksU0FBUyxDQUFDSCxNQUFNLENBQUNJLE1BQU0sQ0FBQyxHQUFHTCxHQUFHO0FBQ3RFO0FBQ0EsU0FBU00sWUFBWUEsQ0FBQzFCLE1BQU0sRUFBRW9CLEdBQUcsRUFBRU8sRUFBRSxFQUFFO0VBQ25DO0VBQ0E7RUFDQSxJQUFJLENBQUNDLFlBQVksRUFBRUMsVUFBVSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUVqRCxZQUFZLENBQUNrRCxXQUFXLEVBQUU5QixNQUFNLEVBQUVvQixHQUFHLEVBQUUsSUFBSSxDQUFDO0VBQ2pGLE1BQU1DLE1BQU0sR0FBRyxDQUFDLENBQUMsRUFBRXZELE1BQU0sQ0FBQ3dELGlCQUFpQixFQUFFLENBQUM7RUFDOUMsTUFBTVMsZUFBZSxHQUFHSCxZQUFZLENBQUNMLFVBQVUsQ0FBQ0YsTUFBTSxDQUFDO0VBQ3ZELE1BQU1XLGFBQWEsR0FBR0gsVUFBVSxJQUFJQSxVQUFVLENBQUNOLFVBQVUsQ0FBQ0YsTUFBTSxDQUFDO0VBQ2pFTyxZQUFZLEdBQUdULFdBQVcsQ0FBQ1MsWUFBWSxDQUFDO0VBQ3hDQyxVQUFVLEdBQUdBLFVBQVUsR0FBR1YsV0FBVyxDQUFDVSxVQUFVLENBQUMsR0FBR0EsVUFBVTtFQUM5RCxNQUFNSSxXQUFXLEdBQUdGLGVBQWUsR0FBR0gsWUFBWSxHQUFHLENBQUMsQ0FBQyxFQUFFbEQsWUFBWSxDQUFDaUMsV0FBVyxFQUFFaUIsWUFBWSxDQUFDO0VBQ2hHLE1BQU1NLFVBQVUsR0FBR1AsRUFBRSxHQUFHUixXQUFXLENBQUMsQ0FBQyxDQUFDLEVBQUV2QyxZQUFZLENBQUNrRCxXQUFXLEVBQUU5QixNQUFNLEVBQUUyQixFQUFFLENBQUMsQ0FBQyxHQUFHRSxVQUFVLElBQUlELFlBQVk7RUFDM0csT0FBTztJQUNIUixHQUFHLEVBQUVhLFdBQVc7SUFDaEJOLEVBQUUsRUFBRUssYUFBYSxHQUFHRSxVQUFVLEdBQUcsQ0FBQyxDQUFDLEVBQUV4RCxZQUFZLENBQUNpQyxXQUFXLEVBQUV1QixVQUFVO0VBQzdFLENBQUM7QUFDTDtBQUNBLFNBQVNDLG1CQUFtQkEsQ0FBQ2hDLFFBQVEsRUFBRWlDLEtBQUssRUFBRTtFQUMxQyxNQUFNQyxhQUFhLEdBQUcsQ0FBQyxDQUFDLEVBQUUvRSxvQkFBb0IsQ0FBQ2dGLG1CQUFtQixFQUFFLENBQUMsQ0FBQyxFQUFFM0Usb0JBQW9CLENBQUM0RSxtQkFBbUIsRUFBRXBDLFFBQVEsQ0FBQyxDQUFDO0VBQzVILElBQUlrQyxhQUFhLEtBQUssTUFBTSxJQUFJQSxhQUFhLEtBQUssU0FBUyxFQUFFO0lBQ3pELE9BQU9sQyxRQUFRO0VBQ25CO0VBQ0E7RUFDQSxJQUFJLENBQUNpQyxLQUFLLENBQUNJLFFBQVEsQ0FBQ0gsYUFBYSxDQUFDLEVBQUU7SUFDaEM7SUFDQUQsS0FBSyxDQUFDdEIsSUFBSSxDQUFFMkIsSUFBSSxJQUFHO01BQ2YsSUFBSSxDQUFDLENBQUMsRUFBRTFFLFVBQVUsQ0FBQzJFLGNBQWMsRUFBRUQsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLEVBQUV0RSxXQUFXLENBQUN3RSxhQUFhLEVBQUVGLElBQUksQ0FBQyxDQUFDRyxFQUFFLENBQUMxQixJQUFJLENBQUNtQixhQUFhLENBQUMsRUFBRTtRQUNyR2xDLFFBQVEsR0FBR3NDLElBQUk7UUFDZixPQUFPLElBQUk7TUFDZjtJQUNKLENBQUMsQ0FBQztFQUNOO0VBQ0EsT0FBTyxDQUFDLENBQUMsRUFBRW5GLG9CQUFvQixDQUFDZ0YsbUJBQW1CLEVBQUVuQyxRQUFRLENBQUM7QUFDbEU7QUFDQSxTQUFTMEMsaUJBQWlCQSxDQUFDQyxNQUFNLEVBQUVDLFFBQVEsRUFBRW5ELE9BQU8sRUFBRTtFQUNsRCxNQUFNb0QsVUFBVSxHQUFHO0lBQ2ZDLFFBQVEsRUFBRXJELE9BQU8sQ0FBQ0ksTUFBTSxDQUFDaUQsUUFBUTtJQUNqQ0MsSUFBSSxFQUFFO01BQ0ZDLE9BQU8sRUFBRXZELE9BQU8sQ0FBQ0ksTUFBTSxDQUFDbUQ7SUFDNUIsQ0FBQztJQUNEQyxhQUFhLEVBQUVDLE9BQU8sQ0FBQ0MsS0FBaUM7RUFDNUQsQ0FBQztFQUNELE1BQU1HLGFBQWEsR0FBR1YsUUFBUSxDQUFDVyxPQUFPLENBQUN6RyxHQUFHLENBQUMsa0JBQWtCLENBQUM7RUFDOUQsSUFBSTBHLGFBQWEsR0FBR0YsYUFBYSxJQUFJVixRQUFRLENBQUNXLE9BQU8sQ0FBQ3pHLEdBQUcsQ0FBQyx1QkFBdUIsQ0FBQztFQUNsRixNQUFNMkcsV0FBVyxHQUFHYixRQUFRLENBQUNXLE9BQU8sQ0FBQ3pHLEdBQUcsQ0FBQ3FDLFVBQVUsQ0FBQ3VFLG1CQUFtQixDQUFDO0VBQ3hFLElBQUlELFdBQVcsSUFBSSxDQUFDRCxhQUFhLElBQUksQ0FBQ0MsV0FBVyxDQUFDcEIsUUFBUSxDQUFDLHNCQUFzQixDQUFDLElBQUksQ0FBQ29CLFdBQVcsQ0FBQ3BCLFFBQVEsQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDb0IsV0FBVyxDQUFDcEIsUUFBUSxDQUFDLE1BQU0sQ0FBQyxFQUFFO0lBQ3JKO0lBQ0FtQixhQUFhLEdBQUdDLFdBQVc7RUFDL0I7RUFDQSxJQUFJRCxhQUFhLEVBQUU7SUFDZixJQUFJQSxhQUFhLENBQUNwQyxVQUFVLENBQUMsR0FBRyxDQUFDLElBQUkrQixLQUFzRCxFQUFFO01BQ3pGLE1BQU1TLG1CQUFtQixHQUFHLENBQUMsQ0FBQyxFQUFFL0YsaUJBQWlCLENBQUNnRyxnQkFBZ0IsRUFBRUwsYUFBYSxDQUFDO01BQ2xGLE1BQU1NLFlBQVksR0FBRyxDQUFDLENBQUMsRUFBRW5GLG9CQUFvQixDQUFDb0YsbUJBQW1CLEVBQUVILG1CQUFtQixDQUFDNUQsUUFBUSxFQUFFO1FBQzdGNkMsVUFBVTtRQUNWbUIsU0FBUyxFQUFFO01BQ2YsQ0FBQyxDQUFDO01BQ0YsSUFBSUMsVUFBVSxHQUFHLENBQUMsQ0FBQyxFQUFFOUcsb0JBQW9CLENBQUNnRixtQkFBbUIsRUFBRTJCLFlBQVksQ0FBQzlELFFBQVEsQ0FBQztNQUNyRixPQUFPTCxPQUFPLENBQUNoRCxHQUFHLENBQUMsQ0FDZjhDLE9BQU8sQ0FBQ0ksTUFBTSxDQUFDQyxVQUFVLENBQUNvRSxXQUFXLENBQUMsQ0FBQyxFQUN2QyxDQUFDLENBQUMsRUFBRTlHLFlBQVksQ0FBQytHLHNCQUFzQixFQUFFLENBQUMsQ0FDN0MsQ0FBQyxDQUFDQyxJQUFJLENBQUVDLEtBQUssSUFBRztRQUNiLElBQUksQ0FBQ3BDLEtBQUssRUFBRTtVQUFFcUMsVUFBVSxFQUFFQztRQUFTLENBQUMsQ0FBQyxHQUFHRixLQUFLO1FBQzdDLElBQUk3QyxFQUFFLEdBQUcsQ0FBQyxDQUFDLEVBQUVwRCxVQUFVLENBQUNxQyxTQUFTLEVBQUVxRCxZQUFZLENBQUM5RCxRQUFRLEVBQUU4RCxZQUFZLENBQUNwRCxNQUFNLENBQUM7UUFDOUUsSUFBSSxDQUFDLENBQUMsRUFBRTlDLFVBQVUsQ0FBQzJFLGNBQWMsRUFBRWYsRUFBRSxDQUFDLElBQUksQ0FBQzhCLGFBQWEsSUFBSXJCLEtBQUssQ0FBQ0ksUUFBUSxDQUFDLENBQUMsQ0FBQyxFQUFFNUUsb0JBQW9CLENBQUMrRyxtQkFBbUIsRUFBRSxDQUFDLENBQUMsRUFBRWxHLGVBQWUsQ0FBQ2dDLGNBQWMsRUFBRWtCLEVBQUUsQ0FBQyxFQUFFL0IsT0FBTyxDQUFDSSxNQUFNLENBQUNtRCxPQUFPLENBQUMsQ0FBQ2hELFFBQVEsQ0FBQyxFQUFFO1VBQ2pNLE1BQU15RSxZQUFZLEdBQUcsQ0FBQyxDQUFDLEVBQUU5RixvQkFBb0IsQ0FBQ29GLG1CQUFtQixFQUFFLENBQUMsQ0FBQyxFQUFFbEcsaUJBQWlCLENBQUNnRyxnQkFBZ0IsRUFBRWxCLE1BQU0sQ0FBQyxDQUFDM0MsUUFBUSxFQUFFO1lBQ3pINkMsVUFBVSxFQUFFTSxNQUErQixHQUFHd0IsQ0FBUyxHQUFHOUIsVUFBVTtZQUNwRW1CLFNBQVMsRUFBRTtVQUNmLENBQUMsQ0FBQztVQUNGeEMsRUFBRSxHQUFHLENBQUMsQ0FBQyxFQUFFakQsWUFBWSxDQUFDaUMsV0FBVyxFQUFFaUUsWUFBWSxDQUFDekUsUUFBUSxDQUFDO1VBQ3pENEQsbUJBQW1CLENBQUM1RCxRQUFRLEdBQUd3QixFQUFFO1FBQ3JDO1FBQ0EsSUFBSTJCLEtBQStCLEVBQUUsRUFPcEMsTUFBTSxJQUFJLENBQUNsQixLQUFLLENBQUNJLFFBQVEsQ0FBQzRCLFVBQVUsQ0FBQyxFQUFFO1VBQ3BDLE1BQU1nQixnQkFBZ0IsR0FBR2pELG1CQUFtQixDQUFDaUMsVUFBVSxFQUFFaEMsS0FBSyxDQUFDO1VBQy9ELElBQUlnRCxnQkFBZ0IsS0FBS2hCLFVBQVUsRUFBRTtZQUNqQ0EsVUFBVSxHQUFHZ0IsZ0JBQWdCO1VBQ2pDO1FBQ0o7UUFDQSxNQUFNeEQsWUFBWSxHQUFHLENBQUNRLEtBQUssQ0FBQ0ksUUFBUSxDQUFDNEIsVUFBVSxDQUFDLEdBQUdqQyxtQkFBbUIsQ0FBQyxDQUFDLENBQUMsRUFBRXZFLG9CQUFvQixDQUFDK0csbUJBQW1CLEVBQUUsQ0FBQyxDQUFDLEVBQUVsRyxlQUFlLENBQUNnQyxjQUFjLEVBQUVzRCxtQkFBbUIsQ0FBQzVELFFBQVEsQ0FBQyxFQUFFUCxPQUFPLENBQUNJLE1BQU0sQ0FBQ21ELE9BQU8sQ0FBQyxDQUFDaEQsUUFBUSxFQUFFaUMsS0FBSyxDQUFDLEdBQUdnQyxVQUFVO1FBQzdPLElBQUksQ0FBQyxDQUFDLEVBQUVyRyxVQUFVLENBQUMyRSxjQUFjLEVBQUVkLFlBQVksQ0FBQyxFQUFFO1VBQzlDLE1BQU15RCxPQUFPLEdBQUcsQ0FBQyxDQUFDLEVBQUVuSCxhQUFhLENBQUNvSCxlQUFlLEVBQUUsQ0FBQyxDQUFDLEVBQUVuSCxXQUFXLENBQUN3RSxhQUFhLEVBQUVmLFlBQVksQ0FBQyxDQUFDLENBQUNELEVBQUUsQ0FBQztVQUNwR3ZGLE1BQU0sQ0FBQ29ELE1BQU0sQ0FBQ3VFLG1CQUFtQixDQUFDaUIsS0FBSyxFQUFFSyxPQUFPLElBQUksQ0FBQyxDQUFDLENBQUM7UUFDM0Q7UUFDQSxPQUFPO1VBQ0hFLElBQUksRUFBRSxTQUFTO1VBQ2ZKLFFBQVEsRUFBRXBCLG1CQUFtQjtVQUM3Qm5DO1FBQ0osQ0FBQztNQUNMLENBQUMsQ0FBQztJQUNOO0lBQ0EsTUFBTTRELEdBQUcsR0FBRyxDQUFDLENBQUMsRUFBRWxILFVBQVUsQ0FBQytCLFNBQVMsRUFBRXlDLE1BQU0sQ0FBQztJQUM3QyxNQUFNM0MsUUFBUSxHQUFHLENBQUMsQ0FBQyxFQUFFcEIsdUJBQXVCLENBQUMwRyxzQkFBc0IsRUFBRTtNQUNqRSxHQUFHLENBQUMsQ0FBQyxFQUFFM0csb0JBQW9CLENBQUNvRixtQkFBbUIsRUFBRXNCLEdBQUcsQ0FBQ3JGLFFBQVEsRUFBRTtRQUMzRDZDLFVBQVU7UUFDVm1CLFNBQVMsRUFBRTtNQUNmLENBQUMsQ0FBQztNQUNGdUIsYUFBYSxFQUFFOUYsT0FBTyxDQUFDSSxNQUFNLENBQUMwRixhQUFhO01BQzNDQyxPQUFPLEVBQUU7SUFDYixDQUFDLENBQUM7SUFDRixPQUFPN0YsT0FBTyxDQUFDQyxPQUFPLENBQUM7TUFDbkJ3RixJQUFJLEVBQUUsbUJBQW1CO01BQ3pCSyxXQUFXLEVBQUUsRUFBRSxHQUFHekYsUUFBUSxHQUFHcUYsR0FBRyxDQUFDUixLQUFLLEdBQUdRLEdBQUcsQ0FBQ0s7SUFDakQsQ0FBQyxDQUFDO0VBQ047RUFDQSxNQUFNQyxjQUFjLEdBQUcvQyxRQUFRLENBQUNXLE9BQU8sQ0FBQ3pHLEdBQUcsQ0FBQyxtQkFBbUIsQ0FBQztFQUNoRSxJQUFJNkksY0FBYyxFQUFFO0lBQ2hCLElBQUlBLGNBQWMsQ0FBQ3ZFLFVBQVUsQ0FBQyxHQUFHLENBQUMsRUFBRTtNQUNoQyxNQUFNaUUsR0FBRyxHQUFHLENBQUMsQ0FBQyxFQUFFbEgsVUFBVSxDQUFDK0IsU0FBUyxFQUFFeUYsY0FBYyxDQUFDO01BQ3JELE1BQU0zRixRQUFRLEdBQUcsQ0FBQyxDQUFDLEVBQUVwQix1QkFBdUIsQ0FBQzBHLHNCQUFzQixFQUFFO1FBQ2pFLEdBQUcsQ0FBQyxDQUFDLEVBQUUzRyxvQkFBb0IsQ0FBQ29GLG1CQUFtQixFQUFFc0IsR0FBRyxDQUFDckYsUUFBUSxFQUFFO1VBQzNENkMsVUFBVTtVQUNWbUIsU0FBUyxFQUFFO1FBQ2YsQ0FBQyxDQUFDO1FBQ0Z1QixhQUFhLEVBQUU5RixPQUFPLENBQUNJLE1BQU0sQ0FBQzBGLGFBQWE7UUFDM0NDLE9BQU8sRUFBRTtNQUNiLENBQUMsQ0FBQztNQUNGLE9BQU83RixPQUFPLENBQUNDLE9BQU8sQ0FBQztRQUNuQndGLElBQUksRUFBRSxtQkFBbUI7UUFDekJRLEtBQUssRUFBRSxFQUFFLEdBQUc1RixRQUFRLEdBQUdxRixHQUFHLENBQUNSLEtBQUssR0FBR1EsR0FBRyxDQUFDSyxJQUFJO1FBQzNDRyxNQUFNLEVBQUUsRUFBRSxHQUFHN0YsUUFBUSxHQUFHcUYsR0FBRyxDQUFDUixLQUFLLEdBQUdRLEdBQUcsQ0FBQ0s7TUFDNUMsQ0FBQyxDQUFDO0lBQ047SUFDQSxPQUFPL0YsT0FBTyxDQUFDQyxPQUFPLENBQUM7TUFDbkJ3RixJQUFJLEVBQUUsbUJBQW1CO01BQ3pCSyxXQUFXLEVBQUVFO0lBQ2pCLENBQUMsQ0FBQztFQUNOO0VBQ0EsT0FBT2hHLE9BQU8sQ0FBQ0MsT0FBTyxDQUFDO0lBQ25Cd0YsSUFBSSxFQUFFO0VBQ1YsQ0FBQyxDQUFDO0FBQ047QUFDQSxlQUFlVSxxQkFBcUJBLENBQUNyRyxPQUFPLEVBQUU7RUFDMUMsTUFBTXlGLE9BQU8sR0FBRyxNQUFNMUksaUJBQWlCLENBQUNpRCxPQUFPLENBQUM7RUFDaEQsSUFBSSxDQUFDeUYsT0FBTyxJQUFJLENBQUN6RixPQUFPLENBQUNzRyxTQUFTLEVBQUU7SUFDaEMsT0FBTyxJQUFJO0VBQ2Y7RUFDQSxNQUFNQyxJQUFJLEdBQUcsTUFBTXZHLE9BQU8sQ0FBQ3NHLFNBQVMsQ0FBQyxDQUFDO0VBQ3RDLE1BQU1FLE1BQU0sR0FBRyxNQUFNdkQsaUJBQWlCLENBQUNzRCxJQUFJLENBQUNFLFFBQVEsRUFBRUYsSUFBSSxDQUFDcEQsUUFBUSxFQUFFbkQsT0FBTyxDQUFDO0VBQzdFLE9BQU87SUFDSHlHLFFBQVEsRUFBRUYsSUFBSSxDQUFDRSxRQUFRO0lBQ3ZCQyxJQUFJLEVBQUVILElBQUksQ0FBQ0csSUFBSTtJQUNmdkQsUUFBUSxFQUFFb0QsSUFBSSxDQUFDcEQsUUFBUTtJQUN2QndELElBQUksRUFBRUosSUFBSSxDQUFDSSxJQUFJO0lBQ2ZDLFFBQVEsRUFBRUwsSUFBSSxDQUFDSyxRQUFRO0lBQ3ZCSjtFQUNKLENBQUM7QUFDTDtBQUNBLE1BQU1LLHVCQUF1QixHQUFHbkQsTUFBK0csSUFBSSxDQU1oSjtBQUNILE1BQU00RCxrQkFBa0IsR0FBR0MsTUFBTSxDQUFDLG9CQUFvQixDQUFDO0FBQ3ZELFNBQVNDLFVBQVVBLENBQUNoRyxHQUFHLEVBQUVpRyxRQUFRLEVBQUV6SCxPQUFPLEVBQUU7RUFDeEMsT0FBTzBILEtBQUssQ0FBQ2xHLEdBQUcsRUFBRTtJQUNkO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQW1HLFdBQVcsRUFBRSxhQUFhO0lBQzFCQyxNQUFNLEVBQUU1SCxPQUFPLENBQUM0SCxNQUFNLElBQUksS0FBSztJQUMvQjlELE9BQU8sRUFBRXRILE1BQU0sQ0FBQ29ELE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRUksT0FBTyxDQUFDOEQsT0FBTyxFQUFFO01BQ3hDLGVBQWUsRUFBRTtJQUNyQixDQUFDO0VBQ0wsQ0FBQyxDQUFDLENBQUNhLElBQUksQ0FBRXhCLFFBQVEsSUFBRztJQUNoQixPQUFPLENBQUNBLFFBQVEsQ0FBQzBFLEVBQUUsSUFBSUosUUFBUSxHQUFHLENBQUMsSUFBSXRFLFFBQVEsQ0FBQzJFLE1BQU0sSUFBSSxHQUFHLEdBQUdOLFVBQVUsQ0FBQ2hHLEdBQUcsRUFBRWlHLFFBQVEsR0FBRyxDQUFDLEVBQUV6SCxPQUFPLENBQUMsR0FBR21ELFFBQVE7RUFDckgsQ0FBQyxDQUFDO0FBQ047QUFDQSxTQUFTNEUsZ0JBQWdCQSxDQUFDcEIsSUFBSSxFQUFFO0VBQzVCLElBQUk7SUFDQSxPQUFPcUIsSUFBSSxDQUFDQyxLQUFLLENBQUN0QixJQUFJLENBQUM7RUFDM0IsQ0FBQyxDQUFDLE9BQU91QixLQUFLLEVBQUU7SUFDWixPQUFPLElBQUk7RUFDZjtBQUNKO0FBQ0EsU0FBU0MsYUFBYUEsQ0FBQ3ZELEtBQUssRUFBRTtFQUMxQixJQUFJO0lBQUU2QixRQUFRO0lBQUUyQixhQUFhO0lBQUVDLFVBQVU7SUFBRUMsYUFBYTtJQUFFQyxjQUFjO0lBQUVDLFNBQVM7SUFBRUMsWUFBWTtJQUFFQyxZQUFZO0lBQUVDO0VBQXlCLENBQUMsR0FBRy9ELEtBQUs7RUFDbkosTUFBTTtJQUFFZ0UsSUFBSSxFQUFFaEM7RUFBUyxDQUFDLEdBQUcsSUFBSWlDLEdBQUcsQ0FBQ3BDLFFBQVEsRUFBRU0sTUFBTSxDQUFDK0IsUUFBUSxDQUFDRixJQUFJLENBQUM7RUFDbEUsTUFBTUcsT0FBTyxHQUFJQyxNQUFNLElBQUc7SUFDdEIsSUFBSUMsY0FBYztJQUNsQixPQUFPekIsVUFBVSxDQUFDZixRQUFRLEVBQUU4QixjQUFjLEdBQUcsQ0FBQyxHQUFHLENBQUMsRUFBRTtNQUNoRHpFLE9BQU8sRUFBRXRILE1BQU0sQ0FBQ29ELE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRXlJLFVBQVUsR0FBRztRQUNwQ2EsT0FBTyxFQUFFO01BQ2IsQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFYixVQUFVLElBQUlDLGFBQWEsR0FBRztRQUNsQyx1QkFBdUIsRUFBRTtNQUM3QixDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUU1RSxNQUE4QixHQUFHLENBRXhDLEdBQUcsQ0FBQyxDQUFDLENBQUM7TUFDUGtFLE1BQU0sRUFBRSxDQUFDcUIsY0FBYyxHQUFHRCxNQUFNLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHQSxNQUFNLENBQUNwQixNQUFNLEtBQUssSUFBSSxHQUFHcUIsY0FBYyxHQUFHO0lBQ2xHLENBQUMsQ0FBQyxDQUFDdEUsSUFBSSxDQUFFeEIsUUFBUSxJQUFHO01BQ2hCLElBQUlBLFFBQVEsQ0FBQzBFLEVBQUUsSUFBSSxDQUFDbUIsTUFBTSxJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR0EsTUFBTSxDQUFDcEIsTUFBTSxNQUFNLE1BQU0sRUFBRTtRQUNyRSxPQUFPO1VBQ0huQixRQUFRO1VBQ1J0RCxRQUFRO1VBQ1J3RCxJQUFJLEVBQUUsRUFBRTtVQUNSRCxJQUFJLEVBQUUsQ0FBQyxDQUFDO1VBQ1JFO1FBQ0osQ0FBQztNQUNMO01BQ0EsT0FBT3pELFFBQVEsQ0FBQ3dELElBQUksQ0FBQyxDQUFDLENBQUNoQyxJQUFJLENBQUVnQyxJQUFJLElBQUc7UUFDaEMsSUFBSSxDQUFDeEQsUUFBUSxDQUFDMEUsRUFBRSxFQUFFO1VBQ2Q7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtVQUFnQixJQUFJUyxhQUFhLElBQUksQ0FDYixHQUFHLEVBQ0gsR0FBRyxFQUNILEdBQUcsRUFDSCxHQUFHLENBQ04sQ0FBQzFGLFFBQVEsQ0FBQ08sUUFBUSxDQUFDMkUsTUFBTSxDQUFDLEVBQUU7WUFDekIsT0FBTztjQUNIckIsUUFBUTtjQUNSdEQsUUFBUTtjQUNSd0QsSUFBSTtjQUNKRCxJQUFJLEVBQUUsQ0FBQyxDQUFDO2NBQ1JFO1lBQ0osQ0FBQztVQUNMO1VBQ0EsSUFBSXpELFFBQVEsQ0FBQzJFLE1BQU0sS0FBSyxHQUFHLEVBQUU7WUFDekIsSUFBSXNCLGlCQUFpQjtZQUNyQixJQUFJLENBQUNBLGlCQUFpQixHQUFHckIsZ0JBQWdCLENBQUNwQixJQUFJLENBQUMsS0FBSyxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUd5QyxpQkFBaUIsQ0FBQ0MsUUFBUSxFQUFFO2NBQzVGLE9BQU87Z0JBQ0g1QyxRQUFRO2dCQUNSQyxJQUFJLEVBQUU7a0JBQ0YyQyxRQUFRLEVBQUUvQjtnQkFDZCxDQUFDO2dCQUNEbkUsUUFBUTtnQkFDUndELElBQUk7Z0JBQ0pDO2NBQ0osQ0FBQztZQUNMO1VBQ0o7VUFDQSxNQUFNc0IsS0FBSyxHQUFHMUwsTUFBTSxDQUFDQyxjQUFjLENBQUMsSUFBSW9ELEtBQUssQ0FBQyw2QkFBNkIsQ0FBQyxFQUFFLG1CQUFtQixFQUFFO1lBQy9GbEQsS0FBSyxFQUFFLE1BQU07WUFDYlMsVUFBVSxFQUFFLEtBQUs7WUFDakIwQyxZQUFZLEVBQUU7VUFDbEIsQ0FBQyxDQUFDO1VBQ0Y7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7VUFBZ0IsSUFBSSxDQUFDeUksY0FBYyxFQUFFO1lBQ2IsQ0FBQyxDQUFDLEVBQUU1SyxZQUFZLENBQUMyTCxjQUFjLEVBQUVwQixLQUFLLENBQUM7VUFDM0M7VUFDQSxNQUFNQSxLQUFLO1FBQ2Y7UUFDQSxPQUFPO1VBQ0h6QixRQUFRO1VBQ1JDLElBQUksRUFBRThCLFNBQVMsR0FBR1QsZ0JBQWdCLENBQUNwQixJQUFJLENBQUMsR0FBRyxJQUFJO1VBQy9DeEQsUUFBUTtVQUNSd0QsSUFBSTtVQUNKQztRQUNKLENBQUM7TUFDTCxDQUFDLENBQUM7SUFDTixDQUFDLENBQUMsQ0FBQ2pDLElBQUksQ0FBRTRCLElBQUksSUFBRztNQUNaLElBQUksQ0FBQ2tDLFlBQVksUUFBeUMsSUFBSWxDLENBQThELEVBQUU7UUFDMUgsT0FBTzZCLGFBQWEsQ0FBQ3hCLFFBQVEsQ0FBQztNQUNsQztNQUNBLE9BQU9MLElBQUk7SUFDZixDQUFDLENBQUMsQ0FBQ2dELEtBQUssQ0FBRUMsR0FBRyxJQUFHO01BQ1osSUFBSSxDQUFDYix3QkFBd0IsRUFBRTtRQUMzQixPQUFPUCxhQUFhLENBQUN4QixRQUFRLENBQUM7TUFDbEM7TUFDQTtNQUFJO01BQ0o0QyxHQUFHLENBQUNDLE9BQU8sS0FBSyxpQkFBaUI7TUFBSTtNQUNyQ0QsR0FBRyxDQUFDQyxPQUFPLEtBQUssaURBQWlEO01BQUk7TUFDckVELEdBQUcsQ0FBQ0MsT0FBTyxLQUFLLGFBQWEsRUFBRTtRQUMzQixDQUFDLENBQUMsRUFBRTlMLFlBQVksQ0FBQzJMLGNBQWMsRUFBRUUsR0FBRyxDQUFDO01BQ3pDO01BQ0EsTUFBTUEsR0FBRztJQUNiLENBQUMsQ0FBQztFQUNOLENBQUM7RUFDRDtFQUNBO0VBQ0E7RUFDQTtFQUNBLElBQUliLHdCQUF3QixJQUFJRixZQUFZLEVBQUU7SUFDMUMsT0FBT00sT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUNwRSxJQUFJLENBQUU0QixJQUFJLElBQUc7TUFDNUIsSUFBSUEsSUFBSSxDQUFDcEQsUUFBUSxDQUFDVyxPQUFPLENBQUN6RyxHQUFHLENBQUMsb0JBQW9CLENBQUMsS0FBSyxVQUFVLEVBQUU7UUFDaEU7UUFDQStLLGFBQWEsQ0FBQ3hCLFFBQVEsQ0FBQyxHQUFHMUcsT0FBTyxDQUFDQyxPQUFPLENBQUNvRyxJQUFJLENBQUM7TUFDbkQ7TUFDQSxPQUFPQSxJQUFJO0lBQ2YsQ0FBQyxDQUFDO0VBQ047RUFDQSxJQUFJNkIsYUFBYSxDQUFDeEIsUUFBUSxDQUFDLEtBQUsxQixTQUFTLEVBQUU7SUFDdkMsT0FBT2tELGFBQWEsQ0FBQ3hCLFFBQVEsQ0FBQztFQUNsQztFQUNBLE9BQU93QixhQUFhLENBQUN4QixRQUFRLENBQUMsR0FBR21DLE9BQU8sQ0FBQ0wsWUFBWSxHQUFHO0lBQ3BEZCxNQUFNLEVBQUU7RUFDWixDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUM7QUFDWDtBQUNBLFNBQVMvSyxTQUFTQSxDQUFBLEVBQUc7RUFDakIsT0FBTzZNLElBQUksQ0FBQ0MsTUFBTSxDQUFDLENBQUMsQ0FBQ0MsUUFBUSxDQUFDLEVBQUUsQ0FBQyxDQUFDQyxLQUFLLENBQUMsQ0FBQyxFQUFFLEVBQUUsQ0FBQztBQUNsRDtBQUNBLFNBQVNDLG9CQUFvQkEsQ0FBQ2xGLEtBQUssRUFBRTtFQUNqQyxJQUFJO0lBQUVwRCxHQUFHO0lBQUVwQjtFQUFPLENBQUMsR0FBR3dFLEtBQUs7RUFDM0I7RUFDQTtFQUNBLElBQUlwRCxHQUFHLEtBQUssQ0FBQyxDQUFDLEVBQUUxQyxZQUFZLENBQUNpQyxXQUFXLEVBQUUsQ0FBQyxDQUFDLEVBQUVwQyxVQUFVLENBQUNxQyxTQUFTLEVBQUVaLE1BQU0sQ0FBQ00sTUFBTSxFQUFFTixNQUFNLENBQUNhLE1BQU0sQ0FBQyxDQUFDLEVBQUU7SUFDaEcsTUFBTXpFLE1BQU0sQ0FBQ0MsY0FBYyxDQUFDLElBQUlvRCxLQUFLLENBQUMsd0RBQXdELEdBQUcyQixHQUFHLEdBQUcsR0FBRyxHQUFHc0gsUUFBUSxDQUFDRixJQUFJLENBQUMsRUFBRSxtQkFBbUIsRUFBRTtNQUM5SWpNLEtBQUssRUFBRSxNQUFNO01BQ2JTLFVBQVUsRUFBRSxLQUFLO01BQ2pCMEMsWUFBWSxFQUFFO0lBQ2xCLENBQUMsQ0FBQztFQUNOO0VBQ0FpSCxNQUFNLENBQUMrQixRQUFRLENBQUNGLElBQUksR0FBR3BILEdBQUc7QUFDOUI7QUFDQSxNQUFNdUksbUJBQW1CLEdBQUluRixLQUFLLElBQUc7RUFDakMsSUFBSTtJQUFFb0YsS0FBSztJQUFFNUo7RUFBTyxDQUFDLEdBQUd3RSxLQUFLO0VBQzdCLElBQUk3RSxTQUFTLEdBQUcsS0FBSztFQUNyQixNQUFNa0ssTUFBTSxHQUFHN0osTUFBTSxDQUFDOEosR0FBRyxHQUFHLE1BQUk7SUFDNUJuSyxTQUFTLEdBQUcsSUFBSTtFQUNwQixDQUFDO0VBQ0QsTUFBTW9LLGVBQWUsR0FBR0EsQ0FBQSxLQUFJO0lBQ3hCLElBQUlwSyxTQUFTLEVBQUU7TUFDWCxNQUFNbUksS0FBSyxHQUFHMUwsTUFBTSxDQUFDQyxjQUFjLENBQUMsSUFBSW9ELEtBQUssQ0FBQyx1Q0FBdUMsR0FBR21LLEtBQUssR0FBRyxHQUFHLENBQUMsRUFBRSxtQkFBbUIsRUFBRTtRQUN2SHJOLEtBQUssRUFBRSxNQUFNO1FBQ2JTLFVBQVUsRUFBRSxLQUFLO1FBQ2pCMEMsWUFBWSxFQUFFO01BQ2xCLENBQUMsQ0FBQztNQUNGb0ksS0FBSyxDQUFDbkksU0FBUyxHQUFHLElBQUk7TUFDdEIsTUFBTW1JLEtBQUs7SUFDZjtJQUNBLElBQUkrQixNQUFNLEtBQUs3SixNQUFNLENBQUM4SixHQUFHLEVBQUU7TUFDdkI5SixNQUFNLENBQUM4SixHQUFHLEdBQUcsSUFBSTtJQUNyQjtFQUNKLENBQUM7RUFDRCxPQUFPQyxlQUFlO0FBQzFCLENBQUM7QUFDRCxNQUFNN00sTUFBTSxDQUFDO0VBQ1Q4TSxNQUFNQSxDQUFBLEVBQUc7SUFDTHJELE1BQU0sQ0FBQytCLFFBQVEsQ0FBQ3NCLE1BQU0sQ0FBQyxDQUFDO0VBQzVCO0VBQ0E7QUFDSjtBQUNBO0VBQU1DLElBQUlBLENBQUEsRUFBRztJQUNMdEQsTUFBTSxDQUFDQyxPQUFPLENBQUNxRCxJQUFJLENBQUMsQ0FBQztFQUN6QjtFQUNBO0FBQ0o7QUFDQTtFQUFNQyxPQUFPQSxDQUFBLEVBQUc7SUFDUnZELE1BQU0sQ0FBQ0MsT0FBTyxDQUFDc0QsT0FBTyxDQUFDLENBQUM7RUFDNUI7RUFDQTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7RUFBTUMsSUFBSUEsQ0FBQy9JLEdBQUcsRUFBRU8sRUFBRSxFQUFFL0IsT0FBTyxFQUFFO0lBQ3JCLElBQUlBLE9BQU8sS0FBSyxLQUFLLENBQUMsRUFBRUEsT0FBTyxHQUFHLENBQUMsQ0FBQztJQUNwQyxJQUFJMEQsS0FBcUMsRUFBRSxFQVkxQztJQUNEO0lBQ0EsQ0FBQztNQUFFbEMsR0FBRztNQUFFTztJQUFHLENBQUMsR0FBR0QsWUFBWSxDQUFDLElBQUksRUFBRU4sR0FBRyxFQUFFTyxFQUFFLENBQUM7SUFDMUMsT0FBTyxJQUFJLENBQUNpSixNQUFNLENBQUMsV0FBVyxFQUFFeEosR0FBRyxFQUFFTyxFQUFFLEVBQUUvQixPQUFPLENBQUM7RUFDckQ7RUFDQTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7RUFBTWlMLE9BQU9BLENBQUN6SixHQUFHLEVBQUVPLEVBQUUsRUFBRS9CLE9BQU8sRUFBRTtJQUN4QixJQUFJQSxPQUFPLEtBQUssS0FBSyxDQUFDLEVBQUVBLE9BQU8sR0FBRyxDQUFDLENBQUM7SUFDcEM7SUFDQSxDQUFDO01BQUV3QixHQUFHO01BQUVPO0lBQUcsQ0FBQyxHQUFHRCxZQUFZLENBQUMsSUFBSSxFQUFFTixHQUFHLEVBQUVPLEVBQUUsQ0FBQztJQUMxQyxPQUFPLElBQUksQ0FBQ2lKLE1BQU0sQ0FBQyxjQUFjLEVBQUV4SixHQUFHLEVBQUVPLEVBQUUsRUFBRS9CLE9BQU8sQ0FBQztFQUN4RDtFQUNBLE1BQU1rTCxJQUFJQSxDQUFDbkosRUFBRSxFQUFFRSxVQUFVLEVBQUVoQixNQUFNLEVBQUVrSyxZQUFZLEVBQUU7SUFDN0MsSUFBSXpILElBQStDLEVBQUU7TUFDakQsSUFBSSxDQUFDLElBQUksQ0FBQzJILE1BQU0sSUFBSSxDQUFDLElBQUksQ0FBQ0MsTUFBTSxFQUFFO1FBQzlCLE1BQU07VUFBRUM7UUFBWSxDQUFDLEdBQUcvTixtQkFBTyxDQUFDLHVHQUF3QixDQUFDO1FBQ3pELElBQUlnTyxnQkFBZ0I7UUFDcEIsSUFBSUMsaUJBQWlCO1FBQ3JCLElBQUk7VUFDQTtVQUNBLENBQUM7WUFBRUMsb0JBQW9CLEVBQUVGLGdCQUFnQjtZQUFFRyxxQkFBcUIsRUFBRUY7VUFBa0IsQ0FBQyxHQUFHLE1BQU0sQ0FBQyxDQUFDLEVBQUU5TixZQUFZLENBQUMrRyxzQkFBc0IsRUFBRSxDQUFDO1FBQzVJLENBQUMsQ0FBQyxPQUFPOEUsR0FBRyxFQUFFO1VBQ1Y7VUFDQTtVQUNBb0MsT0FBTyxDQUFDMUQsS0FBSyxDQUFDc0IsR0FBRyxDQUFDO1VBQ2xCLElBQUkyQixZQUFZLEVBQUU7WUFDZCxPQUFPLElBQUk7VUFDZjtVQUNBckIsb0JBQW9CLENBQUM7WUFDakJ0SSxHQUFHLEVBQUUsQ0FBQyxDQUFDLEVBQUUxQyxZQUFZLENBQUNpQyxXQUFXLEVBQUUsQ0FBQyxDQUFDLEVBQUVwQyxVQUFVLENBQUNxQyxTQUFTLEVBQUVlLEVBQUUsRUFBRWQsTUFBTSxJQUFJLElBQUksQ0FBQ0EsTUFBTSxFQUFFLElBQUksQ0FBQzZFLGFBQWEsQ0FBQyxDQUFDO1lBQzVHMUYsTUFBTSxFQUFFO1VBQ1osQ0FBQyxDQUFDO1VBQ0YsT0FBTyxJQUFJRixPQUFPLENBQUMsTUFBSSxDQUFDLENBQUMsQ0FBQztRQUM5QjtRQUNBLE1BQU0yTCxrQkFBa0IsR0FBR25JLGsySUFBeUM7UUFDcEUsSUFBSSxDQUFDOEgsZ0JBQWdCLElBQUlLLGtCQUFrQixFQUFFO1VBQ3pDTCxnQkFBZ0IsR0FBR0ssa0JBQWtCLEdBQUdBLGtCQUFrQixHQUFHM0csU0FBUztRQUMxRTtRQUNBLE1BQU02RyxrQkFBa0IsR0FBR3JJLDA0Q0FBeUM7UUFDcEUsSUFBSSxDQUFDK0gsaUJBQWlCLElBQUlNLGtCQUFrQixFQUFFO1VBQzFDTixpQkFBaUIsR0FBR00sa0JBQWtCLEdBQUdBLGtCQUFrQixHQUFHN0csU0FBUztRQUMzRTtRQUNBLElBQUlzRyxnQkFBZ0IsSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUdBLGdCQUFnQixDQUFDUyxTQUFTLEVBQUU7VUFDaEUsSUFBSSxDQUFDWixNQUFNLEdBQUcsSUFBSUUsV0FBVyxDQUFDQyxnQkFBZ0IsQ0FBQ1UsUUFBUSxFQUFFVixnQkFBZ0IsQ0FBQ1csU0FBUyxDQUFDO1VBQ3BGLElBQUksQ0FBQ2QsTUFBTSxDQUFDZSxNQUFNLENBQUNaLGdCQUFnQixDQUFDO1FBQ3hDO1FBQ0EsSUFBSUMsaUJBQWlCLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHQSxpQkFBaUIsQ0FBQ1EsU0FBUyxFQUFFO1VBQ2xFLElBQUksQ0FBQ1gsTUFBTSxHQUFHLElBQUlDLFdBQVcsQ0FBQ0UsaUJBQWlCLENBQUNTLFFBQVEsRUFBRVQsaUJBQWlCLENBQUNVLFNBQVMsQ0FBQztVQUN0RixJQUFJLENBQUNiLE1BQU0sQ0FBQ2MsTUFBTSxDQUFDWCxpQkFBaUIsQ0FBQztRQUN6QztNQUNKO01BQ0EsSUFBSVksZ0JBQWdCLEdBQUcsS0FBSztNQUM1QixJQUFJQyxpQkFBaUIsR0FBRyxLQUFLO01BQzdCLE1BQU1DLFlBQVksR0FBRyxDQUNqQjtRQUNJeEs7TUFDSixDQUFDLEVBQ0Q7UUFDSUEsRUFBRSxFQUFFRTtNQUNSLENBQUMsQ0FDSjtNQUNELEtBQUssTUFBTTtRQUFFRixFQUFFLEVBQUV5SyxLQUFLO1FBQUVDO01BQWtCLENBQUMsSUFBSUYsWUFBWSxFQUFDO1FBQ3hELElBQUlDLEtBQUssRUFBRTtVQUNQLE1BQU1FLFNBQVMsR0FBRyxDQUFDLENBQUMsRUFBRWhQLG9CQUFvQixDQUFDZ0YsbUJBQW1CLEVBQUUsSUFBSW1HLEdBQUcsQ0FBQzJELEtBQUssRUFBRSxVQUFVLENBQUMsQ0FBQ2pNLFFBQVEsQ0FBQztVQUNwRyxNQUFNb00sZUFBZSxHQUFHLENBQUMsQ0FBQyxFQUFFN04sWUFBWSxDQUFDaUMsV0FBVyxFQUFFLENBQUMsQ0FBQyxFQUFFcEMsVUFBVSxDQUFDcUMsU0FBUyxFQUFFMEwsU0FBUyxFQUFFekwsTUFBTSxJQUFJLElBQUksQ0FBQ0EsTUFBTSxDQUFDLENBQUM7VUFDbEgsSUFBSXdMLGlCQUFpQixJQUFJQyxTQUFTLEtBQUssQ0FBQyxDQUFDLEVBQUVoUCxvQkFBb0IsQ0FBQ2dGLG1CQUFtQixFQUFFLElBQUltRyxHQUFHLENBQUMsSUFBSSxDQUFDbkksTUFBTSxFQUFFLFVBQVUsQ0FBQyxDQUFDSCxRQUFRLENBQUMsRUFBRTtZQUM3SCxJQUFJcU0sWUFBWSxFQUFFQyxhQUFhO1lBQy9CUixnQkFBZ0IsR0FBR0EsZ0JBQWdCLElBQUksQ0FBQyxFQUFFLENBQUNPLFlBQVksR0FBRyxJQUFJLENBQUN2QixNQUFNLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHdUIsWUFBWSxDQUFDRSxRQUFRLENBQUNKLFNBQVMsQ0FBQyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUNHLGFBQWEsR0FBRyxJQUFJLENBQUN4QixNQUFNLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHd0IsYUFBYSxDQUFDQyxRQUFRLENBQUNILGVBQWUsQ0FBQyxDQUFDO1lBQzNOLEtBQUssTUFBTUksWUFBWSxJQUFJLENBQ3ZCTCxTQUFTLEVBQ1RDLGVBQWUsQ0FDbEIsRUFBQztjQUNFO2NBQ0E7Y0FDQSxNQUFNSyxVQUFVLEdBQUdELFlBQVksQ0FBQ0UsS0FBSyxDQUFDLEdBQUcsQ0FBQztjQUMxQyxLQUFJLElBQUlDLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQ1osaUJBQWlCLElBQUlZLENBQUMsR0FBR0YsVUFBVSxDQUFDbkwsTUFBTSxHQUFHLENBQUMsRUFBRXFMLENBQUMsRUFBRSxFQUFDO2dCQUNoRSxJQUFJQyxZQUFZO2dCQUNoQixNQUFNQyxXQUFXLEdBQUdKLFVBQVUsQ0FBQ25ELEtBQUssQ0FBQyxDQUFDLEVBQUVxRCxDQUFDLENBQUMsQ0FBQ0csSUFBSSxDQUFDLEdBQUcsQ0FBQztnQkFDcEQsSUFBSUQsV0FBVyxLQUFLLENBQUNELFlBQVksR0FBRyxJQUFJLENBQUM3QixNQUFNLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHNkIsWUFBWSxDQUFDTCxRQUFRLENBQUNNLFdBQVcsQ0FBQyxDQUFDLEVBQUU7a0JBQ3JHZCxpQkFBaUIsR0FBRyxJQUFJO2tCQUN4QjtnQkFDSjtjQUNKO1lBQ0o7WUFDQTtZQUNBO1lBQ0EsSUFBSUQsZ0JBQWdCLElBQUlDLGlCQUFpQixFQUFFO2NBQ3ZDLElBQUluQixZQUFZLEVBQUU7Z0JBQ2QsT0FBTyxJQUFJO2NBQ2Y7Y0FDQXJCLG9CQUFvQixDQUFDO2dCQUNqQnRJLEdBQUcsRUFBRSxDQUFDLENBQUMsRUFBRTFDLFlBQVksQ0FBQ2lDLFdBQVcsRUFBRSxDQUFDLENBQUMsRUFBRXBDLFVBQVUsQ0FBQ3FDLFNBQVMsRUFBRWUsRUFBRSxFQUFFZCxNQUFNLElBQUksSUFBSSxDQUFDQSxNQUFNLEVBQUUsSUFBSSxDQUFDNkUsYUFBYSxDQUFDLENBQUM7Z0JBQzVHMUYsTUFBTSxFQUFFO2NBQ1osQ0FBQyxDQUFDO2NBQ0YsT0FBTyxJQUFJRixPQUFPLENBQUMsTUFBSSxDQUFDLENBQUMsQ0FBQztZQUM5QjtVQUNKO1FBQ0o7TUFDSjtJQUNKO0lBQ0EsT0FBTyxLQUFLO0VBQ2hCO0VBQ0EsTUFBTThLLE1BQU1BLENBQUNwRCxNQUFNLEVBQUVwRyxHQUFHLEVBQUVPLEVBQUUsRUFBRS9CLE9BQU8sRUFBRXNOLFlBQVksRUFBRTtJQUNqRCxJQUFJQyx5QkFBeUI7SUFDN0IsSUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFFbE8sV0FBVyxDQUFDbU8sVUFBVSxFQUFFaE0sR0FBRyxDQUFDLEVBQUU7TUFDbkNzSSxvQkFBb0IsQ0FBQztRQUNqQnRJLEdBQUc7UUFDSHBCLE1BQU0sRUFBRTtNQUNaLENBQUMsQ0FBQztNQUNGLE9BQU8sS0FBSztJQUNoQjtJQUNBO0lBQ0E7SUFDQTtJQUNBLE1BQU1xTixlQUFlLEdBQUd6TixPQUFPLENBQUMwTixFQUFFLEtBQUssQ0FBQztJQUN4QyxJQUFJLENBQUNELGVBQWUsSUFBSSxDQUFDek4sT0FBTyxDQUFDMk4sT0FBTyxFQUFFO01BQ3RDLE1BQU0sSUFBSSxDQUFDekMsSUFBSSxDQUFDbkosRUFBRSxFQUFFbUQsU0FBUyxFQUFFbEYsT0FBTyxDQUFDaUIsTUFBTSxDQUFDO0lBQ2xEO0lBQ0EsSUFBSTJNLGlCQUFpQixHQUFHSCxlQUFlLElBQUl6TixPQUFPLENBQUM2TixrQkFBa0IsSUFBSSxDQUFDLENBQUMsRUFBRW5QLFVBQVUsQ0FBQytCLFNBQVMsRUFBRWUsR0FBRyxDQUFDLENBQUNqQixRQUFRLEtBQUssQ0FBQyxDQUFDLEVBQUU3QixVQUFVLENBQUMrQixTQUFTLEVBQUVzQixFQUFFLENBQUMsQ0FBQ3hCLFFBQVE7SUFDM0osTUFBTXVOLFNBQVMsR0FBRztNQUNkLEdBQUcsSUFBSSxDQUFDQztJQUNaLENBQUM7SUFDRDtJQUNBO0lBQ0E7SUFDQSxNQUFNQyxnQkFBZ0IsR0FBRyxJQUFJLENBQUNDLE9BQU8sS0FBSyxJQUFJO0lBQzlDLElBQUksQ0FBQ0EsT0FBTyxHQUFHLElBQUk7SUFDbkIsTUFBTUMsS0FBSyxHQUFHLElBQUksQ0FBQ0EsS0FBSztJQUN4QixJQUFJLENBQUNULGVBQWUsRUFBRTtNQUNsQixJQUFJLENBQUNTLEtBQUssR0FBRyxLQUFLO0lBQ3RCO0lBQ0E7SUFDQTtJQUNBLElBQUlULGVBQWUsSUFBSSxJQUFJLENBQUN2RCxHQUFHLEVBQUU7TUFDN0IsT0FBTyxLQUFLO0lBQ2hCO0lBQ0EsTUFBTWlFLFVBQVUsR0FBR0wsU0FBUyxDQUFDN00sTUFBTTtJQUNuQyxJQUFJeUMsS0FBK0IsRUFBRSxzQkFrRHBDO0lBQ0Q7SUFDQSxJQUFJeEYsTUFBTSxDQUFDZ1IsRUFBRSxFQUFFO01BQ1hDLFdBQVcsQ0FBQ0MsSUFBSSxDQUFDLGFBQWEsQ0FBQztJQUNuQztJQUNBLE1BQU07TUFBRXpCLE9BQU8sR0FBRyxLQUFLO01BQUUwQixNQUFNLEdBQUc7SUFBSyxDQUFDLEdBQUdyUCxPQUFPO0lBQ2xELE1BQU1zUCxVQUFVLEdBQUc7TUFDZjNCO0lBQ0osQ0FBQztJQUNELElBQUksSUFBSSxDQUFDNEIsY0FBYyxJQUFJLElBQUksQ0FBQ3JGLEdBQUcsRUFBRTtNQUNqQyxJQUFJLENBQUNnRSxLQUFLLEVBQUU7UUFDUjVRLE1BQU0sQ0FBQ2tTLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLGtCQUFrQixFQUFFOVAsc0JBQXNCLENBQUMsQ0FBQyxFQUFFLElBQUksQ0FBQzRQLGNBQWMsRUFBRUQsVUFBVSxDQUFDO01BQ3JHO01BQ0EsSUFBSSxDQUFDcEYsR0FBRyxDQUFDLENBQUM7TUFDVixJQUFJLENBQUNBLEdBQUcsR0FBRyxJQUFJO0lBQ25CO0lBQ0FuSSxFQUFFLEdBQUcsQ0FBQyxDQUFDLEVBQUVqRCxZQUFZLENBQUNpQyxXQUFXLEVBQUUsQ0FBQyxDQUFDLEVBQUVwQyxVQUFVLENBQUNxQyxTQUFTLEVBQUUsQ0FBQyxDQUFDLEVBQUVqQyxZQUFZLENBQUM2QixXQUFXLEVBQUVtQixFQUFFLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBRWxELGVBQWUsQ0FBQ2dDLGNBQWMsRUFBRWtCLEVBQUUsQ0FBQyxHQUFHQSxFQUFFLEVBQUUvQixPQUFPLENBQUNpQixNQUFNLEVBQUUsSUFBSSxDQUFDNkUsYUFBYSxDQUFDLENBQUM7SUFDbkwsTUFBTW5GLFNBQVMsR0FBRyxDQUFDLENBQUMsRUFBRS9CLGFBQWEsQ0FBQzhRLFlBQVksRUFBRSxDQUFDLENBQUMsRUFBRTNRLFlBQVksQ0FBQzZCLFdBQVcsRUFBRW1CLEVBQUUsQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFbEQsZUFBZSxDQUFDZ0MsY0FBYyxFQUFFa0IsRUFBRSxDQUFDLEdBQUdBLEVBQUUsRUFBRStMLFNBQVMsQ0FBQzdNLE1BQU0sQ0FBQztJQUNySixJQUFJLENBQUNzTyxjQUFjLEdBQUd4TixFQUFFO0lBQ3hCLE1BQU00TixZQUFZLEdBQUd4QixVQUFVLEtBQUtMLFNBQVMsQ0FBQzdNLE1BQU07SUFDcEQ7SUFDQTtJQUNBLElBQUksQ0FBQ3dNLGVBQWUsSUFBSSxJQUFJLENBQUNtQyxlQUFlLENBQUNqUCxTQUFTLENBQUMsSUFBSSxDQUFDZ1AsWUFBWSxFQUFFO01BQ3RFN0IsU0FBUyxDQUFDcE4sTUFBTSxHQUFHQyxTQUFTO01BQzVCckQsTUFBTSxDQUFDa1MsTUFBTSxDQUFDQyxJQUFJLENBQUMsaUJBQWlCLEVBQUUxTixFQUFFLEVBQUV1TixVQUFVLENBQUM7TUFDckQ7TUFDQSxJQUFJLENBQUNPLFdBQVcsQ0FBQ2pJLE1BQU0sRUFBRXBHLEdBQUcsRUFBRU8sRUFBRSxFQUFFO1FBQzlCLEdBQUcvQixPQUFPO1FBQ1ZxUCxNQUFNLEVBQUU7TUFDWixDQUFDLENBQUM7TUFDRixJQUFJQSxNQUFNLEVBQUU7UUFDUixJQUFJLENBQUNTLFlBQVksQ0FBQ25QLFNBQVMsQ0FBQztNQUNoQztNQUNBLElBQUk7UUFDQSxNQUFNLElBQUksQ0FBQ29QLEdBQUcsQ0FBQ2pDLFNBQVMsRUFBRSxJQUFJLENBQUNrQyxVQUFVLENBQUNsQyxTQUFTLENBQUM5RCxLQUFLLENBQUMsRUFBRSxJQUFJLENBQUM7TUFDckUsQ0FBQyxDQUFDLE9BQU9SLEdBQUcsRUFBRTtRQUNWLElBQUksQ0FBQyxDQUFDLEVBQUUzTCxRQUFRLENBQUNmLE9BQU8sRUFBRTBNLEdBQUcsQ0FBQyxJQUFJQSxHQUFHLENBQUN6SixTQUFTLEVBQUU7VUFDN0N6QyxNQUFNLENBQUNrUyxNQUFNLENBQUNDLElBQUksQ0FBQyxrQkFBa0IsRUFBRWpHLEdBQUcsRUFBRTdJLFNBQVMsRUFBRTJPLFVBQVUsQ0FBQztRQUN0RTtRQUNBLE1BQU05RixHQUFHO01BQ2I7TUFDQWxNLE1BQU0sQ0FBQ2tTLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLG9CQUFvQixFQUFFMU4sRUFBRSxFQUFFdU4sVUFBVSxDQUFDO01BQ3hELE9BQU8sSUFBSTtJQUNmO0lBQ0EsSUFBSVcsTUFBTSxHQUFHLENBQUMsQ0FBQyxFQUFFN1IsaUJBQWlCLENBQUNnRyxnQkFBZ0IsRUFBRTVDLEdBQUcsQ0FBQztJQUN6RCxJQUFJO01BQUVqQixRQUFRO01BQUU2RTtJQUFNLENBQUMsR0FBRzZLLE1BQU07SUFDaEM7SUFDQTtJQUNBO0lBQ0EsSUFBSXpOLEtBQUssRUFBRXNDLFFBQVE7SUFDbkIsSUFBSTtNQUNBO01BQ0EsQ0FBQ3RDLEtBQUssRUFBRTtRQUFFcUMsVUFBVSxFQUFFQztNQUFTLENBQUMsQ0FBQyxHQUFHLE1BQU01RSxPQUFPLENBQUNoRCxHQUFHLENBQUMsQ0FDbEQsSUFBSSxDQUFDbUQsVUFBVSxDQUFDb0UsV0FBVyxDQUFDLENBQUMsRUFDN0IsQ0FBQyxDQUFDLEVBQUU5RyxZQUFZLENBQUMrRyxzQkFBc0IsRUFBRSxDQUFDLEVBQzFDLElBQUksQ0FBQ3JFLFVBQVUsQ0FBQ0MsYUFBYSxDQUFDLENBQUMsQ0FDbEMsQ0FBQztJQUNOLENBQUMsQ0FBQyxPQUFPa0osR0FBRyxFQUFFO01BQ1Y7TUFDQTtNQUNBTSxvQkFBb0IsQ0FBQztRQUNqQnRJLEdBQUcsRUFBRU8sRUFBRTtRQUNQM0IsTUFBTSxFQUFFO01BQ1osQ0FBQyxDQUFDO01BQ0YsT0FBTyxLQUFLO0lBQ2hCO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBLElBQUksQ0FBQyxJQUFJLENBQUM4UCxRQUFRLENBQUN2UCxTQUFTLENBQUMsSUFBSSxDQUFDZ1AsWUFBWSxFQUFFO01BQzVDL0gsTUFBTSxHQUFHLGNBQWM7SUFDM0I7SUFDQTtJQUNBO0lBQ0EsSUFBSTNGLFVBQVUsR0FBR0YsRUFBRTtJQUNuQjtJQUNBO0lBQ0E7SUFDQXhCLFFBQVEsR0FBR0EsUUFBUSxHQUFHLENBQUMsQ0FBQyxFQUFFN0Msb0JBQW9CLENBQUNnRixtQkFBbUIsRUFBRSxDQUFDLENBQUMsRUFBRTdELGVBQWUsQ0FBQ2dDLGNBQWMsRUFBRU4sUUFBUSxDQUFDLENBQUMsR0FBR0EsUUFBUTtJQUM3SCxJQUFJeUosS0FBSyxHQUFHLENBQUMsQ0FBQyxFQUFFdE0sb0JBQW9CLENBQUNnRixtQkFBbUIsRUFBRW5DLFFBQVEsQ0FBQztJQUNuRSxNQUFNNFAsZ0JBQWdCLEdBQUdwTyxFQUFFLENBQUNKLFVBQVUsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUMsRUFBRXZELGlCQUFpQixDQUFDZ0csZ0JBQWdCLEVBQUVyQyxFQUFFLENBQUMsQ0FBQ3hCLFFBQVE7SUFDbkc7SUFDQTtJQUNBLElBQUksQ0FBQ2dOLHlCQUF5QixHQUFHLElBQUksQ0FBQ3lDLFVBQVUsQ0FBQ3pQLFFBQVEsQ0FBQyxLQUFLLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR2dOLHlCQUF5QixDQUFDNkMsV0FBVyxFQUFFO01BQ2xIdEcsb0JBQW9CLENBQUM7UUFDakJ0SSxHQUFHLEVBQUVPLEVBQUU7UUFDUDNCLE1BQU0sRUFBRTtNQUNaLENBQUMsQ0FBQztNQUNGLE9BQU8sSUFBSUYsT0FBTyxDQUFDLE1BQUksQ0FBQyxDQUFDLENBQUM7SUFDOUI7SUFDQSxNQUFNbVEsbUJBQW1CLEdBQUcsQ0FBQyxFQUFFRixnQkFBZ0IsSUFBSW5HLEtBQUssS0FBS21HLGdCQUFnQixLQUFLLENBQUMsQ0FBQyxDQUFDLEVBQUVoUyxVQUFVLENBQUMyRSxjQUFjLEVBQUVrSCxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFFMUwsYUFBYSxDQUFDb0gsZUFBZSxFQUFFLENBQUMsQ0FBQyxFQUFFbkgsV0FBVyxDQUFDd0UsYUFBYSxFQUFFaUgsS0FBSyxDQUFDLENBQUMsQ0FBQ21HLGdCQUFnQixDQUFDLENBQUMsQ0FBQztJQUMxTjtJQUNBO0lBQ0EsTUFBTUcsaUJBQWlCLEdBQUcsQ0FBQ3RRLE9BQU8sQ0FBQzJOLE9BQU8sS0FBSSxNQUFNNVEsaUJBQWlCLENBQUM7TUFDbEUyRCxNQUFNLEVBQUVxQixFQUFFO01BQ1ZkLE1BQU0sRUFBRTZNLFNBQVMsQ0FBQzdNLE1BQU07TUFDeEJiLE1BQU0sRUFBRTtJQUNaLENBQUMsQ0FBQztJQUNGLElBQUlxTixlQUFlLElBQUk2QyxpQkFBaUIsRUFBRTtNQUN0QzFDLGlCQUFpQixHQUFHLEtBQUs7SUFDN0I7SUFDQSxJQUFJQSxpQkFBaUIsSUFBSXJOLFFBQVEsS0FBSyxTQUFTLEVBQUU7TUFDN0M7TUFDQVAsT0FBTyxDQUFDNk4sa0JBQWtCLEdBQUcsSUFBSTtNQUNqQyxJQUFJbkssS0FBcUQsRUFBRSxFQXFCMUQsTUFBTTtRQUNIdU0sTUFBTSxDQUFDMVAsUUFBUSxHQUFHZ0MsbUJBQW1CLENBQUNoQyxRQUFRLEVBQUVpQyxLQUFLLENBQUM7UUFDdEQsSUFBSXlOLE1BQU0sQ0FBQzFQLFFBQVEsS0FBS0EsUUFBUSxFQUFFO1VBQzlCQSxRQUFRLEdBQUcwUCxNQUFNLENBQUMxUCxRQUFRO1VBQzFCMFAsTUFBTSxDQUFDMVAsUUFBUSxHQUFHLENBQUMsQ0FBQyxFQUFFekIsWUFBWSxDQUFDaUMsV0FBVyxFQUFFUixRQUFRLENBQUM7VUFDekQsSUFBSSxDQUFDK1AsaUJBQWlCLEVBQUU7WUFDcEI5TyxHQUFHLEdBQUcsQ0FBQyxDQUFDLEVBQUVoRCxVQUFVLENBQUMrUCxvQkFBb0IsRUFBRTBCLE1BQU0sQ0FBQztVQUN0RDtRQUNKO01BQ0o7SUFDSjtJQUNBLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRTVRLFdBQVcsQ0FBQ21PLFVBQVUsRUFBRXpMLEVBQUUsQ0FBQyxFQUFFO01BQ2xDLElBQUksTUFBdUM7UUFDdkMsTUFBTXZGLE1BQU0sQ0FBQ0MsY0FBYyxDQUFDLElBQUlvRCxLQUFLLENBQUMsaUJBQWlCLEdBQUcyQixHQUFHLEdBQUcsYUFBYSxHQUFHTyxFQUFFLEdBQUcsMkNBQTJDLEdBQUcsb0ZBQW9GLENBQUMsRUFBRSxtQkFBbUIsRUFBRTtVQUMzT3BGLEtBQUssRUFBRSxNQUFNO1VBQ2JTLFVBQVUsRUFBRSxLQUFLO1VBQ2pCMEMsWUFBWSxFQUFFO1FBQ2xCLENBQUMsQ0FBQztNQUNOO01BQ0FnSyxvQkFBb0IsQ0FBQztRQUNqQnRJLEdBQUcsRUFBRU8sRUFBRTtRQUNQM0IsTUFBTSxFQUFFO01BQ1osQ0FBQyxDQUFDO01BQ0YsT0FBTyxLQUFLO0lBQ2hCO0lBQ0E2QixVQUFVLEdBQUcsQ0FBQyxDQUFDLEVBQUVyRCxhQUFhLENBQUM4USxZQUFZLEVBQUUsQ0FBQyxDQUFDLEVBQUU3USxlQUFlLENBQUNnQyxjQUFjLEVBQUVvQixVQUFVLENBQUMsRUFBRTZMLFNBQVMsQ0FBQzdNLE1BQU0sQ0FBQztJQUMvRytJLEtBQUssR0FBRyxDQUFDLENBQUMsRUFBRXRNLG9CQUFvQixDQUFDZ0YsbUJBQW1CLEVBQUVuQyxRQUFRLENBQUM7SUFDL0QsSUFBSW1RLFVBQVUsR0FBRyxLQUFLO0lBQ3RCLElBQUksQ0FBQyxDQUFDLEVBQUV2UyxVQUFVLENBQUMyRSxjQUFjLEVBQUVrSCxLQUFLLENBQUMsRUFBRTtNQUN2QyxNQUFNekUsUUFBUSxHQUFHLENBQUMsQ0FBQyxFQUFFbkgsaUJBQWlCLENBQUNnRyxnQkFBZ0IsRUFBRW5DLFVBQVUsQ0FBQztNQUNwRSxNQUFNekIsVUFBVSxHQUFHK0UsUUFBUSxDQUFDaEYsUUFBUTtNQUNwQyxNQUFNb1EsVUFBVSxHQUFHLENBQUMsQ0FBQyxFQUFFcFMsV0FBVyxDQUFDd0UsYUFBYSxFQUFFaUgsS0FBSyxDQUFDO01BQ3hEMEcsVUFBVSxHQUFHLENBQUMsQ0FBQyxFQUFFcFMsYUFBYSxDQUFDb0gsZUFBZSxFQUFFaUwsVUFBVSxDQUFDLENBQUNuUSxVQUFVLENBQUM7TUFDdkUsTUFBTW9RLGlCQUFpQixHQUFHNUcsS0FBSyxLQUFLeEosVUFBVTtNQUM5QyxNQUFNcVEsY0FBYyxHQUFHRCxpQkFBaUIsR0FBRyxDQUFDLENBQUMsRUFBRXBSLGNBQWMsQ0FBQ3NSLGFBQWEsRUFBRTlHLEtBQUssRUFBRXhKLFVBQVUsRUFBRTRFLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQztNQUMzRyxJQUFJLENBQUNzTCxVQUFVLElBQUlFLGlCQUFpQixJQUFJLENBQUNDLGNBQWMsQ0FBQzFMLE1BQU0sRUFBRTtRQUM1RCxNQUFNNEwsYUFBYSxHQUFHdlUsTUFBTSxDQUFDd1UsSUFBSSxDQUFDTCxVQUFVLENBQUNNLE1BQU0sQ0FBQyxDQUFDQyxNQUFNLENBQUV0TSxLQUFLLElBQUcsQ0FBQ1EsS0FBSyxDQUFDUixLQUFLLENBQUMsSUFBSSxDQUFDK0wsVUFBVSxDQUFDTSxNQUFNLENBQUNyTSxLQUFLLENBQUMsQ0FBQ3VNLFFBQVEsQ0FBQztRQUN6SCxJQUFJSixhQUFhLENBQUNsUCxNQUFNLEdBQUcsQ0FBQyxJQUFJLENBQUN5TyxpQkFBaUIsRUFBRTtVQUNoRCxJQUFJLE1BQXVDO1lBQ3ZDMUUsT0FBTyxDQUFDd0YsSUFBSSxDQUFDLEVBQUUsSUFBSVIsaUJBQWlCLEdBQUcsb0JBQW9CLEdBQUcsNkJBQTZCLENBQUMsR0FBRyw4QkFBOEIsSUFBSSxjQUFjLEdBQUdHLGFBQWEsQ0FBQzFELElBQUksQ0FBQyxJQUFJLENBQUMsR0FBRywwQkFBMEIsQ0FBQyxDQUFDO1VBQzdNO1VBQ0EsTUFBTTdRLE1BQU0sQ0FBQ0MsY0FBYyxDQUFDLElBQUlvRCxLQUFLLENBQUMsQ0FBQytRLGlCQUFpQixHQUFHLHVCQUF1QixHQUFHcFAsR0FBRyxHQUFHLG1DQUFtQyxHQUFHdVAsYUFBYSxDQUFDMUQsSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLGlDQUFpQyxHQUFHLDJCQUEyQixHQUFHN00sVUFBVSxHQUFHLDJDQUEyQyxHQUFHd0osS0FBSyxHQUFHLEtBQUssS0FBSyw4Q0FBOEMsSUFBSTRHLGlCQUFpQixHQUFHLDJCQUEyQixHQUFHLHNCQUFzQixDQUFDLENBQUMsQ0FBQyxFQUFFLG1CQUFtQixFQUFFO1lBQzNialUsS0FBSyxFQUFFLE1BQU07WUFDYlMsVUFBVSxFQUFFLEtBQUs7WUFDakIwQyxZQUFZLEVBQUU7VUFDbEIsQ0FBQyxDQUFDO1FBQ047TUFDSixDQUFDLE1BQU0sSUFBSThRLGlCQUFpQixFQUFFO1FBQzFCN08sRUFBRSxHQUFHLENBQUMsQ0FBQyxFQUFFdkQsVUFBVSxDQUFDK1Asb0JBQW9CLEVBQUUvUixNQUFNLENBQUNvRCxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUUyRixRQUFRLEVBQUU7VUFDbEVoRixRQUFRLEVBQUVzUSxjQUFjLENBQUMxTCxNQUFNO1VBQy9CQyxLQUFLLEVBQUUsQ0FBQyxDQUFDLEVBQUU3RixLQUFLLENBQUM4UixJQUFJLEVBQUVqTSxLQUFLLEVBQUV5TCxjQUFjLENBQUM3SCxNQUFNO1FBQ3ZELENBQUMsQ0FBQyxDQUFDO01BQ1AsQ0FBQyxNQUFNO1FBQ0g7UUFDQXhNLE1BQU0sQ0FBQ29ELE1BQU0sQ0FBQ3dGLEtBQUssRUFBRXNMLFVBQVUsQ0FBQztNQUNwQztJQUNKO0lBQ0EsSUFBSSxDQUFDakQsZUFBZSxFQUFFO01BQ2xCblEsTUFBTSxDQUFDa1MsTUFBTSxDQUFDQyxJQUFJLENBQUMsa0JBQWtCLEVBQUUxTixFQUFFLEVBQUV1TixVQUFVLENBQUM7SUFDMUQ7SUFDQSxNQUFNZ0MsWUFBWSxHQUFHLElBQUksQ0FBQy9RLFFBQVEsS0FBSyxNQUFNLElBQUksSUFBSSxDQUFDQSxRQUFRLEtBQUssU0FBUztJQUM1RSxJQUFJO01BQ0EsSUFBSWdSLG1DQUFtQyxFQUFFQyx5QkFBeUIsRUFBRUMsZ0JBQWdCO01BQ3BGLElBQUlDLFNBQVMsR0FBRyxNQUFNLElBQUksQ0FBQ0MsWUFBWSxDQUFDO1FBQ3BDM0gsS0FBSztRQUNMekosUUFBUTtRQUNSNkUsS0FBSztRQUNMckQsRUFBRTtRQUNGRSxVQUFVO1FBQ1ZxTixVQUFVO1FBQ1ZyTyxNQUFNLEVBQUU2TSxTQUFTLENBQUM3TSxNQUFNO1FBQ3hCMlEsU0FBUyxFQUFFOUQsU0FBUyxDQUFDOEQsU0FBUztRQUM5QnRKLGFBQWEsRUFBRWdJLGlCQUFpQjtRQUNoQzNILHdCQUF3QixFQUFFM0ksT0FBTyxDQUFDMkksd0JBQXdCO1FBQzFEOEUsZUFBZSxFQUFFQSxlQUFlLElBQUksQ0FBQyxJQUFJLENBQUNvRSxVQUFVO1FBQ3BEeEI7TUFDSixDQUFDLENBQUM7TUFDRixJQUFJLENBQUM1QyxlQUFlLElBQUksQ0FBQ3pOLE9BQU8sQ0FBQzJOLE9BQU8sRUFBRTtRQUN0QyxNQUFNLElBQUksQ0FBQ3pDLElBQUksQ0FBQ25KLEVBQUUsRUFBRSxZQUFZLElBQUkyUCxTQUFTLEdBQUdBLFNBQVMsQ0FBQ3pQLFVBQVUsR0FBR2lELFNBQVMsRUFBRTRJLFNBQVMsQ0FBQzdNLE1BQU0sQ0FBQztNQUN2RztNQUNBLElBQUksT0FBTyxJQUFJeVEsU0FBUyxJQUFJcEIsaUJBQWlCLEVBQUU7UUFDM0MvUCxRQUFRLEdBQUdtUixTQUFTLENBQUMxSCxLQUFLLElBQUlBLEtBQUs7UUFDbkNBLEtBQUssR0FBR3pKLFFBQVE7UUFDaEIsSUFBSSxDQUFDK08sVUFBVSxDQUFDM0IsT0FBTyxFQUFFO1VBQ3JCdkksS0FBSyxHQUFHNUksTUFBTSxDQUFDb0QsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFOFIsU0FBUyxDQUFDdE0sS0FBSyxJQUFJLENBQUMsQ0FBQyxFQUFFQSxLQUFLLENBQUM7UUFDM0Q7UUFDQSxNQUFNME0scUJBQXFCLEdBQUcsQ0FBQyxDQUFDLEVBQUUvUyxZQUFZLENBQUM2QixXQUFXLEVBQUVxUCxNQUFNLENBQUMxUCxRQUFRLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBRTFCLGVBQWUsQ0FBQ2dDLGNBQWMsRUFBRW9QLE1BQU0sQ0FBQzFQLFFBQVEsQ0FBQyxHQUFHMFAsTUFBTSxDQUFDMVAsUUFBUTtRQUNySixJQUFJbVEsVUFBVSxJQUFJblEsUUFBUSxLQUFLdVIscUJBQXFCLEVBQUU7VUFDbER0VixNQUFNLENBQUN3VSxJQUFJLENBQUNOLFVBQVUsQ0FBQyxDQUFDcUIsT0FBTyxDQUFFQyxHQUFHLElBQUc7WUFDbkMsSUFBSXRCLFVBQVUsSUFBSXRMLEtBQUssQ0FBQzRNLEdBQUcsQ0FBQyxLQUFLdEIsVUFBVSxDQUFDc0IsR0FBRyxDQUFDLEVBQUU7Y0FDOUMsT0FBTzVNLEtBQUssQ0FBQzRNLEdBQUcsQ0FBQztZQUNyQjtVQUNKLENBQUMsQ0FBQztRQUNOO1FBQ0EsSUFBSSxDQUFDLENBQUMsRUFBRTdULFVBQVUsQ0FBQzJFLGNBQWMsRUFBRXZDLFFBQVEsQ0FBQyxFQUFFO1VBQzFDLE1BQU0wUixVQUFVLEdBQUcsQ0FBQzNDLFVBQVUsQ0FBQzNCLE9BQU8sSUFBSStELFNBQVMsQ0FBQ3pQLFVBQVUsR0FBR3lQLFNBQVMsQ0FBQ3pQLFVBQVUsR0FBRyxDQUFDLENBQUMsRUFBRW5ELFlBQVksQ0FBQ2lDLFdBQVcsRUFBRSxDQUFDLENBQUMsRUFBRXBDLFVBQVUsQ0FBQ3FDLFNBQVMsRUFBRSxJQUFJNkgsR0FBRyxDQUFDOUcsRUFBRSxFQUFFK0csUUFBUSxDQUFDRixJQUFJLENBQUMsQ0FBQ3JJLFFBQVEsRUFBRXVOLFNBQVMsQ0FBQzdNLE1BQU0sQ0FBQyxFQUFFLElBQUksQ0FBQztVQUM3TSxJQUFJaVIsU0FBUyxHQUFHRCxVQUFVO1VBQzFCLElBQUksQ0FBQyxDQUFDLEVBQUVsVCxZQUFZLENBQUM2QixXQUFXLEVBQUVzUixTQUFTLENBQUMsRUFBRTtZQUMxQ0EsU0FBUyxHQUFHLENBQUMsQ0FBQyxFQUFFclQsZUFBZSxDQUFDZ0MsY0FBYyxFQUFFcVIsU0FBUyxDQUFDO1VBQzlEO1VBQ0EsSUFBSXhPLEtBQStCLEVBQUUsRUFJcEM7VUFDRCxNQUFNaU4sVUFBVSxHQUFHLENBQUMsQ0FBQyxFQUFFcFMsV0FBVyxDQUFDd0UsYUFBYSxFQUFFeEMsUUFBUSxDQUFDO1VBQzNELE1BQU02UixhQUFhLEdBQUcsQ0FBQyxDQUFDLEVBQUU5VCxhQUFhLENBQUNvSCxlQUFlLEVBQUVpTCxVQUFVLENBQUMsQ0FBQyxJQUFJOUgsR0FBRyxDQUFDcUosU0FBUyxFQUFFcEosUUFBUSxDQUFDRixJQUFJLENBQUMsQ0FBQ3JJLFFBQVEsQ0FBQztVQUNoSCxJQUFJNlIsYUFBYSxFQUFFO1lBQ2Y1VixNQUFNLENBQUNvRCxNQUFNLENBQUN3RixLQUFLLEVBQUVnTixhQUFhLENBQUM7VUFDdkM7UUFDSjtNQUNKO01BQ0E7TUFDQSxJQUFJLE1BQU0sSUFBSVYsU0FBUyxFQUFFO1FBQ3JCLElBQUlBLFNBQVMsQ0FBQy9MLElBQUksS0FBSyxtQkFBbUIsRUFBRTtVQUN4QyxPQUFPLElBQUksQ0FBQ3FGLE1BQU0sQ0FBQ3BELE1BQU0sRUFBRThKLFNBQVMsQ0FBQ3RMLE1BQU0sRUFBRXNMLFNBQVMsQ0FBQ3ZMLEtBQUssRUFBRW5HLE9BQU8sQ0FBQztRQUMxRSxDQUFDLE1BQU07VUFDSDhKLG9CQUFvQixDQUFDO1lBQ2pCdEksR0FBRyxFQUFFa1EsU0FBUyxDQUFDMUwsV0FBVztZQUMxQjVGLE1BQU0sRUFBRTtVQUNaLENBQUMsQ0FBQztVQUNGLE9BQU8sSUFBSUYsT0FBTyxDQUFDLE1BQUksQ0FBQyxDQUFDLENBQUM7UUFDOUI7TUFDSjtNQUNBLE1BQU1tUyxTQUFTLEdBQUdYLFNBQVMsQ0FBQ1ksU0FBUztNQUNyQyxJQUFJRCxTQUFTLElBQUlBLFNBQVMsQ0FBQ0UscUJBQXFCLEVBQUU7UUFDOUMsTUFBTUMsT0FBTyxHQUFHLEVBQUUsQ0FBQ0MsTUFBTSxDQUFDSixTQUFTLENBQUNFLHFCQUFxQixDQUFDLENBQUMsQ0FBQztRQUM1REMsT0FBTyxDQUFDVCxPQUFPLENBQUVXLE1BQU0sSUFBRztVQUN0QixDQUFDLENBQUMsRUFBRTlVLE9BQU8sQ0FBQytVLHNCQUFzQixFQUFFRCxNQUFNLENBQUNFLEtBQUssQ0FBQztRQUNyRCxDQUFDLENBQUM7TUFDTjtNQUNBO01BQ0EsSUFBSSxDQUFDbEIsU0FBUyxDQUFDbUIsT0FBTyxJQUFJbkIsU0FBUyxDQUFDb0IsT0FBTyxLQUFLcEIsU0FBUyxDQUFDa0IsS0FBSyxFQUFFO1FBQzdELElBQUlsQixTQUFTLENBQUNrQixLQUFLLENBQUNHLFNBQVMsSUFBSXJCLFNBQVMsQ0FBQ2tCLEtBQUssQ0FBQ0csU0FBUyxDQUFDQyxZQUFZLEVBQUU7VUFDckU7VUFDQWhULE9BQU8sQ0FBQ2lCLE1BQU0sR0FBRyxLQUFLO1VBQ3RCLE1BQU0rRSxXQUFXLEdBQUcwTCxTQUFTLENBQUNrQixLQUFLLENBQUNHLFNBQVMsQ0FBQ0MsWUFBWTtVQUMxRDtVQUNBO1VBQ0E7VUFDQSxJQUFJaE4sV0FBVyxDQUFDckUsVUFBVSxDQUFDLEdBQUcsQ0FBQyxJQUFJK1AsU0FBUyxDQUFDa0IsS0FBSyxDQUFDRyxTQUFTLENBQUNFLHNCQUFzQixLQUFLLEtBQUssRUFBRTtZQUMzRixNQUFNQyxVQUFVLEdBQUcsQ0FBQyxDQUFDLEVBQUU5VSxpQkFBaUIsQ0FBQ2dHLGdCQUFnQixFQUFFNEIsV0FBVyxDQUFDO1lBQ3ZFa04sVUFBVSxDQUFDM1MsUUFBUSxHQUFHZ0MsbUJBQW1CLENBQUMyUSxVQUFVLENBQUMzUyxRQUFRLEVBQUVpQyxLQUFLLENBQUM7WUFDckUsTUFBTTtjQUFFaEIsR0FBRyxFQUFFNEUsTUFBTTtjQUFFckUsRUFBRSxFQUFFb0U7WUFBTSxDQUFDLEdBQUdyRSxZQUFZLENBQUMsSUFBSSxFQUFFa0UsV0FBVyxFQUFFQSxXQUFXLENBQUM7WUFDL0UsT0FBTyxJQUFJLENBQUNnRixNQUFNLENBQUNwRCxNQUFNLEVBQUV4QixNQUFNLEVBQUVELEtBQUssRUFBRW5HLE9BQU8sQ0FBQztVQUN0RDtVQUNBOEosb0JBQW9CLENBQUM7WUFDakJ0SSxHQUFHLEVBQUV3RSxXQUFXO1lBQ2hCNUYsTUFBTSxFQUFFO1VBQ1osQ0FBQyxDQUFDO1VBQ0YsT0FBTyxJQUFJRixPQUFPLENBQUMsTUFBSSxDQUFDLENBQUMsQ0FBQztRQUM5QjtRQUNBNE4sU0FBUyxDQUFDOEQsU0FBUyxHQUFHLENBQUMsQ0FBQ0YsU0FBUyxDQUFDa0IsS0FBSyxDQUFDTyxXQUFXO1FBQ25EO1FBQ0EsSUFBSXpCLFNBQVMsQ0FBQ2tCLEtBQUssQ0FBQ3ZKLFFBQVEsS0FBSy9CLGtCQUFrQixFQUFFO1VBQ2pELElBQUk4TCxhQUFhO1VBQ2pCLElBQUk7WUFDQSxNQUFNLElBQUksQ0FBQ0MsY0FBYyxDQUFDLE1BQU0sQ0FBQztZQUNqQ0QsYUFBYSxHQUFHLE1BQU07VUFDMUIsQ0FBQyxDQUFDLE9BQU90VixDQUFDLEVBQUU7WUFDUnNWLGFBQWEsR0FBRyxTQUFTO1VBQzdCO1VBQ0ExQixTQUFTLEdBQUcsTUFBTSxJQUFJLENBQUNDLFlBQVksQ0FBQztZQUNoQzNILEtBQUssRUFBRW9KLGFBQWE7WUFDcEI3UyxRQUFRLEVBQUU2UyxhQUFhO1lBQ3ZCaE8sS0FBSztZQUNMckQsRUFBRTtZQUNGRSxVQUFVO1lBQ1ZxTixVQUFVLEVBQUU7Y0FDUjNCLE9BQU8sRUFBRTtZQUNiLENBQUM7WUFDRDFNLE1BQU0sRUFBRTZNLFNBQVMsQ0FBQzdNLE1BQU07WUFDeEIyUSxTQUFTLEVBQUU5RCxTQUFTLENBQUM4RCxTQUFTO1lBQzlCMEIsVUFBVSxFQUFFO1VBQ2hCLENBQUMsQ0FBQztVQUNGLElBQUksTUFBTSxJQUFJNUIsU0FBUyxFQUFFO1lBQ3JCLE1BQU1sVixNQUFNLENBQUNDLGNBQWMsQ0FBQyxJQUFJb0QsS0FBSyxDQUFDLHNDQUFzQyxDQUFDLEVBQUUsbUJBQW1CLEVBQUU7Y0FDaEdsRCxLQUFLLEVBQUUsTUFBTTtjQUNiUyxVQUFVLEVBQUUsS0FBSztjQUNqQjBDLFlBQVksRUFBRTtZQUNsQixDQUFDLENBQUM7VUFDTjtRQUNKO01BQ0o7TUFDQSxJQUFJMk4sZUFBZSxJQUFJLElBQUksQ0FBQ2xOLFFBQVEsS0FBSyxTQUFTLElBQUksQ0FBQyxDQUFDaVIseUJBQXlCLEdBQUc3RyxJQUFJLENBQUM0SSxhQUFhLENBQUNYLEtBQUssS0FBSyxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUcsQ0FBQ3JCLG1DQUFtQyxHQUFHQyx5QkFBeUIsQ0FBQ3VCLFNBQVMsS0FBSyxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUd4QixtQ0FBbUMsQ0FBQ2lDLFVBQVUsTUFBTSxHQUFHLEtBQUssQ0FBQy9CLGdCQUFnQixHQUFHQyxTQUFTLENBQUNrQixLQUFLLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHbkIsZ0JBQWdCLENBQUNzQixTQUFTLENBQUMsRUFBRTtRQUM3VztRQUNBO1FBQ0FyQixTQUFTLENBQUNrQixLQUFLLENBQUNHLFNBQVMsQ0FBQ1MsVUFBVSxHQUFHLEdBQUc7TUFDOUM7TUFDQSxJQUFJQyxnQkFBZ0I7TUFDcEI7TUFDQSxNQUFNQyxtQkFBbUIsR0FBRzFULE9BQU8sQ0FBQzJOLE9BQU8sSUFBSUcsU0FBUyxDQUFDOUQsS0FBSyxNQUFNLENBQUN5SixnQkFBZ0IsR0FBRy9CLFNBQVMsQ0FBQzFILEtBQUssS0FBSyxJQUFJLEdBQUd5SixnQkFBZ0IsR0FBR3pKLEtBQUssQ0FBQztNQUM1SSxJQUFJMkosZUFBZTtNQUNuQixNQUFNQyxZQUFZLEdBQUcsQ0FBQ0QsZUFBZSxHQUFHM1QsT0FBTyxDQUFDcVAsTUFBTSxLQUFLLElBQUksR0FBR3NFLGVBQWUsR0FBRyxDQUFDbEcsZUFBZSxJQUFJLENBQUNpRyxtQkFBbUI7TUFDNUgsTUFBTUcsV0FBVyxHQUFHRCxZQUFZLEdBQUc7UUFDL0JsSixDQUFDLEVBQUUsQ0FBQztRQUNKRyxDQUFDLEVBQUU7TUFDUCxDQUFDLEdBQUcsSUFBSTtNQUNSLE1BQU1pSixtQkFBbUIsR0FBR3hHLFlBQVksSUFBSSxJQUFJLEdBQUdBLFlBQVksR0FBR3VHLFdBQVc7TUFDN0U7TUFDQSxNQUFNRSxtQkFBbUIsR0FBRztRQUN4QixHQUFHakcsU0FBUztRQUNaOUQsS0FBSztRQUNMekosUUFBUTtRQUNSNkUsS0FBSztRQUNMMUUsTUFBTSxFQUFFQyxTQUFTO1FBQ2pCa1IsVUFBVSxFQUFFO01BQ2hCLENBQUM7TUFDRDtNQUNBO01BQ0E7TUFDQTtNQUNBO01BQ0EsSUFBSXBFLGVBQWUsSUFBSTZELFlBQVksRUFBRTtRQUNqQyxJQUFJMEMsb0NBQW9DLEVBQUVDLDBCQUEwQixFQUFFQyxpQkFBaUI7UUFDdkZ4QyxTQUFTLEdBQUcsTUFBTSxJQUFJLENBQUNDLFlBQVksQ0FBQztVQUNoQzNILEtBQUssRUFBRSxJQUFJLENBQUN6SixRQUFRO1VBQ3BCQSxRQUFRLEVBQUUsSUFBSSxDQUFDQSxRQUFRO1VBQ3ZCNkUsS0FBSztVQUNMckQsRUFBRTtVQUNGRSxVQUFVO1VBQ1ZxTixVQUFVLEVBQUU7WUFDUjNCLE9BQU8sRUFBRTtVQUNiLENBQUM7VUFDRDFNLE1BQU0sRUFBRTZNLFNBQVMsQ0FBQzdNLE1BQU07VUFDeEIyUSxTQUFTLEVBQUU5RCxTQUFTLENBQUM4RCxTQUFTO1VBQzlCbkUsZUFBZSxFQUFFQSxlQUFlLElBQUksQ0FBQyxJQUFJLENBQUNvRTtRQUM5QyxDQUFDLENBQUM7UUFDRixJQUFJLE1BQU0sSUFBSUgsU0FBUyxFQUFFO1VBQ3JCLE1BQU1sVixNQUFNLENBQUNDLGNBQWMsQ0FBQyxJQUFJb0QsS0FBSyxDQUFDLGtDQUFrQyxHQUFHLElBQUksQ0FBQ1UsUUFBUSxDQUFDLEVBQUUsbUJBQW1CLEVBQUU7WUFDNUc1RCxLQUFLLEVBQUUsTUFBTTtZQUNiUyxVQUFVLEVBQUUsS0FBSztZQUNqQjBDLFlBQVksRUFBRTtVQUNsQixDQUFDLENBQUM7UUFDTjtRQUNBLElBQUksSUFBSSxDQUFDUyxRQUFRLEtBQUssU0FBUyxJQUFJLENBQUMsQ0FBQzBULDBCQUEwQixHQUFHdEosSUFBSSxDQUFDNEksYUFBYSxDQUFDWCxLQUFLLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHLENBQUNvQixvQ0FBb0MsR0FBR0MsMEJBQTBCLENBQUNsQixTQUFTLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHaUIsb0NBQW9DLENBQUNSLFVBQVUsTUFBTSxHQUFHLEtBQUssQ0FBQ1UsaUJBQWlCLEdBQUd4QyxTQUFTLENBQUNrQixLQUFLLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHc0IsaUJBQWlCLENBQUNuQixTQUFTLENBQUMsRUFBRTtVQUNoVztVQUNBO1VBQ0FyQixTQUFTLENBQUNrQixLQUFLLENBQUNHLFNBQVMsQ0FBQ1MsVUFBVSxHQUFHLEdBQUc7UUFDOUM7UUFDQSxJQUFJO1VBQ0EsTUFBTSxJQUFJLENBQUN6RCxHQUFHLENBQUNnRSxtQkFBbUIsRUFBRXJDLFNBQVMsRUFBRW9DLG1CQUFtQixDQUFDO1FBQ3ZFLENBQUMsQ0FBQyxPQUFPdEssR0FBRyxFQUFFO1VBQ1YsSUFBSSxDQUFDLENBQUMsRUFBRTNMLFFBQVEsQ0FBQ2YsT0FBTyxFQUFFME0sR0FBRyxDQUFDLElBQUlBLEdBQUcsQ0FBQ3pKLFNBQVMsRUFBRTtZQUM3Q3pDLE1BQU0sQ0FBQ2tTLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLGtCQUFrQixFQUFFakcsR0FBRyxFQUFFN0ksU0FBUyxFQUFFMk8sVUFBVSxDQUFDO1VBQ3RFO1VBQ0EsTUFBTTlGLEdBQUc7UUFDYjtRQUNBLE9BQU8sSUFBSTtNQUNmO01BQ0FsTSxNQUFNLENBQUNrUyxNQUFNLENBQUNDLElBQUksQ0FBQyxxQkFBcUIsRUFBRTFOLEVBQUUsRUFBRXVOLFVBQVUsQ0FBQztNQUN6RCxJQUFJLENBQUNPLFdBQVcsQ0FBQ2pJLE1BQU0sRUFBRXBHLEdBQUcsRUFBRU8sRUFBRSxFQUFFL0IsT0FBTyxDQUFDO01BQzFDO01BQ0E7TUFDQTtNQUNBLE1BQU1tVSxlQUFlLEdBQUcxRyxlQUFlLElBQUksQ0FBQ3FHLG1CQUFtQixJQUFJLENBQUM5RixnQkFBZ0IsSUFBSSxDQUFDMkIsWUFBWSxJQUFJLENBQUMsQ0FBQyxFQUFFdlEsY0FBYyxDQUFDZ1YsbUJBQW1CLEVBQUVMLG1CQUFtQixFQUFFLElBQUksQ0FBQ2hHLEtBQUssQ0FBQztNQUNqTCxJQUFJLENBQUNvRyxlQUFlLEVBQUU7UUFDbEIsSUFBSTtVQUNBLE1BQU0sSUFBSSxDQUFDcEUsR0FBRyxDQUFDZ0UsbUJBQW1CLEVBQUVyQyxTQUFTLEVBQUVvQyxtQkFBbUIsQ0FBQztRQUN2RSxDQUFDLENBQUMsT0FBTy9JLENBQUMsRUFBRTtVQUNSLElBQUlBLENBQUMsQ0FBQ2hMLFNBQVMsRUFBRTJSLFNBQVMsQ0FBQ3hKLEtBQUssR0FBR3dKLFNBQVMsQ0FBQ3hKLEtBQUssSUFBSTZDLENBQUMsQ0FBQyxLQUNuRCxNQUFNQSxDQUFDO1FBQ2hCO1FBQ0EsSUFBSTJHLFNBQVMsQ0FBQ3hKLEtBQUssRUFBRTtVQUNqQixJQUFJLENBQUN1RixlQUFlLEVBQUU7WUFDbEJuUSxNQUFNLENBQUNrUyxNQUFNLENBQUNDLElBQUksQ0FBQyxrQkFBa0IsRUFBRWlDLFNBQVMsQ0FBQ3hKLEtBQUssRUFBRXZILFNBQVMsRUFBRTJPLFVBQVUsQ0FBQztVQUNsRjtVQUNBLE1BQU1vQyxTQUFTLENBQUN4SixLQUFLO1FBQ3pCO1FBQ0EsSUFBSXhFLEtBQStCLEVBQUUsRUFJcEM7UUFDRCxJQUFJLENBQUMrSixlQUFlLEVBQUU7VUFDbEJuUSxNQUFNLENBQUNrUyxNQUFNLENBQUNDLElBQUksQ0FBQyxxQkFBcUIsRUFBRTFOLEVBQUUsRUFBRXVOLFVBQVUsQ0FBQztRQUM3RDtRQUNBO1FBQ0EsTUFBTWtGLFNBQVMsR0FBRyxNQUFNO1FBQ3hCLElBQUlaLFlBQVksSUFBSVksU0FBUyxDQUFDbFQsSUFBSSxDQUFDUyxFQUFFLENBQUMsRUFBRTtVQUNwQyxJQUFJLENBQUMrTixZQUFZLENBQUMvTixFQUFFLENBQUM7UUFDekI7TUFDSjtNQUNBLE9BQU8sSUFBSTtJQUNmLENBQUMsQ0FBQyxPQUFPeUgsR0FBRyxFQUFFO01BQ1YsSUFBSSxDQUFDLENBQUMsRUFBRTNMLFFBQVEsQ0FBQ2YsT0FBTyxFQUFFME0sR0FBRyxDQUFDLElBQUlBLEdBQUcsQ0FBQ3pKLFNBQVMsRUFBRTtRQUM3QyxPQUFPLEtBQUs7TUFDaEI7TUFDQSxNQUFNeUosR0FBRztJQUNiO0VBQ0o7RUFDQXFHLFdBQVdBLENBQUNqSSxNQUFNLEVBQUVwRyxHQUFHLEVBQUVPLEVBQUUsRUFBRS9CLE9BQU8sRUFBRTtJQUNsQyxJQUFJQSxPQUFPLEtBQUssS0FBSyxDQUFDLEVBQUVBLE9BQU8sR0FBRyxDQUFDLENBQUM7SUFDcEMsSUFBSSxNQUF1QztNQUN2QyxJQUFJLE9BQU8rRyxNQUFNLENBQUNDLE9BQU8sS0FBSyxXQUFXLEVBQUU7UUFDdkM0RSxPQUFPLENBQUMxRCxLQUFLLENBQUMsMkNBQTJDLENBQUM7UUFDMUQ7TUFDSjtNQUNBLElBQUksT0FBT25CLE1BQU0sQ0FBQ0MsT0FBTyxDQUFDWSxNQUFNLENBQUMsS0FBSyxXQUFXLEVBQUU7UUFDL0NnRSxPQUFPLENBQUMxRCxLQUFLLENBQUMsMEJBQTBCLEdBQUdOLE1BQU0sR0FBRyxtQkFBbUIsQ0FBQztRQUN4RTtNQUNKO0lBQ0o7SUFDQSxJQUFJQSxNQUFNLEtBQUssV0FBVyxJQUFJLENBQUMsQ0FBQyxFQUFFMUosTUFBTSxDQUFDdVcsTUFBTSxFQUFFLENBQUMsS0FBSzFTLEVBQUUsRUFBRTtNQUN2RCxJQUFJLENBQUMyUyxRQUFRLEdBQUcxVSxPQUFPLENBQUMyTixPQUFPO01BQy9CNUcsTUFBTSxDQUFDQyxPQUFPLENBQUNZLE1BQU0sQ0FBQyxDQUFDO1FBQ25CcEcsR0FBRztRQUNITyxFQUFFO1FBQ0YvQixPQUFPO1FBQ1AyVSxHQUFHLEVBQUUsSUFBSTtRQUNUM0MsR0FBRyxFQUFFLElBQUksQ0FBQ3hILElBQUksR0FBRzVDLE1BQU0sS0FBSyxXQUFXLEdBQUcsSUFBSSxDQUFDNEMsSUFBSSxHQUFHM04sU0FBUyxDQUFDO01BQ3BFLENBQUM7TUFBRTtNQUNIO01BQ0E7TUFDQSxFQUFFLEVBQUVrRixFQUFFLENBQUM7SUFDWDtFQUNKO0VBQ0EsTUFBTTZTLG9CQUFvQkEsQ0FBQ3BMLEdBQUcsRUFBRWpKLFFBQVEsRUFBRTZFLEtBQUssRUFBRXJELEVBQUUsRUFBRXVOLFVBQVUsRUFBRXVGLGFBQWEsRUFBRTtJQUM1RSxJQUFJckwsR0FBRyxDQUFDekosU0FBUyxFQUFFO01BQ2Y7TUFDQSxNQUFNeUosR0FBRztJQUNiO0lBQ0EsSUFBSSxDQUFDLENBQUMsRUFBRTdMLFlBQVksQ0FBQ21YLFlBQVksRUFBRXRMLEdBQUcsQ0FBQyxJQUFJcUwsYUFBYSxFQUFFO01BQ3REdlgsTUFBTSxDQUFDa1MsTUFBTSxDQUFDQyxJQUFJLENBQUMsa0JBQWtCLEVBQUVqRyxHQUFHLEVBQUV6SCxFQUFFLEVBQUV1TixVQUFVLENBQUM7TUFDM0Q7TUFDQTtNQUNBO01BQ0E7TUFDQTtNQUNBeEYsb0JBQW9CLENBQUM7UUFDakJ0SSxHQUFHLEVBQUVPLEVBQUU7UUFDUDNCLE1BQU0sRUFBRTtNQUNaLENBQUMsQ0FBQztNQUNGO01BQ0E7TUFDQSxNQUFNVCxzQkFBc0IsQ0FBQyxDQUFDO0lBQ2xDO0lBQ0FpTSxPQUFPLENBQUMxRCxLQUFLLENBQUNzQixHQUFHLENBQUM7SUFDbEIsSUFBSTtNQUNBLElBQUlvSixLQUFLO01BQ1QsTUFBTTtRQUFFL1AsSUFBSSxFQUFFeVAsU0FBUztRQUFFeUM7TUFBWSxDQUFDLEdBQUcsTUFBTSxJQUFJLENBQUMxQixjQUFjLENBQUMsU0FBUyxDQUFDO01BQzdFLE1BQU0zQixTQUFTLEdBQUc7UUFDZGtCLEtBQUs7UUFDTE4sU0FBUztRQUNUeUMsV0FBVztRQUNYdkwsR0FBRztRQUNIdEIsS0FBSyxFQUFFc0I7TUFDWCxDQUFDO01BQ0QsSUFBSSxDQUFDa0ksU0FBUyxDQUFDa0IsS0FBSyxFQUFFO1FBQ2xCLElBQUk7VUFDQWxCLFNBQVMsQ0FBQ2tCLEtBQUssR0FBRyxNQUFNLElBQUksQ0FBQ29DLGVBQWUsQ0FBQzFDLFNBQVMsRUFBRTtZQUNwRDlJLEdBQUc7WUFDSGpKLFFBQVE7WUFDUjZFO1VBQ0osQ0FBQyxDQUFDO1FBQ04sQ0FBQyxDQUFDLE9BQU82UCxNQUFNLEVBQUU7VUFDYnJKLE9BQU8sQ0FBQzFELEtBQUssQ0FBQyx5Q0FBeUMsRUFBRStNLE1BQU0sQ0FBQztVQUNoRXZELFNBQVMsQ0FBQ2tCLEtBQUssR0FBRyxDQUFDLENBQUM7UUFDeEI7TUFDSjtNQUNBLE9BQU9sQixTQUFTO0lBQ3BCLENBQUMsQ0FBQyxPQUFPd0QsWUFBWSxFQUFFO01BQ25CLE9BQU8sSUFBSSxDQUFDTixvQkFBb0IsQ0FBQyxDQUFDLENBQUMsRUFBRS9XLFFBQVEsQ0FBQ2YsT0FBTyxFQUFFb1ksWUFBWSxDQUFDLEdBQUdBLFlBQVksR0FBRzFZLE1BQU0sQ0FBQ0MsY0FBYyxDQUFDLElBQUlvRCxLQUFLLENBQUNxVixZQUFZLEdBQUcsRUFBRSxDQUFDLEVBQUUsbUJBQW1CLEVBQUU7UUFDM0p2WSxLQUFLLEVBQUUsTUFBTTtRQUNiUyxVQUFVLEVBQUUsS0FBSztRQUNqQjBDLFlBQVksRUFBRTtNQUNsQixDQUFDLENBQUMsRUFBRVMsUUFBUSxFQUFFNkUsS0FBSyxFQUFFckQsRUFBRSxFQUFFdU4sVUFBVSxFQUFFLElBQUksQ0FBQztJQUM5QztFQUNKO0VBQ0EsTUFBTXFDLFlBQVlBLENBQUMvTSxLQUFLLEVBQUU7SUFDdEIsSUFBSTtNQUFFb0YsS0FBSyxFQUFFbUwsY0FBYztNQUFFNVUsUUFBUTtNQUFFNkUsS0FBSztNQUFFckQsRUFBRTtNQUFFRSxVQUFVO01BQUVxTixVQUFVO01BQUVyTyxNQUFNO01BQUVxSCxhQUFhO01BQUVzSixTQUFTO01BQUVqSix3QkFBd0I7TUFBRThFLGVBQWU7TUFBRTRDLG1CQUFtQjtNQUFFaUQ7SUFBVyxDQUFDLEdBQUcxTyxLQUFLO0lBQ2hNO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtJQUFRLElBQUlvRixLQUFLLEdBQUdtTCxjQUFjO0lBQzFCLElBQUk7TUFDQSxJQUFJQyxZQUFZLEVBQUVDLGFBQWEsRUFBRUMsYUFBYSxFQUFFQyxjQUFjO01BQzlELElBQUlDLFlBQVksR0FBRyxJQUFJLENBQUN4RixVQUFVLENBQUNoRyxLQUFLLENBQUM7TUFDekMsSUFBSXNGLFVBQVUsQ0FBQzNCLE9BQU8sSUFBSTZILFlBQVksSUFBSSxJQUFJLENBQUN4TCxLQUFLLEtBQUtBLEtBQUssRUFBRTtRQUM1RCxPQUFPd0wsWUFBWTtNQUN2QjtNQUNBLE1BQU1yTCxlQUFlLEdBQUdKLG1CQUFtQixDQUFDO1FBQ3hDQyxLQUFLO1FBQ0w1SixNQUFNLEVBQUU7TUFDWixDQUFDLENBQUM7TUFDRixJQUFJa0ksYUFBYSxFQUFFO1FBQ2ZrTixZQUFZLEdBQUd0USxTQUFTO01BQzVCO01BQ0EsSUFBSXVRLGVBQWUsR0FBR0QsWUFBWSxJQUFJLEVBQUUsU0FBUyxJQUFJQSxZQUFZLENBQUMsU0FBMEMsR0FBR0EsQ0FBWSxHQUFHdFEsU0FBUztNQUN2SSxNQUFNd0QsWUFBWSxHQUFHK0UsZUFBZTtNQUNwQyxNQUFNaUksbUJBQW1CLEdBQUc7UUFDeEJqUCxRQUFRLEVBQUUsSUFBSSxDQUFDcEcsVUFBVSxDQUFDc1YsV0FBVyxDQUFDO1VBQ2xDL00sSUFBSSxFQUFFLENBQUMsQ0FBQyxFQUFFcEssVUFBVSxDQUFDK1Asb0JBQW9CLEVBQUU7WUFDdkNoTyxRQUFRO1lBQ1I2RTtVQUNKLENBQUMsQ0FBQztVQUNGd1EsaUJBQWlCLEVBQUUsSUFBSTtVQUN2QmxWLE1BQU0sRUFBRTRTLFVBQVUsR0FBRyxNQUFNLEdBQUdyUixVQUFVO1VBQ3hDaEI7UUFDSixDQUFDLENBQUM7UUFDRnFILGFBQWEsRUFBRSxJQUFJO1FBQ25CQyxjQUFjLEVBQUUsSUFBSSxDQUFDMkYsS0FBSztRQUMxQjFGLFNBQVMsRUFBRSxJQUFJO1FBQ2ZKLGFBQWEsRUFBRU0sWUFBWSxHQUFHLElBQUksQ0FBQ21OLEdBQUcsR0FBRyxJQUFJLENBQUNDLEdBQUc7UUFDakRyTixZQUFZLEVBQUUsQ0FBQ21KLFNBQVM7UUFDeEJ2SixVQUFVLEVBQUUsS0FBSztRQUNqQk0sd0JBQXdCO1FBQ3hCRDtNQUNKLENBQUM7TUFDRCxJQUFJbkMsSUFBSSxHQUFHa0gsZUFBZSxJQUFJLENBQUM0QyxtQkFBbUIsR0FBRyxJQUFJLEdBQUcsTUFBTWhLLHFCQUFxQixDQUFDO1FBQ3BGQyxTQUFTLEVBQUVBLENBQUEsS0FBSTZCLGFBQWEsQ0FBQ3VOLG1CQUFtQixDQUFDO1FBQ2pEaFYsTUFBTSxFQUFFNFMsVUFBVSxHQUFHLE1BQU0sR0FBR3JSLFVBQVU7UUFDeENoQixNQUFNLEVBQUVBLE1BQU07UUFDZGIsTUFBTSxFQUFFO01BQ1osQ0FBQyxDQUFDLENBQUNtSixLQUFLLENBQUVDLEdBQUcsSUFBRztRQUNaO1FBQ0E7UUFDQTtRQUNBO1FBQ0EsSUFBSWlFLGVBQWUsRUFBRTtVQUNqQixPQUFPLElBQUk7UUFDZjtRQUNBLE1BQU1qRSxHQUFHO01BQ2IsQ0FBQyxDQUFDO01BQ0Y7TUFDQTtNQUNBLElBQUlqRCxJQUFJLEtBQUtoRyxRQUFRLEtBQUssU0FBUyxJQUFJQSxRQUFRLEtBQUssTUFBTSxDQUFDLEVBQUU7UUFDekRnRyxJQUFJLENBQUNDLE1BQU0sR0FBR3RCLFNBQVM7TUFDM0I7TUFDQSxJQUFJdUksZUFBZSxFQUFFO1FBQ2pCLElBQUksQ0FBQ2xILElBQUksRUFBRTtVQUNQQSxJQUFJLEdBQUc7WUFDSEcsSUFBSSxFQUFFaUUsSUFBSSxDQUFDNEksYUFBYSxDQUFDWDtVQUM3QixDQUFDO1FBQ0wsQ0FBQyxNQUFNO1VBQ0hyTSxJQUFJLENBQUNHLElBQUksR0FBR2lFLElBQUksQ0FBQzRJLGFBQWEsQ0FBQ1gsS0FBSztRQUN4QztNQUNKO01BQ0F6SSxlQUFlLENBQUMsQ0FBQztNQUNqQixJQUFJLENBQUM1RCxJQUFJLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHLENBQUM2TyxZQUFZLEdBQUc3TyxJQUFJLENBQUNDLE1BQU0sS0FBSyxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUc0TyxZQUFZLENBQUN6UCxJQUFJLE1BQU0sbUJBQW1CLElBQUksQ0FBQ1ksSUFBSSxJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBRyxDQUFDOE8sYUFBYSxHQUFHOU8sSUFBSSxDQUFDQyxNQUFNLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHNk8sYUFBYSxDQUFDMVAsSUFBSSxNQUFNLG1CQUFtQixFQUFFO1FBQzlPLE9BQU9ZLElBQUksQ0FBQ0MsTUFBTTtNQUN0QjtNQUNBLElBQUksQ0FBQ0QsSUFBSSxJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBRyxDQUFDK08sYUFBYSxHQUFHL08sSUFBSSxDQUFDQyxNQUFNLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHOE8sYUFBYSxDQUFDM1AsSUFBSSxNQUFNLFNBQVMsRUFBRTtRQUM3RyxNQUFNb1EsYUFBYSxHQUFHLENBQUMsQ0FBQyxFQUFFclksb0JBQW9CLENBQUNnRixtQkFBbUIsRUFBRTZELElBQUksQ0FBQ0MsTUFBTSxDQUFDeEUsWUFBWSxDQUFDO1FBQzdGLE1BQU1RLEtBQUssR0FBRyxNQUFNLElBQUksQ0FBQ25DLFVBQVUsQ0FBQ29FLFdBQVcsQ0FBQyxDQUFDO1FBQ2pEO1FBQ0E7UUFDQTtRQUNBO1FBQ0EsSUFBSSxDQUFDZ0osZUFBZSxJQUFJakwsS0FBSyxDQUFDSSxRQUFRLENBQUNtVCxhQUFhLENBQUMsRUFBRTtVQUNuRC9MLEtBQUssR0FBRytMLGFBQWE7VUFDckJ4VixRQUFRLEdBQUdnRyxJQUFJLENBQUNDLE1BQU0sQ0FBQ3hFLFlBQVk7VUFDbkNvRCxLQUFLLEdBQUc7WUFDSixHQUFHQSxLQUFLO1lBQ1IsR0FBR21CLElBQUksQ0FBQ0MsTUFBTSxDQUFDakIsUUFBUSxDQUFDSDtVQUM1QixDQUFDO1VBQ0RuRCxVQUFVLEdBQUcsQ0FBQyxDQUFDLEVBQUVwRCxlQUFlLENBQUNnQyxjQUFjLEVBQUUsQ0FBQyxDQUFDLEVBQUU3QyxvQkFBb0IsQ0FBQytHLG1CQUFtQixFQUFFd0IsSUFBSSxDQUFDQyxNQUFNLENBQUNqQixRQUFRLENBQUNoRixRQUFRLEVBQUUsSUFBSSxDQUFDZ0QsT0FBTyxDQUFDLENBQUNoRCxRQUFRLENBQUM7VUFDcko7VUFDQWlWLFlBQVksR0FBRyxJQUFJLENBQUN4RixVQUFVLENBQUNoRyxLQUFLLENBQUM7VUFDckMsSUFBSXNGLFVBQVUsQ0FBQzNCLE9BQU8sSUFBSTZILFlBQVksSUFBSSxJQUFJLENBQUN4TCxLQUFLLEtBQUtBLEtBQUssSUFBSSxDQUFDMUIsYUFBYSxFQUFFO1lBQzlFO1lBQ0E7WUFDQTtZQUNBLE9BQU87Y0FDSCxHQUFHa04sWUFBWTtjQUNmeEw7WUFDSixDQUFDO1VBQ0w7UUFDSjtNQUNKO01BQ0EsSUFBSSxDQUFDLENBQUMsRUFBRS9LLFdBQVcsQ0FBQytXLFVBQVUsRUFBRWhNLEtBQUssQ0FBQyxFQUFFO1FBQ3BDRixvQkFBb0IsQ0FBQztVQUNqQnRJLEdBQUcsRUFBRU8sRUFBRTtVQUNQM0IsTUFBTSxFQUFFO1FBQ1osQ0FBQyxDQUFDO1FBQ0YsT0FBTyxJQUFJRixPQUFPLENBQUMsTUFBSSxDQUFDLENBQUMsQ0FBQztNQUM5QjtNQUNBLE1BQU13UixTQUFTLEdBQUcrRCxlQUFlLEtBQUksTUFBTSxJQUFJLENBQUNwQyxjQUFjLENBQUNySixLQUFLLENBQUMsQ0FBQ3JGLElBQUksQ0FBRXNSLEdBQUcsS0FBSTtRQUMzRTNELFNBQVMsRUFBRTJELEdBQUcsQ0FBQ3BULElBQUk7UUFDbkJrUyxXQUFXLEVBQUVrQixHQUFHLENBQUNsQixXQUFXO1FBQzVCbEMsT0FBTyxFQUFFb0QsR0FBRyxDQUFDQyxHQUFHLENBQUNyRCxPQUFPO1FBQ3hCQyxPQUFPLEVBQUVtRCxHQUFHLENBQUNDLEdBQUcsQ0FBQ3BEO01BQ3JCLENBQUMsQ0FBQyxDQUFDO01BQ1AsSUFBSSxNQUF1QztRQUN2QyxNQUFNO1VBQUVxRDtRQUFtQixDQUFDLEdBQUczWSxtQkFBTyxDQUFDLDRHQUE2QixDQUFDO1FBQ3JFLElBQUksQ0FBQzJZLGtCQUFrQixDQUFDekUsU0FBUyxDQUFDWSxTQUFTLENBQUMsRUFBRTtVQUMxQyxNQUFNOVYsTUFBTSxDQUFDQyxjQUFjLENBQUMsSUFBSW9ELEtBQUssQ0FBQyx3REFBd0QsR0FBR1UsUUFBUSxHQUFHLEdBQUcsQ0FBQyxFQUFFLG1CQUFtQixFQUFFO1lBQ25JNUQsS0FBSyxFQUFFLE1BQU07WUFDYlMsVUFBVSxFQUFFLEtBQUs7WUFDakIwQyxZQUFZLEVBQUU7VUFDbEIsQ0FBQyxDQUFDO1FBQ047TUFDSjtNQUNBLE1BQU1zVyxpQkFBaUIsR0FBRzdQLElBQUksSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUcsQ0FBQ2dQLGNBQWMsR0FBR2hQLElBQUksQ0FBQ3BELFFBQVEsS0FBSyxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUdvUyxjQUFjLENBQUN6UixPQUFPLENBQUN6RyxHQUFHLENBQUMsbUJBQW1CLENBQUM7TUFDckosTUFBTWdaLGVBQWUsR0FBRzNFLFNBQVMsQ0FBQ21CLE9BQU8sSUFBSW5CLFNBQVMsQ0FBQ29CLE9BQU87TUFDOUQ7TUFDQTtNQUNBLElBQUlzRCxpQkFBaUIsS0FBSzdQLElBQUksSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUdBLElBQUksQ0FBQ0UsUUFBUSxDQUFDLEVBQUU7UUFDOUQsT0FBTyxJQUFJLENBQUNxUCxHQUFHLENBQUN2UCxJQUFJLENBQUNFLFFBQVEsQ0FBQztNQUNsQztNQUNBLE1BQU07UUFBRW1NLEtBQUs7UUFBRWhNO01BQVMsQ0FBQyxHQUFHLE1BQU0sSUFBSSxDQUFDMFAsUUFBUSxDQUFDLFlBQVU7UUFDdEQsSUFBSUQsZUFBZSxFQUFFO1VBQ2pCLElBQUksQ0FBQzlQLElBQUksSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUdBLElBQUksQ0FBQ0csSUFBSSxLQUFLLENBQUMwUCxpQkFBaUIsRUFBRTtZQUMzRCxPQUFPO2NBQ0h4UCxRQUFRLEVBQUVMLElBQUksQ0FBQ0ssUUFBUTtjQUN2QmdNLEtBQUssRUFBRXJNLElBQUksQ0FBQ0c7WUFDaEIsQ0FBQztVQUNMO1VBQ0EsTUFBTUQsUUFBUSxHQUFHLENBQUNGLElBQUksSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUdBLElBQUksQ0FBQ0UsUUFBUSxJQUFJRixJQUFJLENBQUNFLFFBQVEsR0FBRyxJQUFJLENBQUNwRyxVQUFVLENBQUNzVixXQUFXLENBQUM7WUFDbkcvTSxJQUFJLEVBQUUsQ0FBQyxDQUFDLEVBQUVwSyxVQUFVLENBQUMrUCxvQkFBb0IsRUFBRTtjQUN2Q2hPLFFBQVE7Y0FDUjZFO1lBQ0osQ0FBQyxDQUFDO1lBQ0YxRSxNQUFNLEVBQUV1QixVQUFVO1lBQ2xCaEI7VUFDSixDQUFDLENBQUM7VUFDRixNQUFNc1YsT0FBTyxHQUFHLE1BQU1wTyxhQUFhLENBQUM7WUFDaEMxQixRQUFRO1lBQ1I4QixjQUFjLEVBQUUsSUFBSSxDQUFDMkYsS0FBSztZQUMxQjFGLFNBQVMsRUFBRSxJQUFJO1lBQ2ZKLGFBQWEsRUFBRWdPLGlCQUFpQixHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQ04sR0FBRztZQUNoRHJOLFlBQVksRUFBRSxDQUFDbUosU0FBUztZQUN4QnZKLFVBQVUsRUFBRSxLQUFLO1lBQ2pCTTtVQUNKLENBQUMsQ0FBQztVQUNGLE9BQU87WUFDSC9CLFFBQVEsRUFBRTJQLE9BQU8sQ0FBQzNQLFFBQVE7WUFDMUJnTSxLQUFLLEVBQUUyRCxPQUFPLENBQUM3UCxJQUFJLElBQUksQ0FBQztVQUM1QixDQUFDO1FBQ0w7UUFDQSxPQUFPO1VBQ0g1QyxPQUFPLEVBQUUsQ0FBQyxDQUFDO1VBQ1g4TyxLQUFLLEVBQUUsTUFBTSxJQUFJLENBQUNvQyxlQUFlLENBQUN0RCxTQUFTLENBQUNZLFNBQVM7VUFBRTtVQUN2RDtZQUNJL1IsUUFBUTtZQUNSNkUsS0FBSztZQUNMMUUsTUFBTSxFQUFFcUIsRUFBRTtZQUNWZCxNQUFNO1lBQ05zQyxPQUFPLEVBQUUsSUFBSSxDQUFDQSxPQUFPO1lBQ3JCdUMsYUFBYSxFQUFFLElBQUksQ0FBQ0E7VUFDeEIsQ0FBQztRQUNMLENBQUM7TUFDTCxDQUFDLENBQUM7TUFDRjtNQUNBO01BQ0E7TUFDQSxJQUFJNEwsU0FBUyxDQUFDb0IsT0FBTyxJQUFJNEMsbUJBQW1CLENBQUNqUCxRQUFRLElBQUlHLFFBQVEsRUFBRTtRQUMvRCxPQUFPLElBQUksQ0FBQ2tQLEdBQUcsQ0FBQ2xQLFFBQVEsQ0FBQztNQUM3QjtNQUNBO01BQ0E7TUFDQSxJQUFJLENBQUMsSUFBSSxDQUFDZ0wsU0FBUyxJQUFJRixTQUFTLENBQUNtQixPQUFPLFNBQTBDLElBQUksQ0FBZ0IsRUFBRSxFQU12RztNQUNERCxLQUFLLENBQUNHLFNBQVMsR0FBR3ZXLE1BQU0sQ0FBQ29ELE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRWdULEtBQUssQ0FBQ0csU0FBUyxDQUFDO01BQ3BEckIsU0FBUyxDQUFDa0IsS0FBSyxHQUFHQSxLQUFLO01BQ3ZCbEIsU0FBUyxDQUFDMUgsS0FBSyxHQUFHQSxLQUFLO01BQ3ZCMEgsU0FBUyxDQUFDdE0sS0FBSyxHQUFHQSxLQUFLO01BQ3ZCc00sU0FBUyxDQUFDelAsVUFBVSxHQUFHQSxVQUFVO01BQ2pDLElBQUksQ0FBQytOLFVBQVUsQ0FBQ2hHLEtBQUssQ0FBQyxHQUFHMEgsU0FBUztNQUNsQyxPQUFPQSxTQUFTO0lBQ3BCLENBQUMsQ0FBQyxPQUFPbEksR0FBRyxFQUFFO01BQ1YsT0FBTyxJQUFJLENBQUNvTCxvQkFBb0IsQ0FBQyxDQUFDLENBQUMsRUFBRS9XLFFBQVEsQ0FBQzJZLGNBQWMsRUFBRWhOLEdBQUcsQ0FBQyxFQUFFakosUUFBUSxFQUFFNkUsS0FBSyxFQUFFckQsRUFBRSxFQUFFdU4sVUFBVSxDQUFDO0lBQ3hHO0VBQ0o7RUFDQVMsR0FBR0EsQ0FBQ2hDLEtBQUssRUFBRXhILElBQUksRUFBRXNOLFdBQVcsRUFBRTtJQUMxQixJQUFJLENBQUM5RixLQUFLLEdBQUdBLEtBQUs7SUFDbEIsT0FBTyxJQUFJLENBQUMwSSxHQUFHLENBQUNsUSxJQUFJLEVBQUUsSUFBSSxDQUFDeUosVUFBVSxDQUFDLE9BQU8sQ0FBQyxDQUFDc0MsU0FBUyxFQUFFdUIsV0FBVyxDQUFDO0VBQzFFO0VBQ0E7QUFDSjtBQUNBO0FBQ0E7RUFBTTZDLGNBQWNBLENBQUNDLEVBQUUsRUFBRTtJQUNqQixJQUFJLENBQUNDLElBQUksR0FBR0QsRUFBRTtFQUNsQjtFQUNBL0csZUFBZUEsQ0FBQzdOLEVBQUUsRUFBRTtJQUNoQixJQUFJLENBQUMsSUFBSSxDQUFDckIsTUFBTSxFQUFFLE9BQU8sS0FBSztJQUM5QixNQUFNLENBQUNtVyxZQUFZLEVBQUVDLE9BQU8sQ0FBQyxHQUFHLElBQUksQ0FBQ3BXLE1BQU0sQ0FBQ3VNLEtBQUssQ0FBQyxHQUFHLEVBQUUsQ0FBQyxDQUFDO0lBQ3pELE1BQU0sQ0FBQzhKLFlBQVksRUFBRUMsT0FBTyxDQUFDLEdBQUdqVixFQUFFLENBQUNrTCxLQUFLLENBQUMsR0FBRyxFQUFFLENBQUMsQ0FBQztJQUNoRDtJQUNBLElBQUkrSixPQUFPLElBQUlILFlBQVksS0FBS0UsWUFBWSxJQUFJRCxPQUFPLEtBQUtFLE9BQU8sRUFBRTtNQUNqRSxPQUFPLElBQUk7SUFDZjtJQUNBO0lBQ0EsSUFBSUgsWUFBWSxLQUFLRSxZQUFZLEVBQUU7TUFDL0IsT0FBTyxLQUFLO0lBQ2hCO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQSxPQUFPRCxPQUFPLEtBQUtFLE9BQU87RUFDOUI7RUFDQWxILFlBQVlBLENBQUMvTixFQUFFLEVBQUU7SUFDYixNQUFNLEdBQUdrRSxJQUFJLEdBQUcsRUFBRSxDQUFDLEdBQUdsRSxFQUFFLENBQUNrTCxLQUFLLENBQUMsR0FBRyxFQUFFLENBQUMsQ0FBQztJQUN0QyxDQUFDLENBQUMsRUFBRXhOLG1CQUFtQixDQUFDd1gsa0JBQWtCLEVBQUUsTUFBSTtNQUM1QztNQUNBO01BQ0EsSUFBSWhSLElBQUksS0FBSyxFQUFFLElBQUlBLElBQUksS0FBSyxLQUFLLEVBQUU7UUFDL0JjLE1BQU0sQ0FBQ21RLFFBQVEsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDO1FBQ3JCO01BQ0o7TUFDQTtNQUNBLE1BQU1DLE9BQU8sR0FBR0Msa0JBQWtCLENBQUNuUixJQUFJLENBQUM7TUFDeEM7TUFDQSxNQUFNb1IsSUFBSSxHQUFHaEQsUUFBUSxDQUFDaUQsY0FBYyxDQUFDSCxPQUFPLENBQUM7TUFDN0MsSUFBSUUsSUFBSSxFQUFFO1FBQ05BLElBQUksQ0FBQ0UsY0FBYyxDQUFDLENBQUM7UUFDckI7TUFDSjtNQUNBO01BQ0E7TUFDQSxNQUFNQyxNQUFNLEdBQUduRCxRQUFRLENBQUNvRCxpQkFBaUIsQ0FBQ04sT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDO01BQ3JELElBQUlLLE1BQU0sRUFBRTtRQUNSQSxNQUFNLENBQUNELGNBQWMsQ0FBQyxDQUFDO01BQzNCO0lBQ0osQ0FBQyxFQUFFO01BQ0NHLGNBQWMsRUFBRSxJQUFJLENBQUM5SCxlQUFlLENBQUM3TixFQUFFO0lBQzNDLENBQUMsQ0FBQztFQUNOO0VBQ0FtTyxRQUFRQSxDQUFDeFAsTUFBTSxFQUFFO0lBQ2IsT0FBTyxJQUFJLENBQUNBLE1BQU0sS0FBS0EsTUFBTTtFQUNqQztFQUNBO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtFQUFNLE1BQU1pWCxRQUFRQSxDQUFDblcsR0FBRyxFQUFFZCxNQUFNLEVBQUVWLE9BQU8sRUFBRTtJQUNuQyxJQUFJVSxNQUFNLEtBQUssS0FBSyxDQUFDLEVBQUVBLE1BQU0sR0FBR2MsR0FBRztJQUNuQyxJQUFJeEIsT0FBTyxLQUFLLEtBQUssQ0FBQyxFQUFFQSxPQUFPLEdBQUcsQ0FBQyxDQUFDO0lBQ3BDO0lBQ0EsSUFBSSxNQUF1QztNQUN2QztJQUNKO0lBQ0EsSUFBSSxTQUFpQyxDQUFDLENBQUMsRUFBRVYsTUFBTSxDQUFDc1ksS0FBSyxFQUFFN1EsTUFBTSxDQUFDOFEsU0FBUyxDQUFDQyxTQUFTLENBQUMsRUFBRTtNQUNoRjtNQUNBO01BQ0E7TUFDQTtJQUNKO0lBQ0EsSUFBSTdILE1BQU0sR0FBRyxDQUFDLENBQUMsRUFBRTdSLGlCQUFpQixDQUFDZ0csZ0JBQWdCLEVBQUU1QyxHQUFHLENBQUM7SUFDekQsTUFBTXVXLFdBQVcsR0FBRzlILE1BQU0sQ0FBQzFQLFFBQVE7SUFDbkMsSUFBSTtNQUFFQSxRQUFRO01BQUU2RTtJQUFNLENBQUMsR0FBRzZLLE1BQU07SUFDaEMsTUFBTStILGdCQUFnQixHQUFHelgsUUFBUTtJQUNqQyxJQUFJbUQsS0FBK0IsRUFBRSxFQVdwQztJQUNELE1BQU1sQixLQUFLLEdBQUcsTUFBTSxJQUFJLENBQUNuQyxVQUFVLENBQUNvRSxXQUFXLENBQUMsQ0FBQztJQUNqRCxJQUFJeEMsVUFBVSxHQUFHdkIsTUFBTTtJQUN2QixNQUFNTyxNQUFNLEdBQUcsT0FBT2pCLE9BQU8sQ0FBQ2lCLE1BQU0sS0FBSyxXQUFXLEdBQUdqQixPQUFPLENBQUNpQixNQUFNLElBQUlpRSxTQUFTLEdBQUcsSUFBSSxDQUFDakUsTUFBTTtJQUNoRyxNQUFNcVAsaUJBQWlCLEdBQUcsTUFBTXZULGlCQUFpQixDQUFDO01BQzlDMkQsTUFBTSxFQUFFQSxNQUFNO01BQ2RPLE1BQU0sRUFBRUEsTUFBTTtNQUNkYixNQUFNLEVBQUU7SUFDWixDQUFDLENBQUM7SUFDRixJQUFJc0QsS0FBeUQsRUFBRSxFQW1COUQ7SUFDRHVNLE1BQU0sQ0FBQzFQLFFBQVEsR0FBR2dDLG1CQUFtQixDQUFDME4sTUFBTSxDQUFDMVAsUUFBUSxFQUFFaUMsS0FBSyxDQUFDO0lBQzdELElBQUksQ0FBQyxDQUFDLEVBQUVyRSxVQUFVLENBQUMyRSxjQUFjLEVBQUVtTixNQUFNLENBQUMxUCxRQUFRLENBQUMsRUFBRTtNQUNqREEsUUFBUSxHQUFHMFAsTUFBTSxDQUFDMVAsUUFBUTtNQUMxQjBQLE1BQU0sQ0FBQzFQLFFBQVEsR0FBR0EsUUFBUTtNQUMxQi9ELE1BQU0sQ0FBQ29ELE1BQU0sQ0FBQ3dGLEtBQUssRUFBRSxDQUFDLENBQUMsRUFBRTlHLGFBQWEsQ0FBQ29ILGVBQWUsRUFBRSxDQUFDLENBQUMsRUFBRW5ILFdBQVcsQ0FBQ3dFLGFBQWEsRUFBRWtOLE1BQU0sQ0FBQzFQLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUU3QixVQUFVLENBQUMrQixTQUFTLEVBQUVDLE1BQU0sQ0FBQyxDQUFDSCxRQUFRLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztNQUMzSixJQUFJLENBQUMrUCxpQkFBaUIsRUFBRTtRQUNwQjlPLEdBQUcsR0FBRyxDQUFDLENBQUMsRUFBRWhELFVBQVUsQ0FBQytQLG9CQUFvQixFQUFFMEIsTUFBTSxDQUFDO01BQ3REO0lBQ0o7SUFDQSxNQUFNMUosSUFBSSxHQUFHN0MsTUFBbUQsR0FBRyxDQUFJLEdBQUcsTUFBTTJDLHFCQUFxQixDQUFDO01BQ2xHQyxTQUFTLEVBQUVBLENBQUEsS0FBSTZCLGFBQWEsQ0FBQztRQUNyQjFCLFFBQVEsRUFBRSxJQUFJLENBQUNwRyxVQUFVLENBQUNzVixXQUFXLENBQUM7VUFDbEMvTSxJQUFJLEVBQUUsQ0FBQyxDQUFDLEVBQUVwSyxVQUFVLENBQUMrUCxvQkFBb0IsRUFBRTtZQUN2Q2hPLFFBQVEsRUFBRXlYLGdCQUFnQjtZQUMxQjVTO1VBQ0osQ0FBQyxDQUFDO1VBQ0Z3USxpQkFBaUIsRUFBRSxJQUFJO1VBQ3ZCbFYsTUFBTSxFQUFFdUIsVUFBVTtVQUNsQmhCO1FBQ0osQ0FBQyxDQUFDO1FBQ0ZxSCxhQUFhLEVBQUUsSUFBSTtRQUNuQkMsY0FBYyxFQUFFLEtBQUs7UUFDckJDLFNBQVMsRUFBRSxJQUFJO1FBQ2ZKLGFBQWEsRUFBRSxJQUFJLENBQUMwTixHQUFHO1FBQ3ZCck4sWUFBWSxFQUFFLENBQUMsSUFBSSxDQUFDbUosU0FBUztRQUM3QnZKLFVBQVUsRUFBRTtNQUNoQixDQUFDLENBQUM7TUFDTjNILE1BQU0sRUFBRUEsTUFBTTtNQUNkTyxNQUFNLEVBQUVBLE1BQU07TUFDZGIsTUFBTSxFQUFFO0lBQ1osQ0FBQyxDQUFDO0lBQ0Y7QUFDUjtBQUNBO0FBQ0E7SUFBUSxJQUFJLENBQUNtRyxJQUFJLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHQSxJQUFJLENBQUNDLE1BQU0sQ0FBQ2IsSUFBSSxNQUFNLFNBQVMsRUFBRTtNQUMxRHNLLE1BQU0sQ0FBQzFQLFFBQVEsR0FBR2dHLElBQUksQ0FBQ0MsTUFBTSxDQUFDeEUsWUFBWTtNQUMxQ3pCLFFBQVEsR0FBR2dHLElBQUksQ0FBQ0MsTUFBTSxDQUFDeEUsWUFBWTtNQUNuQ29ELEtBQUssR0FBRztRQUNKLEdBQUdBLEtBQUs7UUFDUixHQUFHbUIsSUFBSSxDQUFDQyxNQUFNLENBQUNqQixRQUFRLENBQUNIO01BQzVCLENBQUM7TUFDRG5ELFVBQVUsR0FBR3NFLElBQUksQ0FBQ0MsTUFBTSxDQUFDakIsUUFBUSxDQUFDaEYsUUFBUTtNQUMxQ2lCLEdBQUcsR0FBRyxDQUFDLENBQUMsRUFBRWhELFVBQVUsQ0FBQytQLG9CQUFvQixFQUFFMEIsTUFBTSxDQUFDO0lBQ3REO0lBQ0E7QUFDUjtBQUNBO0FBQ0E7SUFBUSxJQUFJLENBQUMxSixJQUFJLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHQSxJQUFJLENBQUNDLE1BQU0sQ0FBQ2IsSUFBSSxNQUFNLG1CQUFtQixFQUFFO01BQ3BFO0lBQ0o7SUFDQSxNQUFNcUUsS0FBSyxHQUFHLENBQUMsQ0FBQyxFQUFFdE0sb0JBQW9CLENBQUNnRixtQkFBbUIsRUFBRW5DLFFBQVEsQ0FBQztJQUNyRSxJQUFJLE1BQU0sSUFBSSxDQUFDMkssSUFBSSxDQUFDeEssTUFBTSxFQUFFdUIsVUFBVSxFQUFFakMsT0FBTyxDQUFDaUIsTUFBTSxFQUFFLElBQUksQ0FBQyxFQUFFO01BQzNELElBQUksQ0FBQytPLFVBQVUsQ0FBQytILFdBQVcsQ0FBQyxHQUFHO1FBQzNCM0gsV0FBVyxFQUFFO01BQ2pCLENBQUM7SUFDTDtJQUNBLE1BQU1sUSxPQUFPLENBQUNoRCxHQUFHLENBQUMsQ0FDZCxJQUFJLENBQUNtRCxVQUFVLENBQUM2WCxNQUFNLENBQUNsTyxLQUFLLENBQUMsQ0FBQ3JGLElBQUksQ0FBRXdULEtBQUssSUFBRztNQUN4QyxPQUFPQSxLQUFLLEdBQUdoUSxhQUFhLENBQUM7UUFDekIxQixRQUFRLEVBQUUsQ0FBQ0YsSUFBSSxJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR0EsSUFBSSxDQUFDRyxJQUFJLElBQUlILElBQUksSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUdBLElBQUksQ0FBQ0UsUUFBUSxHQUFHLElBQUksQ0FBQ3BHLFVBQVUsQ0FBQ3NWLFdBQVcsQ0FBQztVQUNoSC9NLElBQUksRUFBRXBILEdBQUc7VUFDVGQsTUFBTSxFQUFFdUIsVUFBVTtVQUNsQmhCLE1BQU0sRUFBRUE7UUFDWixDQUFDLENBQUM7UUFDRnNILGNBQWMsRUFBRSxLQUFLO1FBQ3JCQyxTQUFTLEVBQUUsSUFBSTtRQUNmSixhQUFhLEVBQUUsSUFBSSxDQUFDME4sR0FBRztRQUN2QnJOLFlBQVksRUFBRSxDQUFDLElBQUksQ0FBQ21KLFNBQVM7UUFDN0J2SixVQUFVLEVBQUUsSUFBSTtRQUNoQk0sd0JBQXdCLEVBQUUzSSxPQUFPLENBQUMySSx3QkFBd0IsSUFBSTNJLE9BQU8sQ0FBQ29ZLFFBQVEsSUFBSSxDQUFDLENBQUMxVSxJQUEwQzJVO01BQ2xJLENBQUMsQ0FBQyxDQUFDMVQsSUFBSSxDQUFDLE1BQUksS0FBSyxDQUFDLENBQUM0RSxLQUFLLENBQUMsTUFBSSxLQUFLLENBQUMsR0FBRyxLQUFLO0lBQy9DLENBQUMsQ0FBQyxFQUNGLElBQUksQ0FBQ2xKLFVBQVUsQ0FBQ0wsT0FBTyxDQUFDb1ksUUFBUSxHQUFHLFVBQVUsR0FBRyxVQUFVLENBQUMsQ0FBQ3BPLEtBQUssQ0FBQyxDQUNyRSxDQUFDO0VBQ047RUFDQSxNQUFNcUosY0FBY0EsQ0FBQ3JKLEtBQUssRUFBRTtJQUN4QixNQUFNRyxlQUFlLEdBQUdKLG1CQUFtQixDQUFDO01BQ3hDQyxLQUFLO01BQ0w1SixNQUFNLEVBQUU7SUFDWixDQUFDLENBQUM7SUFDRixJQUFJO01BQ0EsTUFBTWtZLGVBQWUsR0FBRyxNQUFNLElBQUksQ0FBQ2pZLFVBQVUsQ0FBQ2tZLFFBQVEsQ0FBQ3ZPLEtBQUssQ0FBQztNQUM3REcsZUFBZSxDQUFDLENBQUM7TUFDakIsT0FBT21PLGVBQWU7SUFDMUIsQ0FBQyxDQUFDLE9BQU85TyxHQUFHLEVBQUU7TUFDVlcsZUFBZSxDQUFDLENBQUM7TUFDakIsTUFBTVgsR0FBRztJQUNiO0VBQ0o7RUFDQThNLFFBQVFBLENBQUNrQyxFQUFFLEVBQUU7SUFDVCxJQUFJelksU0FBUyxHQUFHLEtBQUs7SUFDckIsTUFBTWtLLE1BQU0sR0FBR0EsQ0FBQSxLQUFJO01BQ2ZsSyxTQUFTLEdBQUcsSUFBSTtJQUNwQixDQUFDO0lBQ0QsSUFBSSxDQUFDbUssR0FBRyxHQUFHRCxNQUFNO0lBQ2pCLE9BQU91TyxFQUFFLENBQUMsQ0FBQyxDQUFDN1QsSUFBSSxDQUFFNEIsSUFBSSxJQUFHO01BQ3JCLElBQUkwRCxNQUFNLEtBQUssSUFBSSxDQUFDQyxHQUFHLEVBQUU7UUFDckIsSUFBSSxDQUFDQSxHQUFHLEdBQUcsSUFBSTtNQUNuQjtNQUNBLElBQUluSyxTQUFTLEVBQUU7UUFDWCxNQUFNeUosR0FBRyxHQUFHaE4sTUFBTSxDQUFDQyxjQUFjLENBQUMsSUFBSW9ELEtBQUssQ0FBQyxpQ0FBaUMsQ0FBQyxFQUFFLG1CQUFtQixFQUFFO1VBQ2pHbEQsS0FBSyxFQUFFLE1BQU07VUFDYlMsVUFBVSxFQUFFLEtBQUs7VUFDakIwQyxZQUFZLEVBQUU7UUFDbEIsQ0FBQyxDQUFDO1FBQ0YwSixHQUFHLENBQUN6SixTQUFTLEdBQUcsSUFBSTtRQUNwQixNQUFNeUosR0FBRztNQUNiO01BQ0EsT0FBT2pELElBQUk7SUFDZixDQUFDLENBQUM7RUFDTjtFQUNBeU8sZUFBZUEsQ0FBQzFDLFNBQVMsRUFBRW1HLEdBQUcsRUFBRTtJQUM1QixNQUFNO01BQUVuRyxTQUFTLEVBQUVvRztJQUFJLENBQUMsR0FBRyxJQUFJLENBQUMxSSxVQUFVLENBQUMsT0FBTyxDQUFDO0lBQ25ELE1BQU0ySSxPQUFPLEdBQUcsSUFBSSxDQUFDQyxRQUFRLENBQUNGLEdBQUcsQ0FBQztJQUNsQ0QsR0FBRyxDQUFDRSxPQUFPLEdBQUdBLE9BQU87SUFDckIsT0FBTyxDQUFDLENBQUMsRUFBRXphLE1BQU0sQ0FBQzJhLG1CQUFtQixFQUFFSCxHQUFHLEVBQUU7TUFDeENDLE9BQU87TUFDUHJHLFNBQVM7TUFDVGxTLE1BQU0sRUFBRSxJQUFJO01BQ1pxWTtJQUNKLENBQUMsQ0FBQztFQUNOO0VBQ0EsSUFBSXpPLEtBQUtBLENBQUEsRUFBRztJQUNSLE9BQU8sSUFBSSxDQUFDK0QsS0FBSyxDQUFDL0QsS0FBSztFQUMzQjtFQUNBLElBQUl6SixRQUFRQSxDQUFBLEVBQUc7SUFDWCxPQUFPLElBQUksQ0FBQ3dOLEtBQUssQ0FBQ3hOLFFBQVE7RUFDOUI7RUFDQSxJQUFJNkUsS0FBS0EsQ0FBQSxFQUFHO0lBQ1IsT0FBTyxJQUFJLENBQUMySSxLQUFLLENBQUMzSSxLQUFLO0VBQzNCO0VBQ0EsSUFBSTFFLE1BQU1BLENBQUEsRUFBRztJQUNULE9BQU8sSUFBSSxDQUFDcU4sS0FBSyxDQUFDck4sTUFBTTtFQUM1QjtFQUNBLElBQUlPLE1BQU1BLENBQUEsRUFBRztJQUNULE9BQU8sSUFBSSxDQUFDOE0sS0FBSyxDQUFDOU0sTUFBTTtFQUM1QjtFQUNBLElBQUk0USxVQUFVQSxDQUFBLEVBQUc7SUFDYixPQUFPLElBQUksQ0FBQzlELEtBQUssQ0FBQzhELFVBQVU7RUFDaEM7RUFDQSxJQUFJRCxTQUFTQSxDQUFBLEVBQUc7SUFDWixPQUFPLElBQUksQ0FBQzdELEtBQUssQ0FBQzZELFNBQVM7RUFDL0I7RUFDQWtILFdBQVdBLENBQUN2WSxRQUFRLEVBQUU2RSxLQUFLLEVBQUVyRCxFQUFFLEVBQUU7SUFBRWdYLFlBQVk7SUFBRTFZLFVBQVU7SUFBRXFZLEdBQUc7SUFBRU0sT0FBTztJQUFFMUcsU0FBUztJQUFFOUksR0FBRztJQUFFeVAsWUFBWTtJQUFFcEgsVUFBVTtJQUFFNVEsTUFBTTtJQUFFc0MsT0FBTztJQUFFdUMsYUFBYTtJQUFFOEksYUFBYTtJQUFFZ0Q7RUFBVSxDQUFDLEVBQUM7SUFDNUs7SUFDQSxJQUFJLENBQUNrRSxHQUFHLEdBQUcsQ0FBQyxDQUFDO0lBQ2I7SUFDQSxJQUFJLENBQUNELEdBQUcsR0FBRyxDQUFDLENBQUM7SUFDYixJQUFJLENBQUNxRCxvQkFBb0IsR0FBRyxJQUFJO0lBQ2hDLElBQUksQ0FBQzFPLElBQUksR0FBRzNOLFNBQVMsQ0FBQyxDQUFDO0lBQ3ZCLElBQUksQ0FBQ3NjLFVBQVUsR0FBSXBPLENBQUMsSUFBRztNQUNuQixNQUFNO1FBQUVtTztNQUFxQixDQUFDLEdBQUcsSUFBSTtNQUNyQyxJQUFJLENBQUNBLG9CQUFvQixHQUFHLEtBQUs7TUFDakMsTUFBTW5MLEtBQUssR0FBR2hELENBQUMsQ0FBQ2dELEtBQUs7TUFDckIsSUFBSSxDQUFDQSxLQUFLLEVBQUU7UUFDUjtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQSxNQUFNO1VBQUV4TixRQUFRO1VBQUU2RTtRQUFNLENBQUMsR0FBRyxJQUFJO1FBQ2hDLElBQUksQ0FBQ3lLLFdBQVcsQ0FBQyxjQUFjLEVBQUUsQ0FBQyxDQUFDLEVBQUVyUixVQUFVLENBQUMrUCxvQkFBb0IsRUFBRTtVQUNsRWhPLFFBQVEsRUFBRSxDQUFDLENBQUMsRUFBRXpCLFlBQVksQ0FBQ2lDLFdBQVcsRUFBRVIsUUFBUSxDQUFDO1VBQ2pENkU7UUFDSixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRWxILE1BQU0sQ0FBQ3VXLE1BQU0sRUFBRSxDQUFDLENBQUM7UUFDekI7TUFDSjtNQUNBO01BQ0EsSUFBSTFHLEtBQUssQ0FBQ3FMLElBQUksRUFBRTtRQUNaclMsTUFBTSxDQUFDK0IsUUFBUSxDQUFDc0IsTUFBTSxDQUFDLENBQUM7UUFDeEI7TUFDSjtNQUNBLElBQUksQ0FBQzJELEtBQUssQ0FBQzRHLEdBQUcsRUFBRTtRQUNaO01BQ0o7TUFDQTtNQUNBLElBQUl1RSxvQkFBb0IsSUFBSSxJQUFJLENBQUNqWSxNQUFNLEtBQUs4TSxLQUFLLENBQUMvTixPQUFPLENBQUNpQixNQUFNLElBQUk4TSxLQUFLLENBQUNoTSxFQUFFLEtBQUssSUFBSSxDQUFDckIsTUFBTSxFQUFFO1FBQzFGO01BQ0o7TUFDQSxJQUFJNE0sWUFBWTtNQUNoQixNQUFNO1FBQUU5TCxHQUFHO1FBQUVPLEVBQUU7UUFBRS9CLE9BQU87UUFBRWdTO01BQUksQ0FBQyxHQUFHakUsS0FBSztNQUN2QyxJQUFJckssS0FBcUMsRUFBRSxFQXNCMUM7TUFDRCxJQUFJLENBQUM4RyxJQUFJLEdBQUd3SCxHQUFHO01BQ2YsTUFBTTtRQUFFelI7TUFBUyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUVuQyxpQkFBaUIsQ0FBQ2dHLGdCQUFnQixFQUFFNUMsR0FBRyxDQUFDO01BQ2pFO01BQ0E7TUFDQSxJQUFJLElBQUksQ0FBQzBNLEtBQUssSUFBSW5NLEVBQUUsS0FBSyxDQUFDLENBQUMsRUFBRWpELFlBQVksQ0FBQ2lDLFdBQVcsRUFBRSxJQUFJLENBQUNMLE1BQU0sQ0FBQyxJQUFJSCxRQUFRLEtBQUssQ0FBQyxDQUFDLEVBQUV6QixZQUFZLENBQUNpQyxXQUFXLEVBQUUsSUFBSSxDQUFDUixRQUFRLENBQUMsRUFBRTtRQUM5SDtNQUNKO01BQ0E7TUFDQTtNQUNBLElBQUksSUFBSSxDQUFDcVcsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDQSxJQUFJLENBQUM3SSxLQUFLLENBQUMsRUFBRTtRQUNoQztNQUNKO01BQ0EsSUFBSSxDQUFDL0MsTUFBTSxDQUFDLGNBQWMsRUFBRXhKLEdBQUcsRUFBRU8sRUFBRSxFQUFFdkYsTUFBTSxDQUFDb0QsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFSSxPQUFPLEVBQUU7UUFDNUQyTixPQUFPLEVBQUUzTixPQUFPLENBQUMyTixPQUFPLElBQUksSUFBSSxDQUFDK0csUUFBUTtRQUN6Q3pULE1BQU0sRUFBRWpCLE9BQU8sQ0FBQ2lCLE1BQU0sSUFBSSxJQUFJLENBQUM2RSxhQUFhO1FBQzVDO1FBQ0E0SCxFQUFFLEVBQUU7TUFDUixDQUFDLENBQUMsRUFBRUosWUFBWSxDQUFDO0lBQ3JCLENBQUM7SUFDRDtJQUNBLE1BQU10RCxLQUFLLEdBQUcsQ0FBQyxDQUFDLEVBQUV0TSxvQkFBb0IsQ0FBQ2dGLG1CQUFtQixFQUFFbkMsUUFBUSxDQUFDO0lBQ3JFO0lBQ0EsSUFBSSxDQUFDeVAsVUFBVSxHQUFHLENBQUMsQ0FBQztJQUNwQjtJQUNBO0lBQ0E7SUFDQSxJQUFJelAsUUFBUSxLQUFLLFNBQVMsRUFBRTtNQUN4QixJQUFJLENBQUN5UCxVQUFVLENBQUNoRyxLQUFLLENBQUMsR0FBRztRQUNyQnNJLFNBQVM7UUFDVGdILE9BQU8sRUFBRSxJQUFJO1FBQ2IxRyxLQUFLLEVBQUVtRyxZQUFZO1FBQ25CdlAsR0FBRztRQUNIcUosT0FBTyxFQUFFa0csWUFBWSxJQUFJQSxZQUFZLENBQUNsRyxPQUFPO1FBQzdDQyxPQUFPLEVBQUVpRyxZQUFZLElBQUlBLFlBQVksQ0FBQ2pHO01BQzFDLENBQUM7SUFDTDtJQUNBLElBQUksQ0FBQzlDLFVBQVUsQ0FBQyxPQUFPLENBQUMsR0FBRztNQUN2QnNDLFNBQVMsRUFBRW9HLEdBQUc7TUFDZDNELFdBQVcsRUFBRTtJQUNqQixDQUFDO0lBQ0Q7SUFDQTtJQUNBLElBQUksQ0FBQ3ZGLE1BQU0sR0FBR2xTLE1BQU0sQ0FBQ2tTLE1BQU07SUFDM0IsSUFBSSxDQUFDblAsVUFBVSxHQUFHQSxVQUFVO0lBQzVCO0lBQ0E7SUFDQSxNQUFNa1osaUJBQWlCLEdBQUcsQ0FBQyxDQUFDLEVBQUVwYixVQUFVLENBQUMyRSxjQUFjLEVBQUV2QyxRQUFRLENBQUMsSUFBSW9LLElBQUksQ0FBQzRJLGFBQWEsQ0FBQ2lHLFVBQVU7SUFDbkcsSUFBSSxDQUFDblcsUUFBUSxHQUFHSyxNQUFrQyxJQUFJLEVBQUU7SUFDeEQsSUFBSSxDQUFDK1MsR0FBRyxHQUFHd0MsWUFBWTtJQUN2QixJQUFJLENBQUMvTyxHQUFHLEdBQUcsSUFBSTtJQUNmLElBQUksQ0FBQzBPLFFBQVEsR0FBR0ksT0FBTztJQUN2QjtJQUNBO0lBQ0EsSUFBSSxDQUFDOUssS0FBSyxHQUFHLElBQUk7SUFDakIsSUFBSSxDQUFDVyxjQUFjLEdBQUcsS0FBSztJQUMzQixJQUFJLENBQUNaLE9BQU8sR0FBRyxDQUFDLEVBQUV0RCxJQUFJLENBQUM0SSxhQUFhLENBQUNtRyxJQUFJLElBQUkvTyxJQUFJLENBQUM0SSxhQUFhLENBQUNvRyxHQUFHLElBQUloUCxJQUFJLENBQUM0SSxhQUFhLENBQUNxRyxxQkFBcUIsSUFBSWpQLElBQUksQ0FBQzRJLGFBQWEsQ0FBQ3NHLE1BQU0sSUFBSSxDQUFDbFAsSUFBSSxDQUFDNEksYUFBYSxDQUFDdUcsR0FBRyxJQUFJLENBQUNQLGlCQUFpQixJQUFJLENBQUM1TyxJQUFJLENBQUM3QixRQUFRLENBQUNpUixNQUFNLElBQUksQ0FBQ3JXLEtBQStCLENBQUM7SUFDM1AsSUFBSUEsS0FBK0IsRUFBRSxFQUtwQztJQUNELElBQUksQ0FBQ3FLLEtBQUssR0FBRztNQUNUL0QsS0FBSztNQUNMekosUUFBUTtNQUNSNkUsS0FBSztNQUNMMUUsTUFBTSxFQUFFNlksaUJBQWlCLEdBQUdoWixRQUFRLEdBQUd3QixFQUFFO01BQ3pDNlAsU0FBUyxFQUFFLENBQUMsQ0FBQ0EsU0FBUztNQUN0QjNRLE1BQU0sRUFBRXlDLE1BQStCLEdBQUd6QyxDQUFNLEdBQUdpRSxTQUFTO01BQzVEMk07SUFDSixDQUFDO0lBQ0QsSUFBSSxDQUFDbUksZ0NBQWdDLEdBQUc5WixPQUFPLENBQUNDLE9BQU8sQ0FBQyxLQUFLLENBQUM7SUFDOUQsSUFBSSxNQUErQjtNQUMvQjtNQUNBO01BQ0EsSUFBSSxDQUFDNEIsRUFBRSxDQUFDSixVQUFVLENBQUMsSUFBSSxDQUFDLEVBQUU7UUFDdEI7UUFDQTtRQUNBLE1BQU0zQixPQUFPLEdBQUc7VUFDWmlCO1FBQ0osQ0FBQztRQUNELE1BQU1QLE1BQU0sR0FBRyxDQUFDLENBQUMsRUFBRXhDLE1BQU0sQ0FBQ3VXLE1BQU0sRUFBRSxDQUFDO1FBQ25DLElBQUksQ0FBQ3VGLGdDQUFnQyxHQUFHamQsaUJBQWlCLENBQUM7VUFDdERxRCxNQUFNLEVBQUUsSUFBSTtVQUNaYSxNQUFNO1VBQ05QO1FBQ0osQ0FBQyxDQUFDLENBQUNpRSxJQUFJLENBQUVjLE9BQU8sSUFBRztVQUNmO1VBQ0E7VUFDQTtVQUNBekYsT0FBTyxDQUFDNk4sa0JBQWtCLEdBQUc5TCxFQUFFLEtBQUt4QixRQUFRO1VBQzVDLElBQUksQ0FBQ3NQLFdBQVcsQ0FBQyxjQUFjLEVBQUVwSyxPQUFPLEdBQUcvRSxNQUFNLEdBQUcsQ0FBQyxDQUFDLEVBQUVsQyxVQUFVLENBQUMrUCxvQkFBb0IsRUFBRTtZQUNyRmhPLFFBQVEsRUFBRSxDQUFDLENBQUMsRUFBRXpCLFlBQVksQ0FBQ2lDLFdBQVcsRUFBRVIsUUFBUSxDQUFDO1lBQ2pENkU7VUFDSixDQUFDLENBQUMsRUFBRTFFLE1BQU0sRUFBRVYsT0FBTyxDQUFDO1VBQ3BCLE9BQU95RixPQUFPO1FBQ2xCLENBQUMsQ0FBQztNQUNOO01BQ0FzQixNQUFNLENBQUNrVCxnQkFBZ0IsQ0FBQyxVQUFVLEVBQUUsSUFBSSxDQUFDZCxVQUFVLENBQUM7TUFDcEQ7TUFDQTtNQUNBLElBQUl6VixLQUFxQyxFQUFFLEVBSTFDO0lBQ0w7RUFDSjtBQUNKO0FBQ0FwRyxNQUFNLENBQUNrUyxNQUFNLEdBQUcsQ0FBQyxDQUFDLEVBQUV2UixLQUFLLENBQUNuQixPQUFPLEVBQUUsQ0FBQyIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXHNoYXJlZFxcbGliXFxyb3V0ZXJcXHJvdXRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyB0c2xpbnQ6ZGlzYWJsZTpuby1jb25zb2xlXG5cInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbjAgJiYgKG1vZHVsZS5leHBvcnRzID0ge1xuICAgIGNyZWF0ZUtleTogbnVsbCxcbiAgICBkZWZhdWx0OiBudWxsLFxuICAgIG1hdGNoZXNNaWRkbGV3YXJlOiBudWxsXG59KTtcbmZ1bmN0aW9uIF9leHBvcnQodGFyZ2V0LCBhbGwpIHtcbiAgICBmb3IodmFyIG5hbWUgaW4gYWxsKU9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIG5hbWUsIHtcbiAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgZ2V0OiBhbGxbbmFtZV1cbiAgICB9KTtcbn1cbl9leHBvcnQoZXhwb3J0cywge1xuICAgIGNyZWF0ZUtleTogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBjcmVhdGVLZXk7XG4gICAgfSxcbiAgICBkZWZhdWx0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIFJvdXRlcjtcbiAgICB9LFxuICAgIG1hdGNoZXNNaWRkbGV3YXJlOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIG1hdGNoZXNNaWRkbGV3YXJlO1xuICAgIH1cbn0pO1xuY29uc3QgX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0ID0gcmVxdWlyZShcIkBzd2MvaGVscGVycy9fL19pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdFwiKTtcbmNvbnN0IF9pbnRlcm9wX3JlcXVpcmVfd2lsZGNhcmQgPSByZXF1aXJlKFwiQHN3Yy9oZWxwZXJzL18vX2ludGVyb3BfcmVxdWlyZV93aWxkY2FyZFwiKTtcbmNvbnN0IF9yZW1vdmV0cmFpbGluZ3NsYXNoID0gcmVxdWlyZShcIi4vdXRpbHMvcmVtb3ZlLXRyYWlsaW5nLXNsYXNoXCIpO1xuY29uc3QgX3JvdXRlbG9hZGVyID0gcmVxdWlyZShcIi4uLy4uLy4uL2NsaWVudC9yb3V0ZS1sb2FkZXJcIik7XG5jb25zdCBfc2NyaXB0ID0gcmVxdWlyZShcIi4uLy4uLy4uL2NsaWVudC9zY3JpcHRcIik7XG5jb25zdCBfaXNlcnJvciA9IC8qI19fUFVSRV9fKi8gX2ludGVyb3BfcmVxdWlyZV93aWxkY2FyZC5fKHJlcXVpcmUoXCIuLi8uLi8uLi9saWIvaXMtZXJyb3JcIikpO1xuY29uc3QgX2Rlbm9ybWFsaXplcGFnZXBhdGggPSByZXF1aXJlKFwiLi4vcGFnZS1wYXRoL2Rlbm9ybWFsaXplLXBhZ2UtcGF0aFwiKTtcbmNvbnN0IF9ub3JtYWxpemVsb2NhbGVwYXRoID0gcmVxdWlyZShcIi4uL2kxOG4vbm9ybWFsaXplLWxvY2FsZS1wYXRoXCIpO1xuY29uc3QgX21pdHQgPSAvKiNfX1BVUkVfXyovIF9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdC5fKHJlcXVpcmUoXCIuLi9taXR0XCIpKTtcbmNvbnN0IF91dGlscyA9IHJlcXVpcmUoXCIuLi91dGlsc1wiKTtcbmNvbnN0IF9pc2R5bmFtaWMgPSByZXF1aXJlKFwiLi91dGlscy9pcy1keW5hbWljXCIpO1xuY29uc3QgX3BhcnNlcmVsYXRpdmV1cmwgPSByZXF1aXJlKFwiLi91dGlscy9wYXJzZS1yZWxhdGl2ZS11cmxcIik7XG5jb25zdCBfcmVzb2x2ZXJld3JpdGVzID0gLyojX19QVVJFX18qLyBfaW50ZXJvcF9yZXF1aXJlX2RlZmF1bHQuXyhyZXF1aXJlKFwiLi91dGlscy9yZXNvbHZlLXJld3JpdGVzXCIpKTtcbmNvbnN0IF9yb3V0ZW1hdGNoZXIgPSByZXF1aXJlKFwiLi91dGlscy9yb3V0ZS1tYXRjaGVyXCIpO1xuY29uc3QgX3JvdXRlcmVnZXggPSByZXF1aXJlKFwiLi91dGlscy9yb3V0ZS1yZWdleFwiKTtcbmNvbnN0IF9mb3JtYXR1cmwgPSByZXF1aXJlKFwiLi91dGlscy9mb3JtYXQtdXJsXCIpO1xuY29uc3QgX2RldGVjdGRvbWFpbmxvY2FsZSA9IHJlcXVpcmUoXCIuLi8uLi8uLi9jbGllbnQvZGV0ZWN0LWRvbWFpbi1sb2NhbGVcIik7XG5jb25zdCBfcGFyc2VwYXRoID0gcmVxdWlyZShcIi4vdXRpbHMvcGFyc2UtcGF0aFwiKTtcbmNvbnN0IF9hZGRsb2NhbGUgPSByZXF1aXJlKFwiLi4vLi4vLi4vY2xpZW50L2FkZC1sb2NhbGVcIik7XG5jb25zdCBfcmVtb3ZlbG9jYWxlID0gcmVxdWlyZShcIi4uLy4uLy4uL2NsaWVudC9yZW1vdmUtbG9jYWxlXCIpO1xuY29uc3QgX3JlbW92ZWJhc2VwYXRoID0gcmVxdWlyZShcIi4uLy4uLy4uL2NsaWVudC9yZW1vdmUtYmFzZS1wYXRoXCIpO1xuY29uc3QgX2FkZGJhc2VwYXRoID0gcmVxdWlyZShcIi4uLy4uLy4uL2NsaWVudC9hZGQtYmFzZS1wYXRoXCIpO1xuY29uc3QgX2hhc2Jhc2VwYXRoID0gcmVxdWlyZShcIi4uLy4uLy4uL2NsaWVudC9oYXMtYmFzZS1wYXRoXCIpO1xuY29uc3QgX3Jlc29sdmVocmVmID0gcmVxdWlyZShcIi4uLy4uLy4uL2NsaWVudC9yZXNvbHZlLWhyZWZcIik7XG5jb25zdCBfaXNhcGlyb3V0ZSA9IHJlcXVpcmUoXCIuLi8uLi8uLi9saWIvaXMtYXBpLXJvdXRlXCIpO1xuY29uc3QgX2dldG5leHRwYXRobmFtZWluZm8gPSByZXF1aXJlKFwiLi91dGlscy9nZXQtbmV4dC1wYXRobmFtZS1pbmZvXCIpO1xuY29uc3QgX2Zvcm1hdG5leHRwYXRobmFtZWluZm8gPSByZXF1aXJlKFwiLi91dGlscy9mb3JtYXQtbmV4dC1wYXRobmFtZS1pbmZvXCIpO1xuY29uc3QgX2NvbXBhcmVzdGF0ZXMgPSByZXF1aXJlKFwiLi91dGlscy9jb21wYXJlLXN0YXRlc1wiKTtcbmNvbnN0IF9pc2xvY2FsdXJsID0gcmVxdWlyZShcIi4vdXRpbHMvaXMtbG9jYWwtdXJsXCIpO1xuY29uc3QgX2lzYm90ID0gcmVxdWlyZShcIi4vdXRpbHMvaXMtYm90XCIpO1xuY29uc3QgX29taXQgPSByZXF1aXJlKFwiLi91dGlscy9vbWl0XCIpO1xuY29uc3QgX2ludGVycG9sYXRlYXMgPSByZXF1aXJlKFwiLi91dGlscy9pbnRlcnBvbGF0ZS1hc1wiKTtcbmNvbnN0IF9oYW5kbGVzbW9vdGhzY3JvbGwgPSByZXF1aXJlKFwiLi91dGlscy9oYW5kbGUtc21vb3RoLXNjcm9sbFwiKTtcbmNvbnN0IF9jb25zdGFudHMgPSByZXF1aXJlKFwiLi4vLi4vLi4vbGliL2NvbnN0YW50c1wiKTtcbmZ1bmN0aW9uIGJ1aWxkQ2FuY2VsbGF0aW9uRXJyb3IoKSB7XG4gICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oT2JqZWN0LmRlZmluZVByb3BlcnR5KG5ldyBFcnJvcignUm91dGUgQ2FuY2VsbGVkJyksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICB2YWx1ZTogXCJFMzE1XCIsXG4gICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICB9KSwge1xuICAgICAgICBjYW5jZWxsZWQ6IHRydWVcbiAgICB9KTtcbn1cbmFzeW5jIGZ1bmN0aW9uIG1hdGNoZXNNaWRkbGV3YXJlKG9wdGlvbnMpIHtcbiAgICBjb25zdCBtYXRjaGVycyA9IGF3YWl0IFByb21pc2UucmVzb2x2ZShvcHRpb25zLnJvdXRlci5wYWdlTG9hZGVyLmdldE1pZGRsZXdhcmUoKSk7XG4gICAgaWYgKCFtYXRjaGVycykgcmV0dXJuIGZhbHNlO1xuICAgIGNvbnN0IHsgcGF0aG5hbWU6IGFzUGF0aG5hbWUgfSA9ICgwLCBfcGFyc2VwYXRoLnBhcnNlUGF0aCkob3B0aW9ucy5hc1BhdGgpO1xuICAgIC8vIHJlbW92ZSBiYXNlUGF0aCBmaXJzdCBzaW5jZSBwYXRoIHByZWZpeCBoYXMgdG8gYmUgaW4gdGhlIG9yZGVyIG9mIGAvJHtiYXNlUGF0aH0vJHtsb2NhbGV9YFxuICAgIGNvbnN0IGNsZWFuZWRBcyA9ICgwLCBfaGFzYmFzZXBhdGguaGFzQmFzZVBhdGgpKGFzUGF0aG5hbWUpID8gKDAsIF9yZW1vdmViYXNlcGF0aC5yZW1vdmVCYXNlUGF0aCkoYXNQYXRobmFtZSkgOiBhc1BhdGhuYW1lO1xuICAgIGNvbnN0IGFzV2l0aEJhc2VQYXRoQW5kTG9jYWxlID0gKDAsIF9hZGRiYXNlcGF0aC5hZGRCYXNlUGF0aCkoKDAsIF9hZGRsb2NhbGUuYWRkTG9jYWxlKShjbGVhbmVkQXMsIG9wdGlvbnMubG9jYWxlKSk7XG4gICAgLy8gQ2hlY2sgb25seSBwYXRoIG1hdGNoIG9uIGNsaWVudC4gTWF0Y2hpbmcgXCJoYXNcIiBzaG91bGQgYmUgZG9uZSBvbiBzZXJ2ZXJcbiAgICAvLyB3aGVyZSB3ZSBjYW4gYWNjZXNzIG1vcmUgaW5mbyBzdWNoIGFzIGhlYWRlcnMsIEh0dHBPbmx5IGNvb2tpZSwgZXRjLlxuICAgIHJldHVybiBtYXRjaGVycy5zb21lKChtKT0+bmV3IFJlZ0V4cChtLnJlZ2V4cCkudGVzdChhc1dpdGhCYXNlUGF0aEFuZExvY2FsZSkpO1xufVxuZnVuY3Rpb24gc3RyaXBPcmlnaW4odXJsKSB7XG4gICAgY29uc3Qgb3JpZ2luID0gKDAsIF91dGlscy5nZXRMb2NhdGlvbk9yaWdpbikoKTtcbiAgICByZXR1cm4gdXJsLnN0YXJ0c1dpdGgob3JpZ2luKSA/IHVybC5zdWJzdHJpbmcob3JpZ2luLmxlbmd0aCkgOiB1cmw7XG59XG5mdW5jdGlvbiBwcmVwYXJlVXJsQXMocm91dGVyLCB1cmwsIGFzKSB7XG4gICAgLy8gSWYgdXJsIGFuZCBhcyBwcm92aWRlZCBhcyBhbiBvYmplY3QgcmVwcmVzZW50YXRpb24sXG4gICAgLy8gd2UnbGwgZm9ybWF0IHRoZW0gaW50byB0aGUgc3RyaW5nIHZlcnNpb24gaGVyZS5cbiAgICBsZXQgW3Jlc29sdmVkSHJlZiwgcmVzb2x2ZWRBc10gPSAoMCwgX3Jlc29sdmVocmVmLnJlc29sdmVIcmVmKShyb3V0ZXIsIHVybCwgdHJ1ZSk7XG4gICAgY29uc3Qgb3JpZ2luID0gKDAsIF91dGlscy5nZXRMb2NhdGlvbk9yaWdpbikoKTtcbiAgICBjb25zdCBocmVmV2FzQWJzb2x1dGUgPSByZXNvbHZlZEhyZWYuc3RhcnRzV2l0aChvcmlnaW4pO1xuICAgIGNvbnN0IGFzV2FzQWJzb2x1dGUgPSByZXNvbHZlZEFzICYmIHJlc29sdmVkQXMuc3RhcnRzV2l0aChvcmlnaW4pO1xuICAgIHJlc29sdmVkSHJlZiA9IHN0cmlwT3JpZ2luKHJlc29sdmVkSHJlZik7XG4gICAgcmVzb2x2ZWRBcyA9IHJlc29sdmVkQXMgPyBzdHJpcE9yaWdpbihyZXNvbHZlZEFzKSA6IHJlc29sdmVkQXM7XG4gICAgY29uc3QgcHJlcGFyZWRVcmwgPSBocmVmV2FzQWJzb2x1dGUgPyByZXNvbHZlZEhyZWYgOiAoMCwgX2FkZGJhc2VwYXRoLmFkZEJhc2VQYXRoKShyZXNvbHZlZEhyZWYpO1xuICAgIGNvbnN0IHByZXBhcmVkQXMgPSBhcyA/IHN0cmlwT3JpZ2luKCgwLCBfcmVzb2x2ZWhyZWYucmVzb2x2ZUhyZWYpKHJvdXRlciwgYXMpKSA6IHJlc29sdmVkQXMgfHwgcmVzb2x2ZWRIcmVmO1xuICAgIHJldHVybiB7XG4gICAgICAgIHVybDogcHJlcGFyZWRVcmwsXG4gICAgICAgIGFzOiBhc1dhc0Fic29sdXRlID8gcHJlcGFyZWRBcyA6ICgwLCBfYWRkYmFzZXBhdGguYWRkQmFzZVBhdGgpKHByZXBhcmVkQXMpXG4gICAgfTtcbn1cbmZ1bmN0aW9uIHJlc29sdmVEeW5hbWljUm91dGUocGF0aG5hbWUsIHBhZ2VzKSB7XG4gICAgY29uc3QgY2xlYW5QYXRobmFtZSA9ICgwLCBfcmVtb3ZldHJhaWxpbmdzbGFzaC5yZW1vdmVUcmFpbGluZ1NsYXNoKSgoMCwgX2Rlbm9ybWFsaXplcGFnZXBhdGguZGVub3JtYWxpemVQYWdlUGF0aCkocGF0aG5hbWUpKTtcbiAgICBpZiAoY2xlYW5QYXRobmFtZSA9PT0gJy80MDQnIHx8IGNsZWFuUGF0aG5hbWUgPT09ICcvX2Vycm9yJykge1xuICAgICAgICByZXR1cm4gcGF0aG5hbWU7XG4gICAgfVxuICAgIC8vIGhhbmRsZSByZXNvbHZpbmcgaHJlZiBmb3IgZHluYW1pYyByb3V0ZXNcbiAgICBpZiAoIXBhZ2VzLmluY2x1ZGVzKGNsZWFuUGF0aG5hbWUpKSB7XG4gICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBhcnJheS1jYWxsYmFjay1yZXR1cm5cbiAgICAgICAgcGFnZXMuc29tZSgocGFnZSk9PntcbiAgICAgICAgICAgIGlmICgoMCwgX2lzZHluYW1pYy5pc0R5bmFtaWNSb3V0ZSkocGFnZSkgJiYgKDAsIF9yb3V0ZXJlZ2V4LmdldFJvdXRlUmVnZXgpKHBhZ2UpLnJlLnRlc3QoY2xlYW5QYXRobmFtZSkpIHtcbiAgICAgICAgICAgICAgICBwYXRobmFtZSA9IHBhZ2U7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICByZXR1cm4gKDAsIF9yZW1vdmV0cmFpbGluZ3NsYXNoLnJlbW92ZVRyYWlsaW5nU2xhc2gpKHBhdGhuYW1lKTtcbn1cbmZ1bmN0aW9uIGdldE1pZGRsZXdhcmVEYXRhKHNvdXJjZSwgcmVzcG9uc2UsIG9wdGlvbnMpIHtcbiAgICBjb25zdCBuZXh0Q29uZmlnID0ge1xuICAgICAgICBiYXNlUGF0aDogb3B0aW9ucy5yb3V0ZXIuYmFzZVBhdGgsXG4gICAgICAgIGkxOG46IHtcbiAgICAgICAgICAgIGxvY2FsZXM6IG9wdGlvbnMucm91dGVyLmxvY2FsZXNcbiAgICAgICAgfSxcbiAgICAgICAgdHJhaWxpbmdTbGFzaDogQm9vbGVhbihwcm9jZXNzLmVudi5fX05FWFRfVFJBSUxJTkdfU0xBU0gpXG4gICAgfTtcbiAgICBjb25zdCByZXdyaXRlSGVhZGVyID0gcmVzcG9uc2UuaGVhZGVycy5nZXQoJ3gtbmV4dGpzLXJld3JpdGUnKTtcbiAgICBsZXQgcmV3cml0ZVRhcmdldCA9IHJld3JpdGVIZWFkZXIgfHwgcmVzcG9uc2UuaGVhZGVycy5nZXQoJ3gtbmV4dGpzLW1hdGNoZWQtcGF0aCcpO1xuICAgIGNvbnN0IG1hdGNoZWRQYXRoID0gcmVzcG9uc2UuaGVhZGVycy5nZXQoX2NvbnN0YW50cy5NQVRDSEVEX1BBVEhfSEVBREVSKTtcbiAgICBpZiAobWF0Y2hlZFBhdGggJiYgIXJld3JpdGVUYXJnZXQgJiYgIW1hdGNoZWRQYXRoLmluY2x1ZGVzKCdfX25leHRfZGF0YV9jYXRjaGFsbCcpICYmICFtYXRjaGVkUGF0aC5pbmNsdWRlcygnL19lcnJvcicpICYmICFtYXRjaGVkUGF0aC5pbmNsdWRlcygnLzQwNCcpKSB7XG4gICAgICAgIC8vIGxldmVyYWdlIHgtbWF0Y2hlZC1wYXRoIHRvIGRldGVjdCBuZXh0LmNvbmZpZy5qcyByZXdyaXRlc1xuICAgICAgICByZXdyaXRlVGFyZ2V0ID0gbWF0Y2hlZFBhdGg7XG4gICAgfVxuICAgIGlmIChyZXdyaXRlVGFyZ2V0KSB7XG4gICAgICAgIGlmIChyZXdyaXRlVGFyZ2V0LnN0YXJ0c1dpdGgoJy8nKSB8fCBwcm9jZXNzLmVudi5fX05FWFRfRVhURVJOQUxfTUlERExFV0FSRV9SRVdSSVRFX1JFU09MVkUpIHtcbiAgICAgICAgICAgIGNvbnN0IHBhcnNlZFJld3JpdGVUYXJnZXQgPSAoMCwgX3BhcnNlcmVsYXRpdmV1cmwucGFyc2VSZWxhdGl2ZVVybCkocmV3cml0ZVRhcmdldCk7XG4gICAgICAgICAgICBjb25zdCBwYXRobmFtZUluZm8gPSAoMCwgX2dldG5leHRwYXRobmFtZWluZm8uZ2V0TmV4dFBhdGhuYW1lSW5mbykocGFyc2VkUmV3cml0ZVRhcmdldC5wYXRobmFtZSwge1xuICAgICAgICAgICAgICAgIG5leHRDb25maWcsXG4gICAgICAgICAgICAgICAgcGFyc2VEYXRhOiB0cnVlXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGxldCBmc1BhdGhuYW1lID0gKDAsIF9yZW1vdmV0cmFpbGluZ3NsYXNoLnJlbW92ZVRyYWlsaW5nU2xhc2gpKHBhdGhuYW1lSW5mby5wYXRobmFtZSk7XG4gICAgICAgICAgICByZXR1cm4gUHJvbWlzZS5hbGwoW1xuICAgICAgICAgICAgICAgIG9wdGlvbnMucm91dGVyLnBhZ2VMb2FkZXIuZ2V0UGFnZUxpc3QoKSxcbiAgICAgICAgICAgICAgICAoMCwgX3JvdXRlbG9hZGVyLmdldENsaWVudEJ1aWxkTWFuaWZlc3QpKClcbiAgICAgICAgICAgIF0pLnRoZW4oKHBhcmFtKT0+e1xuICAgICAgICAgICAgICAgIGxldCBbcGFnZXMsIHsgX19yZXdyaXRlczogcmV3cml0ZXMgfV0gPSBwYXJhbTtcbiAgICAgICAgICAgICAgICBsZXQgYXMgPSAoMCwgX2FkZGxvY2FsZS5hZGRMb2NhbGUpKHBhdGhuYW1lSW5mby5wYXRobmFtZSwgcGF0aG5hbWVJbmZvLmxvY2FsZSk7XG4gICAgICAgICAgICAgICAgaWYgKCgwLCBfaXNkeW5hbWljLmlzRHluYW1pY1JvdXRlKShhcykgfHwgIXJld3JpdGVIZWFkZXIgJiYgcGFnZXMuaW5jbHVkZXMoKDAsIF9ub3JtYWxpemVsb2NhbGVwYXRoLm5vcm1hbGl6ZUxvY2FsZVBhdGgpKCgwLCBfcmVtb3ZlYmFzZXBhdGgucmVtb3ZlQmFzZVBhdGgpKGFzKSwgb3B0aW9ucy5yb3V0ZXIubG9jYWxlcykucGF0aG5hbWUpKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHBhcnNlZFNvdXJjZSA9ICgwLCBfZ2V0bmV4dHBhdGhuYW1laW5mby5nZXROZXh0UGF0aG5hbWVJbmZvKSgoMCwgX3BhcnNlcmVsYXRpdmV1cmwucGFyc2VSZWxhdGl2ZVVybCkoc291cmNlKS5wYXRobmFtZSwge1xuICAgICAgICAgICAgICAgICAgICAgICAgbmV4dENvbmZpZzogcHJvY2Vzcy5lbnYuX19ORVhUX0hBU19SRVdSSVRFUyA/IHVuZGVmaW5lZCA6IG5leHRDb25maWcsXG4gICAgICAgICAgICAgICAgICAgICAgICBwYXJzZURhdGE6IHRydWVcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIGFzID0gKDAsIF9hZGRiYXNlcGF0aC5hZGRCYXNlUGF0aCkocGFyc2VkU291cmNlLnBhdGhuYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgcGFyc2VkUmV3cml0ZVRhcmdldC5wYXRobmFtZSA9IGFzO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAocHJvY2Vzcy5lbnYuX19ORVhUX0hBU19SRVdSSVRFUykge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByZXN1bHQgPSAoMCwgX3Jlc29sdmVyZXdyaXRlcy5kZWZhdWx0KShhcywgcGFnZXMsIHJld3JpdGVzLCBwYXJzZWRSZXdyaXRlVGFyZ2V0LnF1ZXJ5LCAocGF0aCk9PnJlc29sdmVEeW5hbWljUm91dGUocGF0aCwgcGFnZXMpLCBvcHRpb25zLnJvdXRlci5sb2NhbGVzKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHJlc3VsdC5tYXRjaGVkUGFnZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgcGFyc2VkUmV3cml0ZVRhcmdldC5wYXRobmFtZSA9IHJlc3VsdC5wYXJzZWRBcy5wYXRobmFtZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFzID0gcGFyc2VkUmV3cml0ZVRhcmdldC5wYXRobmFtZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIE9iamVjdC5hc3NpZ24ocGFyc2VkUmV3cml0ZVRhcmdldC5xdWVyeSwgcmVzdWx0LnBhcnNlZEFzLnF1ZXJ5KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoIXBhZ2VzLmluY2x1ZGVzKGZzUGF0aG5hbWUpKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlc29sdmVkUGF0aG5hbWUgPSByZXNvbHZlRHluYW1pY1JvdXRlKGZzUGF0aG5hbWUsIHBhZ2VzKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHJlc29sdmVkUGF0aG5hbWUgIT09IGZzUGF0aG5hbWUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGZzUGF0aG5hbWUgPSByZXNvbHZlZFBhdGhuYW1lO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IHJlc29sdmVkSHJlZiA9ICFwYWdlcy5pbmNsdWRlcyhmc1BhdGhuYW1lKSA/IHJlc29sdmVEeW5hbWljUm91dGUoKDAsIF9ub3JtYWxpemVsb2NhbGVwYXRoLm5vcm1hbGl6ZUxvY2FsZVBhdGgpKCgwLCBfcmVtb3ZlYmFzZXBhdGgucmVtb3ZlQmFzZVBhdGgpKHBhcnNlZFJld3JpdGVUYXJnZXQucGF0aG5hbWUpLCBvcHRpb25zLnJvdXRlci5sb2NhbGVzKS5wYXRobmFtZSwgcGFnZXMpIDogZnNQYXRobmFtZTtcbiAgICAgICAgICAgICAgICBpZiAoKDAsIF9pc2R5bmFtaWMuaXNEeW5hbWljUm91dGUpKHJlc29sdmVkSHJlZikpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWF0Y2hlcyA9ICgwLCBfcm91dGVtYXRjaGVyLmdldFJvdXRlTWF0Y2hlcikoKDAsIF9yb3V0ZXJlZ2V4LmdldFJvdXRlUmVnZXgpKHJlc29sdmVkSHJlZikpKGFzKTtcbiAgICAgICAgICAgICAgICAgICAgT2JqZWN0LmFzc2lnbihwYXJzZWRSZXdyaXRlVGFyZ2V0LnF1ZXJ5LCBtYXRjaGVzIHx8IHt9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3Jld3JpdGUnLFxuICAgICAgICAgICAgICAgICAgICBwYXJzZWRBczogcGFyc2VkUmV3cml0ZVRhcmdldCxcbiAgICAgICAgICAgICAgICAgICAgcmVzb2x2ZWRIcmVmXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHNyYyA9ICgwLCBfcGFyc2VwYXRoLnBhcnNlUGF0aCkoc291cmNlKTtcbiAgICAgICAgY29uc3QgcGF0aG5hbWUgPSAoMCwgX2Zvcm1hdG5leHRwYXRobmFtZWluZm8uZm9ybWF0TmV4dFBhdGhuYW1lSW5mbykoe1xuICAgICAgICAgICAgLi4uKDAsIF9nZXRuZXh0cGF0aG5hbWVpbmZvLmdldE5leHRQYXRobmFtZUluZm8pKHNyYy5wYXRobmFtZSwge1xuICAgICAgICAgICAgICAgIG5leHRDb25maWcsXG4gICAgICAgICAgICAgICAgcGFyc2VEYXRhOiB0cnVlXG4gICAgICAgICAgICB9KSxcbiAgICAgICAgICAgIGRlZmF1bHRMb2NhbGU6IG9wdGlvbnMucm91dGVyLmRlZmF1bHRMb2NhbGUsXG4gICAgICAgICAgICBidWlsZElkOiAnJ1xuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSh7XG4gICAgICAgICAgICB0eXBlOiAncmVkaXJlY3QtZXh0ZXJuYWwnLFxuICAgICAgICAgICAgZGVzdGluYXRpb246IFwiXCIgKyBwYXRobmFtZSArIHNyYy5xdWVyeSArIHNyYy5oYXNoXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBjb25zdCByZWRpcmVjdFRhcmdldCA9IHJlc3BvbnNlLmhlYWRlcnMuZ2V0KCd4LW5leHRqcy1yZWRpcmVjdCcpO1xuICAgIGlmIChyZWRpcmVjdFRhcmdldCkge1xuICAgICAgICBpZiAocmVkaXJlY3RUYXJnZXQuc3RhcnRzV2l0aCgnLycpKSB7XG4gICAgICAgICAgICBjb25zdCBzcmMgPSAoMCwgX3BhcnNlcGF0aC5wYXJzZVBhdGgpKHJlZGlyZWN0VGFyZ2V0KTtcbiAgICAgICAgICAgIGNvbnN0IHBhdGhuYW1lID0gKDAsIF9mb3JtYXRuZXh0cGF0aG5hbWVpbmZvLmZvcm1hdE5leHRQYXRobmFtZUluZm8pKHtcbiAgICAgICAgICAgICAgICAuLi4oMCwgX2dldG5leHRwYXRobmFtZWluZm8uZ2V0TmV4dFBhdGhuYW1lSW5mbykoc3JjLnBhdGhuYW1lLCB7XG4gICAgICAgICAgICAgICAgICAgIG5leHRDb25maWcsXG4gICAgICAgICAgICAgICAgICAgIHBhcnNlRGF0YTogdHJ1ZVxuICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgICAgIGRlZmF1bHRMb2NhbGU6IG9wdGlvbnMucm91dGVyLmRlZmF1bHRMb2NhbGUsXG4gICAgICAgICAgICAgICAgYnVpbGRJZDogJydcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSh7XG4gICAgICAgICAgICAgICAgdHlwZTogJ3JlZGlyZWN0LWludGVybmFsJyxcbiAgICAgICAgICAgICAgICBuZXdBczogXCJcIiArIHBhdGhuYW1lICsgc3JjLnF1ZXJ5ICsgc3JjLmhhc2gsXG4gICAgICAgICAgICAgICAgbmV3VXJsOiBcIlwiICsgcGF0aG5hbWUgKyBzcmMucXVlcnkgKyBzcmMuaGFzaFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSh7XG4gICAgICAgICAgICB0eXBlOiAncmVkaXJlY3QtZXh0ZXJuYWwnLFxuICAgICAgICAgICAgZGVzdGluYXRpb246IHJlZGlyZWN0VGFyZ2V0XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKHtcbiAgICAgICAgdHlwZTogJ25leHQnXG4gICAgfSk7XG59XG5hc3luYyBmdW5jdGlvbiB3aXRoTWlkZGxld2FyZUVmZmVjdHMob3B0aW9ucykge1xuICAgIGNvbnN0IG1hdGNoZXMgPSBhd2FpdCBtYXRjaGVzTWlkZGxld2FyZShvcHRpb25zKTtcbiAgICBpZiAoIW1hdGNoZXMgfHwgIW9wdGlvbnMuZmV0Y2hEYXRhKSB7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgICBjb25zdCBkYXRhID0gYXdhaXQgb3B0aW9ucy5mZXRjaERhdGEoKTtcbiAgICBjb25zdCBlZmZlY3QgPSBhd2FpdCBnZXRNaWRkbGV3YXJlRGF0YShkYXRhLmRhdGFIcmVmLCBkYXRhLnJlc3BvbnNlLCBvcHRpb25zKTtcbiAgICByZXR1cm4ge1xuICAgICAgICBkYXRhSHJlZjogZGF0YS5kYXRhSHJlZixcbiAgICAgICAganNvbjogZGF0YS5qc29uLFxuICAgICAgICByZXNwb25zZTogZGF0YS5yZXNwb25zZSxcbiAgICAgICAgdGV4dDogZGF0YS50ZXh0LFxuICAgICAgICBjYWNoZUtleTogZGF0YS5jYWNoZUtleSxcbiAgICAgICAgZWZmZWN0XG4gICAgfTtcbn1cbmNvbnN0IG1hbnVhbFNjcm9sbFJlc3RvcmF0aW9uID0gcHJvY2Vzcy5lbnYuX19ORVhUX1NDUk9MTF9SRVNUT1JBVElPTiAmJiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiAnc2Nyb2xsUmVzdG9yYXRpb24nIGluIHdpbmRvdy5oaXN0b3J5ICYmICEhZnVuY3Rpb24oKSB7XG4gICAgdHJ5IHtcbiAgICAgICAgbGV0IHYgPSAnX19uZXh0JztcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXNlcXVlbmNlc1xuICAgICAgICByZXR1cm4gc2Vzc2lvblN0b3JhZ2Uuc2V0SXRlbSh2LCB2KSwgc2Vzc2lvblN0b3JhZ2UucmVtb3ZlSXRlbSh2KSwgdHJ1ZTtcbiAgICB9IGNhdGNoIChuKSB7fVxufSgpO1xuY29uc3QgU1NHX0RBVEFfTk9UX0ZPVU5EID0gU3ltYm9sKCdTU0dfREFUQV9OT1RfRk9VTkQnKTtcbmZ1bmN0aW9uIGZldGNoUmV0cnkodXJsLCBhdHRlbXB0cywgb3B0aW9ucykge1xuICAgIHJldHVybiBmZXRjaCh1cmwsIHtcbiAgICAgICAgLy8gQ29va2llcyBhcmUgcmVxdWlyZWQgdG8gYmUgcHJlc2VudCBmb3IgTmV4dC5qcycgU1NHIFwiUHJldmlldyBNb2RlXCIuXG4gICAgICAgIC8vIENvb2tpZXMgbWF5IGFsc28gYmUgcmVxdWlyZWQgZm9yIGBnZXRTZXJ2ZXJTaWRlUHJvcHNgLlxuICAgICAgICAvL1xuICAgICAgICAvLyA+IGBmZXRjaGAgd29u4oCZdCBzZW5kIGNvb2tpZXMsIHVubGVzcyB5b3Ugc2V0IHRoZSBjcmVkZW50aWFscyBpbml0XG4gICAgICAgIC8vID4gb3B0aW9uLlxuICAgICAgICAvLyBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9kb2NzL1dlYi9BUEkvRmV0Y2hfQVBJL1VzaW5nX0ZldGNoXG4gICAgICAgIC8vXG4gICAgICAgIC8vID4gRm9yIG1heGltdW0gYnJvd3NlciBjb21wYXRpYmlsaXR5IHdoZW4gaXQgY29tZXMgdG8gc2VuZGluZyAmXG4gICAgICAgIC8vID4gcmVjZWl2aW5nIGNvb2tpZXMsIGFsd2F5cyBzdXBwbHkgdGhlIGBjcmVkZW50aWFsczogJ3NhbWUtb3JpZ2luJ2BcbiAgICAgICAgLy8gPiBvcHRpb24gaW5zdGVhZCBvZiByZWx5aW5nIG9uIHRoZSBkZWZhdWx0LlxuICAgICAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vZ2l0aHViL2ZldGNoI2NhdmVhdHNcbiAgICAgICAgY3JlZGVudGlhbHM6ICdzYW1lLW9yaWdpbicsXG4gICAgICAgIG1ldGhvZDogb3B0aW9ucy5tZXRob2QgfHwgJ0dFVCcsXG4gICAgICAgIGhlYWRlcnM6IE9iamVjdC5hc3NpZ24oe30sIG9wdGlvbnMuaGVhZGVycywge1xuICAgICAgICAgICAgJ3gtbmV4dGpzLWRhdGEnOiAnMSdcbiAgICAgICAgfSlcbiAgICB9KS50aGVuKChyZXNwb25zZSk9PntcbiAgICAgICAgcmV0dXJuICFyZXNwb25zZS5vayAmJiBhdHRlbXB0cyA+IDEgJiYgcmVzcG9uc2Uuc3RhdHVzID49IDUwMCA/IGZldGNoUmV0cnkodXJsLCBhdHRlbXB0cyAtIDEsIG9wdGlvbnMpIDogcmVzcG9uc2U7XG4gICAgfSk7XG59XG5mdW5jdGlvbiB0cnlUb1BhcnNlQXNKU09OKHRleHQpIHtcbiAgICB0cnkge1xuICAgICAgICByZXR1cm4gSlNPTi5wYXJzZSh0ZXh0KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG59XG5mdW5jdGlvbiBmZXRjaE5leHREYXRhKHBhcmFtKSB7XG4gICAgbGV0IHsgZGF0YUhyZWYsIGluZmxpZ2h0Q2FjaGUsIGlzUHJlZmV0Y2gsIGhhc01pZGRsZXdhcmUsIGlzU2VydmVyUmVuZGVyLCBwYXJzZUpTT04sIHBlcnNpc3RDYWNoZSwgaXNCYWNrZ3JvdW5kLCB1bnN0YWJsZV9za2lwQ2xpZW50Q2FjaGUgfSA9IHBhcmFtO1xuICAgIGNvbnN0IHsgaHJlZjogY2FjaGVLZXkgfSA9IG5ldyBVUkwoZGF0YUhyZWYsIHdpbmRvdy5sb2NhdGlvbi5ocmVmKTtcbiAgICBjb25zdCBnZXREYXRhID0gKHBhcmFtcyk9PntcbiAgICAgICAgdmFyIF9wYXJhbXNfbWV0aG9kO1xuICAgICAgICByZXR1cm4gZmV0Y2hSZXRyeShkYXRhSHJlZiwgaXNTZXJ2ZXJSZW5kZXIgPyAzIDogMSwge1xuICAgICAgICAgICAgaGVhZGVyczogT2JqZWN0LmFzc2lnbih7fSwgaXNQcmVmZXRjaCA/IHtcbiAgICAgICAgICAgICAgICBwdXJwb3NlOiAncHJlZmV0Y2gnXG4gICAgICAgICAgICB9IDoge30sIGlzUHJlZmV0Y2ggJiYgaGFzTWlkZGxld2FyZSA/IHtcbiAgICAgICAgICAgICAgICAneC1taWRkbGV3YXJlLXByZWZldGNoJzogJzEnXG4gICAgICAgICAgICB9IDoge30sIHByb2Nlc3MuZW52Lk5FWFRfREVQTE9ZTUVOVF9JRCA/IHtcbiAgICAgICAgICAgICAgICAneC1kZXBsb3ltZW50LWlkJzogcHJvY2Vzcy5lbnYuTkVYVF9ERVBMT1lNRU5UX0lEXG4gICAgICAgICAgICB9IDoge30pLFxuICAgICAgICAgICAgbWV0aG9kOiAoX3BhcmFtc19tZXRob2QgPSBwYXJhbXMgPT0gbnVsbCA/IHZvaWQgMCA6IHBhcmFtcy5tZXRob2QpICE9IG51bGwgPyBfcGFyYW1zX21ldGhvZCA6ICdHRVQnXG4gICAgICAgIH0pLnRoZW4oKHJlc3BvbnNlKT0+e1xuICAgICAgICAgICAgaWYgKHJlc3BvbnNlLm9rICYmIChwYXJhbXMgPT0gbnVsbCA/IHZvaWQgMCA6IHBhcmFtcy5tZXRob2QpID09PSAnSEVBRCcpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICBkYXRhSHJlZixcbiAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2UsXG4gICAgICAgICAgICAgICAgICAgIHRleHQ6ICcnLFxuICAgICAgICAgICAgICAgICAgICBqc29uOiB7fSxcbiAgICAgICAgICAgICAgICAgICAgY2FjaGVLZXlcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHJlc3BvbnNlLnRleHQoKS50aGVuKCh0ZXh0KT0+e1xuICAgICAgICAgICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBXaGVuIHRoZSBkYXRhIHJlc3BvbnNlIGlzIGEgcmVkaXJlY3QgYmVjYXVzZSBvZiBhIG1pZGRsZXdhcmVcbiAgICAgICAgICAgICAqIHdlIGRvIG5vdCBjb25zaWRlciBpdCBhbiBlcnJvci4gVGhlIGhlYWRlcnMgbXVzdCBicmluZyB0aGVcbiAgICAgICAgICAgICAqIG1hcHBlZCBsb2NhdGlvbi5cbiAgICAgICAgICAgICAqIFRPRE86IENoYW5nZSB0aGUgc3RhdHVzIGNvZGUgaW4gdGhlIGhhbmRsZXIuXG4gICAgICAgICAgICAgKi8gaWYgKGhhc01pZGRsZXdhcmUgJiYgW1xuICAgICAgICAgICAgICAgICAgICAgICAgMzAxLFxuICAgICAgICAgICAgICAgICAgICAgICAgMzAyLFxuICAgICAgICAgICAgICAgICAgICAgICAgMzA3LFxuICAgICAgICAgICAgICAgICAgICAgICAgMzA4XG4gICAgICAgICAgICAgICAgICAgIF0uaW5jbHVkZXMocmVzcG9uc2Uuc3RhdHVzKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhSHJlZixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXNwb25zZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGpzb246IHt9LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhY2hlS2V5XG4gICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwNCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFyIF90cnlUb1BhcnNlQXNKU09OO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKChfdHJ5VG9QYXJzZUFzSlNPTiA9IHRyeVRvUGFyc2VBc0pTT04odGV4dCkpID09IG51bGwgPyB2b2lkIDAgOiBfdHJ5VG9QYXJzZUFzSlNPTi5ub3RGb3VuZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGFIcmVmLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBqc29uOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBub3RGb3VuZDogU1NHX0RBVEFfTk9UX0ZPVU5EXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYWNoZUtleVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZXJyb3IgPSBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKFwiRmFpbGVkIHRvIGxvYWQgc3RhdGljIHByb3BzXCIpLCBcIl9fTkVYVF9FUlJPUl9DT0RFXCIsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIkUxMjRcIixcbiAgICAgICAgICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIFdlIHNob3VsZCBvbmx5IHRyaWdnZXIgYSBzZXJ2ZXItc2lkZSB0cmFuc2l0aW9uIGlmIHRoaXMgd2FzXG4gICAgICAgICAgICAgKiBjYXVzZWQgb24gYSBjbGllbnQtc2lkZSB0cmFuc2l0aW9uLiBPdGhlcndpc2UsIHdlJ2QgZ2V0IGludG9cbiAgICAgICAgICAgICAqIGFuIGluZmluaXRlIGxvb3AuXG4gICAgICAgICAgICAgKi8gaWYgKCFpc1NlcnZlclJlbmRlcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgKDAsIF9yb3V0ZWxvYWRlci5tYXJrQXNzZXRFcnJvcikoZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICBkYXRhSHJlZixcbiAgICAgICAgICAgICAgICAgICAganNvbjogcGFyc2VKU09OID8gdHJ5VG9QYXJzZUFzSlNPTih0ZXh0KSA6IG51bGwsXG4gICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlLFxuICAgICAgICAgICAgICAgICAgICB0ZXh0LFxuICAgICAgICAgICAgICAgICAgICBjYWNoZUtleVxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSkudGhlbigoZGF0YSk9PntcbiAgICAgICAgICAgIGlmICghcGVyc2lzdENhY2hlIHx8IHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicgfHwgZGF0YS5yZXNwb25zZS5oZWFkZXJzLmdldCgneC1taWRkbGV3YXJlLWNhY2hlJykgPT09ICduby1jYWNoZScpIHtcbiAgICAgICAgICAgICAgICBkZWxldGUgaW5mbGlnaHRDYWNoZVtjYWNoZUtleV07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gZGF0YTtcbiAgICAgICAgfSkuY2F0Y2goKGVycik9PntcbiAgICAgICAgICAgIGlmICghdW5zdGFibGVfc2tpcENsaWVudENhY2hlKSB7XG4gICAgICAgICAgICAgICAgZGVsZXRlIGluZmxpZ2h0Q2FjaGVbY2FjaGVLZXldO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKC8vIGNocm9tZVxuICAgICAgICAgICAgZXJyLm1lc3NhZ2UgPT09ICdGYWlsZWQgdG8gZmV0Y2gnIHx8IC8vIGZpcmVmb3hcbiAgICAgICAgICAgIGVyci5tZXNzYWdlID09PSAnTmV0d29ya0Vycm9yIHdoZW4gYXR0ZW1wdGluZyB0byBmZXRjaCByZXNvdXJjZS4nIHx8IC8vIHNhZmFyaVxuICAgICAgICAgICAgZXJyLm1lc3NhZ2UgPT09ICdMb2FkIGZhaWxlZCcpIHtcbiAgICAgICAgICAgICAgICAoMCwgX3JvdXRlbG9hZGVyLm1hcmtBc3NldEVycm9yKShlcnIpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhyb3cgZXJyO1xuICAgICAgICB9KTtcbiAgICB9O1xuICAgIC8vIHdoZW4gc2tpcHBpbmcgY2xpZW50IGNhY2hlIHdlIHdhaXQgdG8gdXBkYXRlXG4gICAgLy8gaW5mbGlnaHQgY2FjaGUgdW50aWwgc3VjY2Vzc2Z1bCBkYXRhIHJlc3BvbnNlXG4gICAgLy8gdGhpcyBhbGxvd3MgcmFjaW5nIGNsaWNrIGV2ZW50IHdpdGggZmV0Y2hpbmcgbmV3ZXIgZGF0YVxuICAgIC8vIHdpdGhvdXQgYmxvY2tpbmcgbmF2aWdhdGlvbiB3aGVuIHN0YWxlIGRhdGEgaXMgYXZhaWxhYmxlXG4gICAgaWYgKHVuc3RhYmxlX3NraXBDbGllbnRDYWNoZSAmJiBwZXJzaXN0Q2FjaGUpIHtcbiAgICAgICAgcmV0dXJuIGdldERhdGEoe30pLnRoZW4oKGRhdGEpPT57XG4gICAgICAgICAgICBpZiAoZGF0YS5yZXNwb25zZS5oZWFkZXJzLmdldCgneC1taWRkbGV3YXJlLWNhY2hlJykgIT09ICduby1jYWNoZScpIHtcbiAgICAgICAgICAgICAgICAvLyBvbmx5IHVwZGF0ZSBjYWNoZSBpZiBub3QgbWFya2VkIGFzIG5vLWNhY2hlXG4gICAgICAgICAgICAgICAgaW5mbGlnaHRDYWNoZVtjYWNoZUtleV0gPSBQcm9taXNlLnJlc29sdmUoZGF0YSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gZGF0YTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGlmIChpbmZsaWdodENhY2hlW2NhY2hlS2V5XSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHJldHVybiBpbmZsaWdodENhY2hlW2NhY2hlS2V5XTtcbiAgICB9XG4gICAgcmV0dXJuIGluZmxpZ2h0Q2FjaGVbY2FjaGVLZXldID0gZ2V0RGF0YShpc0JhY2tncm91bmQgPyB7XG4gICAgICAgIG1ldGhvZDogJ0hFQUQnXG4gICAgfSA6IHt9KTtcbn1cbmZ1bmN0aW9uIGNyZWF0ZUtleSgpIHtcbiAgICByZXR1cm4gTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc2xpY2UoMiwgMTApO1xufVxuZnVuY3Rpb24gaGFuZGxlSGFyZE5hdmlnYXRpb24ocGFyYW0pIHtcbiAgICBsZXQgeyB1cmwsIHJvdXRlciB9ID0gcGFyYW07XG4gICAgLy8gZW5zdXJlIHdlIGRvbid0IHRyaWdnZXIgYSBoYXJkIG5hdmlnYXRpb24gdG8gdGhlIHNhbWVcbiAgICAvLyBVUkwgYXMgdGhpcyBjYW4gZW5kIHVwIHdpdGggYW4gaW5maW5pdGUgcmVmcmVzaFxuICAgIGlmICh1cmwgPT09ICgwLCBfYWRkYmFzZXBhdGguYWRkQmFzZVBhdGgpKCgwLCBfYWRkbG9jYWxlLmFkZExvY2FsZSkocm91dGVyLmFzUGF0aCwgcm91dGVyLmxvY2FsZSkpKSB7XG4gICAgICAgIHRocm93IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuZXcgRXJyb3IoXCJJbnZhcmlhbnQ6IGF0dGVtcHRlZCB0byBoYXJkIG5hdmlnYXRlIHRvIHRoZSBzYW1lIFVSTCBcIiArIHVybCArIFwiIFwiICsgbG9jYXRpb24uaHJlZiksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgdmFsdWU6IFwiRTI4MlwiLFxuICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gdXJsO1xufVxuY29uc3QgZ2V0Q2FuY2VsbGVkSGFuZGxlciA9IChwYXJhbSk9PntcbiAgICBsZXQgeyByb3V0ZSwgcm91dGVyIH0gPSBwYXJhbTtcbiAgICBsZXQgY2FuY2VsbGVkID0gZmFsc2U7XG4gICAgY29uc3QgY2FuY2VsID0gcm91dGVyLmNsYyA9ICgpPT57XG4gICAgICAgIGNhbmNlbGxlZCA9IHRydWU7XG4gICAgfTtcbiAgICBjb25zdCBoYW5kbGVDYW5jZWxsZWQgPSAoKT0+e1xuICAgICAgICBpZiAoY2FuY2VsbGVkKSB7XG4gICAgICAgICAgICBjb25zdCBlcnJvciA9IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuZXcgRXJyb3IoJ0Fib3J0IGZldGNoaW5nIGNvbXBvbmVudCBmb3Igcm91dGU6IFwiJyArIHJvdXRlICsgJ1wiJyksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgIHZhbHVlOiBcIkU0ODNcIixcbiAgICAgICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgZXJyb3IuY2FuY2VsbGVkID0gdHJ1ZTtcbiAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG4gICAgICAgIGlmIChjYW5jZWwgPT09IHJvdXRlci5jbGMpIHtcbiAgICAgICAgICAgIHJvdXRlci5jbGMgPSBudWxsO1xuICAgICAgICB9XG4gICAgfTtcbiAgICByZXR1cm4gaGFuZGxlQ2FuY2VsbGVkO1xufTtcbmNsYXNzIFJvdXRlciB7XG4gICAgcmVsb2FkKCkge1xuICAgICAgICB3aW5kb3cubG9jYXRpb24ucmVsb2FkKCk7XG4gICAgfVxuICAgIC8qKlxuICAgKiBHbyBiYWNrIGluIGhpc3RvcnlcbiAgICovIGJhY2soKSB7XG4gICAgICAgIHdpbmRvdy5oaXN0b3J5LmJhY2soKTtcbiAgICB9XG4gICAgLyoqXG4gICAqIEdvIGZvcndhcmQgaW4gaGlzdG9yeVxuICAgKi8gZm9yd2FyZCgpIHtcbiAgICAgICAgd2luZG93Lmhpc3RvcnkuZm9yd2FyZCgpO1xuICAgIH1cbiAgICAvKipcbiAgICogUGVyZm9ybXMgYSBgcHVzaFN0YXRlYCB3aXRoIGFyZ3VtZW50c1xuICAgKiBAcGFyYW0gdXJsIG9mIHRoZSByb3V0ZVxuICAgKiBAcGFyYW0gYXMgbWFza3MgYHVybGAgZm9yIHRoZSBicm93c2VyXG4gICAqIEBwYXJhbSBvcHRpb25zIG9iamVjdCB5b3UgY2FuIGRlZmluZSBgc2hhbGxvd2AgYW5kIG90aGVyIG9wdGlvbnNcbiAgICovIHB1c2godXJsLCBhcywgb3B0aW9ucykge1xuICAgICAgICBpZiAob3B0aW9ucyA9PT0gdm9pZCAwKSBvcHRpb25zID0ge307XG4gICAgICAgIGlmIChwcm9jZXNzLmVudi5fX05FWFRfU0NST0xMX1JFU1RPUkFUSU9OKSB7XG4gICAgICAgICAgICAvLyBUT0RPOiByZW1vdmUgaW4gdGhlIGZ1dHVyZSB3aGVuIHdlIHVwZGF0ZSBoaXN0b3J5IGJlZm9yZSByb3V0ZSBjaGFuZ2VcbiAgICAgICAgICAgIC8vIGlzIGNvbXBsZXRlLCBhcyB0aGUgcG9wc3RhdGUgZXZlbnQgc2hvdWxkIGhhbmRsZSB0aGlzIGNhcHR1cmUuXG4gICAgICAgICAgICBpZiAobWFudWFsU2Nyb2xsUmVzdG9yYXRpb24pIHtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAvLyBTbmFwc2hvdCBzY3JvbGwgcG9zaXRpb24gcmlnaHQgYmVmb3JlIG5hdmlnYXRpbmcgdG8gYSBuZXcgcGFnZTpcbiAgICAgICAgICAgICAgICAgICAgc2Vzc2lvblN0b3JhZ2Uuc2V0SXRlbSgnX19uZXh0X3Njcm9sbF8nICsgdGhpcy5fa2V5LCBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgICAgICAgICAgICAgICB4OiBzZWxmLnBhZ2VYT2Zmc2V0LFxuICAgICAgICAgICAgICAgICAgICAgICAgeTogc2VsZi5wYWdlWU9mZnNldFxuICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAoZSkge31cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICA7XG4gICAgICAgICh7IHVybCwgYXMgfSA9IHByZXBhcmVVcmxBcyh0aGlzLCB1cmwsIGFzKSk7XG4gICAgICAgIHJldHVybiB0aGlzLmNoYW5nZSgncHVzaFN0YXRlJywgdXJsLCBhcywgb3B0aW9ucyk7XG4gICAgfVxuICAgIC8qKlxuICAgKiBQZXJmb3JtcyBhIGByZXBsYWNlU3RhdGVgIHdpdGggYXJndW1lbnRzXG4gICAqIEBwYXJhbSB1cmwgb2YgdGhlIHJvdXRlXG4gICAqIEBwYXJhbSBhcyBtYXNrcyBgdXJsYCBmb3IgdGhlIGJyb3dzZXJcbiAgICogQHBhcmFtIG9wdGlvbnMgb2JqZWN0IHlvdSBjYW4gZGVmaW5lIGBzaGFsbG93YCBhbmQgb3RoZXIgb3B0aW9uc1xuICAgKi8gcmVwbGFjZSh1cmwsIGFzLCBvcHRpb25zKSB7XG4gICAgICAgIGlmIChvcHRpb25zID09PSB2b2lkIDApIG9wdGlvbnMgPSB7fTtcbiAgICAgICAgO1xuICAgICAgICAoeyB1cmwsIGFzIH0gPSBwcmVwYXJlVXJsQXModGhpcywgdXJsLCBhcykpO1xuICAgICAgICByZXR1cm4gdGhpcy5jaGFuZ2UoJ3JlcGxhY2VTdGF0ZScsIHVybCwgYXMsIG9wdGlvbnMpO1xuICAgIH1cbiAgICBhc3luYyBfYmZsKGFzLCByZXNvbHZlZEFzLCBsb2NhbGUsIHNraXBOYXZpZ2F0ZSkge1xuICAgICAgICBpZiAocHJvY2Vzcy5lbnYuX19ORVhUX0NMSUVOVF9ST1VURVJfRklMVEVSX0VOQUJMRUQpIHtcbiAgICAgICAgICAgIGlmICghdGhpcy5fYmZsX3MgJiYgIXRoaXMuX2JmbF9kKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgeyBCbG9vbUZpbHRlciB9ID0gcmVxdWlyZSgnLi4vLi4vbGliL2Jsb29tLWZpbHRlcicpO1xuICAgICAgICAgICAgICAgIGxldCBzdGF0aWNGaWx0ZXJEYXRhO1xuICAgICAgICAgICAgICAgIGxldCBkeW5hbWljRmlsdGVyRGF0YTtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICA7XG4gICAgICAgICAgICAgICAgICAgICh7IF9fcm91dGVyRmlsdGVyU3RhdGljOiBzdGF0aWNGaWx0ZXJEYXRhLCBfX3JvdXRlckZpbHRlckR5bmFtaWM6IGR5bmFtaWNGaWx0ZXJEYXRhIH0gPSBhd2FpdCAoMCwgX3JvdXRlbG9hZGVyLmdldENsaWVudEJ1aWxkTWFuaWZlc3QpKCkpO1xuICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICAgICAgICAgICAgICAvLyBmYWlsZWQgdG8gbG9hZCBidWlsZCBtYW5pZmVzdCBoYXJkIG5hdmlnYXRlXG4gICAgICAgICAgICAgICAgICAgIC8vIHRvIGJlIHNhZmVcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihlcnIpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoc2tpcE5hdmlnYXRlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBoYW5kbGVIYXJkTmF2aWdhdGlvbih7XG4gICAgICAgICAgICAgICAgICAgICAgICB1cmw6ICgwLCBfYWRkYmFzZXBhdGguYWRkQmFzZVBhdGgpKCgwLCBfYWRkbG9jYWxlLmFkZExvY2FsZSkoYXMsIGxvY2FsZSB8fCB0aGlzLmxvY2FsZSwgdGhpcy5kZWZhdWx0TG9jYWxlKSksXG4gICAgICAgICAgICAgICAgICAgICAgICByb3V0ZXI6IHRoaXNcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgoKT0+e30pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCByb3V0ZXJGaWx0ZXJTVmFsdWUgPSBwcm9jZXNzLmVudi5fX05FWFRfQ0xJRU5UX1JPVVRFUl9TX0ZJTFRFUjtcbiAgICAgICAgICAgICAgICBpZiAoIXN0YXRpY0ZpbHRlckRhdGEgJiYgcm91dGVyRmlsdGVyU1ZhbHVlKSB7XG4gICAgICAgICAgICAgICAgICAgIHN0YXRpY0ZpbHRlckRhdGEgPSByb3V0ZXJGaWx0ZXJTVmFsdWUgPyByb3V0ZXJGaWx0ZXJTVmFsdWUgOiB1bmRlZmluZWQ7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IHJvdXRlckZpbHRlckRWYWx1ZSA9IHByb2Nlc3MuZW52Ll9fTkVYVF9DTElFTlRfUk9VVEVSX0RfRklMVEVSO1xuICAgICAgICAgICAgICAgIGlmICghZHluYW1pY0ZpbHRlckRhdGEgJiYgcm91dGVyRmlsdGVyRFZhbHVlKSB7XG4gICAgICAgICAgICAgICAgICAgIGR5bmFtaWNGaWx0ZXJEYXRhID0gcm91dGVyRmlsdGVyRFZhbHVlID8gcm91dGVyRmlsdGVyRFZhbHVlIDogdW5kZWZpbmVkO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoc3RhdGljRmlsdGVyRGF0YSA9PSBudWxsID8gdm9pZCAwIDogc3RhdGljRmlsdGVyRGF0YS5udW1IYXNoZXMpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5fYmZsX3MgPSBuZXcgQmxvb21GaWx0ZXIoc3RhdGljRmlsdGVyRGF0YS5udW1JdGVtcywgc3RhdGljRmlsdGVyRGF0YS5lcnJvclJhdGUpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLl9iZmxfcy5pbXBvcnQoc3RhdGljRmlsdGVyRGF0YSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChkeW5hbWljRmlsdGVyRGF0YSA9PSBudWxsID8gdm9pZCAwIDogZHluYW1pY0ZpbHRlckRhdGEubnVtSGFzaGVzKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX2JmbF9kID0gbmV3IEJsb29tRmlsdGVyKGR5bmFtaWNGaWx0ZXJEYXRhLm51bUl0ZW1zLCBkeW5hbWljRmlsdGVyRGF0YS5lcnJvclJhdGUpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLl9iZmxfZC5pbXBvcnQoZHluYW1pY0ZpbHRlckRhdGEpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGxldCBtYXRjaGVzQmZsU3RhdGljID0gZmFsc2U7XG4gICAgICAgICAgICBsZXQgbWF0Y2hlc0JmbER5bmFtaWMgPSBmYWxzZTtcbiAgICAgICAgICAgIGNvbnN0IHBhdGhzVG9DaGVjayA9IFtcbiAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIGFzXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIGFzOiByZXNvbHZlZEFzXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgXTtcbiAgICAgICAgICAgIGZvciAoY29uc3QgeyBhczogY3VyQXMsIGFsbG93TWF0Y2hDdXJyZW50IH0gb2YgcGF0aHNUb0NoZWNrKXtcbiAgICAgICAgICAgICAgICBpZiAoY3VyQXMpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgYXNOb1NsYXNoID0gKDAsIF9yZW1vdmV0cmFpbGluZ3NsYXNoLnJlbW92ZVRyYWlsaW5nU2xhc2gpKG5ldyBVUkwoY3VyQXMsICdodHRwOi8vbicpLnBhdGhuYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgYXNOb1NsYXNoTG9jYWxlID0gKDAsIF9hZGRiYXNlcGF0aC5hZGRCYXNlUGF0aCkoKDAsIF9hZGRsb2NhbGUuYWRkTG9jYWxlKShhc05vU2xhc2gsIGxvY2FsZSB8fCB0aGlzLmxvY2FsZSkpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoYWxsb3dNYXRjaEN1cnJlbnQgfHwgYXNOb1NsYXNoICE9PSAoMCwgX3JlbW92ZXRyYWlsaW5nc2xhc2gucmVtb3ZlVHJhaWxpbmdTbGFzaCkobmV3IFVSTCh0aGlzLmFzUGF0aCwgJ2h0dHA6Ly9uJykucGF0aG5hbWUpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2YXIgX3RoaXNfX2JmbF9zLCBfdGhpc19fYmZsX3MxO1xuICAgICAgICAgICAgICAgICAgICAgICAgbWF0Y2hlc0JmbFN0YXRpYyA9IG1hdGNoZXNCZmxTdGF0aWMgfHwgISEoKF90aGlzX19iZmxfcyA9IHRoaXMuX2JmbF9zKSA9PSBudWxsID8gdm9pZCAwIDogX3RoaXNfX2JmbF9zLmNvbnRhaW5zKGFzTm9TbGFzaCkpIHx8ICEhKChfdGhpc19fYmZsX3MxID0gdGhpcy5fYmZsX3MpID09IG51bGwgPyB2b2lkIDAgOiBfdGhpc19fYmZsX3MxLmNvbnRhaW5zKGFzTm9TbGFzaExvY2FsZSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBub3JtYWxpemVkQVMgb2YgW1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFzTm9TbGFzaCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhc05vU2xhc2hMb2NhbGVcbiAgICAgICAgICAgICAgICAgICAgICAgIF0pe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIGlmIGFueSBzdWItcGF0aCBvZiBhcyBtYXRjaGVzIGEgZHluYW1pYyBmaWx0ZXIgcGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIGl0IHNob3VsZCBiZSBoYXJkIG5hdmlnYXRlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGN1ckFzUGFydHMgPSBub3JtYWxpemVkQVMuc3BsaXQoJy8nKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3IobGV0IGkgPSAwOyAhbWF0Y2hlc0JmbER5bmFtaWMgJiYgaSA8IGN1ckFzUGFydHMubGVuZ3RoICsgMTsgaSsrKXtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyIF90aGlzX19iZmxfZDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgY3VycmVudFBhcnQgPSBjdXJBc1BhcnRzLnNsaWNlKDAsIGkpLmpvaW4oJy8nKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGN1cnJlbnRQYXJ0ICYmICgoX3RoaXNfX2JmbF9kID0gdGhpcy5fYmZsX2QpID09IG51bGwgPyB2b2lkIDAgOiBfdGhpc19fYmZsX2QuY29udGFpbnMoY3VycmVudFBhcnQpKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWF0Y2hlc0JmbER5bmFtaWMgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBpZiB0aGUgY2xpZW50IHJvdXRlciBmaWx0ZXIgaXMgbWF0Y2hlZCB0aGVuIHdlIHRyaWdnZXJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIGEgaGFyZCBuYXZpZ2F0aW9uXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAobWF0Y2hlc0JmbFN0YXRpYyB8fCBtYXRjaGVzQmZsRHluYW1pYykge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChza2lwTmF2aWdhdGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUhhcmROYXZpZ2F0aW9uKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXJsOiAoMCwgX2FkZGJhc2VwYXRoLmFkZEJhc2VQYXRoKSgoMCwgX2FkZGxvY2FsZS5hZGRMb2NhbGUpKGFzLCBsb2NhbGUgfHwgdGhpcy5sb2NhbGUsIHRoaXMuZGVmYXVsdExvY2FsZSkpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3V0ZXI6IHRoaXNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKCk9Pnt9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGFzeW5jIGNoYW5nZShtZXRob2QsIHVybCwgYXMsIG9wdGlvbnMsIGZvcmNlZFNjcm9sbCkge1xuICAgICAgICB2YXIgX3RoaXNfY29tcG9uZW50c19wYXRobmFtZTtcbiAgICAgICAgaWYgKCEoMCwgX2lzbG9jYWx1cmwuaXNMb2NhbFVSTCkodXJsKSkge1xuICAgICAgICAgICAgaGFuZGxlSGFyZE5hdmlnYXRpb24oe1xuICAgICAgICAgICAgICAgIHVybCxcbiAgICAgICAgICAgICAgICByb3V0ZXI6IHRoaXNcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIC8vIFdBUk5JTkc6IGBfaGAgaXMgYW4gaW50ZXJuYWwgb3B0aW9uIGZvciBoYW5kaW5nIE5leHQuanMgY2xpZW50LXNpZGVcbiAgICAgICAgLy8gaHlkcmF0aW9uLiBZb3VyIGFwcCBzaG91bGQgX25ldmVyXyB1c2UgdGhpcyBwcm9wZXJ0eS4gSXQgbWF5IGNoYW5nZSBhdFxuICAgICAgICAvLyBhbnkgdGltZSB3aXRob3V0IG5vdGljZS5cbiAgICAgICAgY29uc3QgaXNRdWVyeVVwZGF0aW5nID0gb3B0aW9ucy5faCA9PT0gMTtcbiAgICAgICAgaWYgKCFpc1F1ZXJ5VXBkYXRpbmcgJiYgIW9wdGlvbnMuc2hhbGxvdykge1xuICAgICAgICAgICAgYXdhaXQgdGhpcy5fYmZsKGFzLCB1bmRlZmluZWQsIG9wdGlvbnMubG9jYWxlKTtcbiAgICAgICAgfVxuICAgICAgICBsZXQgc2hvdWxkUmVzb2x2ZUhyZWYgPSBpc1F1ZXJ5VXBkYXRpbmcgfHwgb3B0aW9ucy5fc2hvdWxkUmVzb2x2ZUhyZWYgfHwgKDAsIF9wYXJzZXBhdGgucGFyc2VQYXRoKSh1cmwpLnBhdGhuYW1lID09PSAoMCwgX3BhcnNlcGF0aC5wYXJzZVBhdGgpKGFzKS5wYXRobmFtZTtcbiAgICAgICAgY29uc3QgbmV4dFN0YXRlID0ge1xuICAgICAgICAgICAgLi4udGhpcy5zdGF0ZVxuICAgICAgICB9O1xuICAgICAgICAvLyBmb3Igc3RhdGljIHBhZ2VzIHdpdGggcXVlcnkgcGFyYW1zIGluIHRoZSBVUkwgd2UgZGVsYXlcbiAgICAgICAgLy8gbWFya2luZyB0aGUgcm91dGVyIHJlYWR5IHVudGlsIGFmdGVyIHRoZSBxdWVyeSBpcyB1cGRhdGVkXG4gICAgICAgIC8vIG9yIGEgbmF2aWdhdGlvbiBoYXMgb2NjdXJyZWRcbiAgICAgICAgY29uc3QgcmVhZHlTdGF0ZUNoYW5nZSA9IHRoaXMuaXNSZWFkeSAhPT0gdHJ1ZTtcbiAgICAgICAgdGhpcy5pc1JlYWR5ID0gdHJ1ZTtcbiAgICAgICAgY29uc3QgaXNTc3IgPSB0aGlzLmlzU3NyO1xuICAgICAgICBpZiAoIWlzUXVlcnlVcGRhdGluZykge1xuICAgICAgICAgICAgdGhpcy5pc1NzciA9IGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIC8vIGlmIGEgcm91dGUgdHJhbnNpdGlvbiBpcyBhbHJlYWR5IGluIHByb2dyZXNzIGJlZm9yZVxuICAgICAgICAvLyB0aGUgcXVlcnkgdXBkYXRpbmcgaXMgdHJpZ2dlcmVkIGlnbm9yZSBxdWVyeSB1cGRhdGluZ1xuICAgICAgICBpZiAoaXNRdWVyeVVwZGF0aW5nICYmIHRoaXMuY2xjKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgcHJldkxvY2FsZSA9IG5leHRTdGF0ZS5sb2NhbGU7XG4gICAgICAgIGlmIChwcm9jZXNzLmVudi5fX05FWFRfSTE4Tl9TVVBQT1JUKSB7XG4gICAgICAgICAgICBuZXh0U3RhdGUubG9jYWxlID0gb3B0aW9ucy5sb2NhbGUgPT09IGZhbHNlID8gdGhpcy5kZWZhdWx0TG9jYWxlIDogb3B0aW9ucy5sb2NhbGUgfHwgbmV4dFN0YXRlLmxvY2FsZTtcbiAgICAgICAgICAgIGlmICh0eXBlb2Ygb3B0aW9ucy5sb2NhbGUgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICAgICAgb3B0aW9ucy5sb2NhbGUgPSBuZXh0U3RhdGUubG9jYWxlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgcGFyc2VkQXMgPSAoMCwgX3BhcnNlcmVsYXRpdmV1cmwucGFyc2VSZWxhdGl2ZVVybCkoKDAsIF9oYXNiYXNlcGF0aC5oYXNCYXNlUGF0aCkoYXMpID8gKDAsIF9yZW1vdmViYXNlcGF0aC5yZW1vdmVCYXNlUGF0aCkoYXMpIDogYXMpO1xuICAgICAgICAgICAgY29uc3QgbG9jYWxlUGF0aFJlc3VsdCA9ICgwLCBfbm9ybWFsaXplbG9jYWxlcGF0aC5ub3JtYWxpemVMb2NhbGVQYXRoKShwYXJzZWRBcy5wYXRobmFtZSwgdGhpcy5sb2NhbGVzKTtcbiAgICAgICAgICAgIGlmIChsb2NhbGVQYXRoUmVzdWx0LmRldGVjdGVkTG9jYWxlKSB7XG4gICAgICAgICAgICAgICAgbmV4dFN0YXRlLmxvY2FsZSA9IGxvY2FsZVBhdGhSZXN1bHQuZGV0ZWN0ZWRMb2NhbGU7XG4gICAgICAgICAgICAgICAgcGFyc2VkQXMucGF0aG5hbWUgPSAoMCwgX2FkZGJhc2VwYXRoLmFkZEJhc2VQYXRoKShwYXJzZWRBcy5wYXRobmFtZSk7XG4gICAgICAgICAgICAgICAgYXMgPSAoMCwgX2Zvcm1hdHVybC5mb3JtYXRXaXRoVmFsaWRhdGlvbikocGFyc2VkQXMpO1xuICAgICAgICAgICAgICAgIHVybCA9ICgwLCBfYWRkYmFzZXBhdGguYWRkQmFzZVBhdGgpKCgwLCBfbm9ybWFsaXplbG9jYWxlcGF0aC5ub3JtYWxpemVMb2NhbGVQYXRoKSgoMCwgX2hhc2Jhc2VwYXRoLmhhc0Jhc2VQYXRoKSh1cmwpID8gKDAsIF9yZW1vdmViYXNlcGF0aC5yZW1vdmVCYXNlUGF0aCkodXJsKSA6IHVybCwgdGhpcy5sb2NhbGVzKS5wYXRobmFtZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBsZXQgZGlkTmF2aWdhdGUgPSBmYWxzZTtcbiAgICAgICAgICAgIC8vIHdlIG5lZWQgdG8gd3JhcCB0aGlzIGluIHRoZSBlbnYgY2hlY2sgYWdhaW4gc2luY2UgcmVnZW5lcmF0b3IgcnVudGltZVxuICAgICAgICAgICAgLy8gbW92ZXMgdGhpcyBvbiBpdHMgb3duIGR1ZSB0byB0aGUgcmV0dXJuXG4gICAgICAgICAgICBpZiAocHJvY2Vzcy5lbnYuX19ORVhUX0kxOE5fU1VQUE9SVCkge1xuICAgICAgICAgICAgICAgIHZhciBfdGhpc19sb2NhbGVzO1xuICAgICAgICAgICAgICAgIC8vIGlmIHRoZSBsb2NhbGUgaXNuJ3QgY29uZmlndXJlZCBoYXJkIG5hdmlnYXRlIHRvIHNob3cgNDA0IHBhZ2VcbiAgICAgICAgICAgICAgICBpZiAoISgoX3RoaXNfbG9jYWxlcyA9IHRoaXMubG9jYWxlcykgPT0gbnVsbCA/IHZvaWQgMCA6IF90aGlzX2xvY2FsZXMuaW5jbHVkZXMobmV4dFN0YXRlLmxvY2FsZSkpKSB7XG4gICAgICAgICAgICAgICAgICAgIHBhcnNlZEFzLnBhdGhuYW1lID0gKDAsIF9hZGRsb2NhbGUuYWRkTG9jYWxlKShwYXJzZWRBcy5wYXRobmFtZSwgbmV4dFN0YXRlLmxvY2FsZSk7XG4gICAgICAgICAgICAgICAgICAgIGhhbmRsZUhhcmROYXZpZ2F0aW9uKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHVybDogKDAsIF9mb3JtYXR1cmwuZm9ybWF0V2l0aFZhbGlkYXRpb24pKHBhcnNlZEFzKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJvdXRlcjogdGhpc1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgLy8gdGhpcyB3YXMgcHJldmlvdXNseSBhIHJldHVybiBidXQgd2FzIHJlbW92ZWQgaW4gZmF2b3JcbiAgICAgICAgICAgICAgICAgICAgLy8gb2YgYmV0dGVyIGRlYWQgY29kZSBlbGltaW5hdGlvbiB3aXRoIHJlZ2VuZXJhdG9yIHJ1bnRpbWVcbiAgICAgICAgICAgICAgICAgICAgZGlkTmF2aWdhdGUgPSB0cnVlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGRldGVjdGVkRG9tYWluID0gKDAsIF9kZXRlY3Rkb21haW5sb2NhbGUuZGV0ZWN0RG9tYWluTG9jYWxlKSh0aGlzLmRvbWFpbkxvY2FsZXMsIHVuZGVmaW5lZCwgbmV4dFN0YXRlLmxvY2FsZSk7XG4gICAgICAgICAgICAvLyB3ZSBuZWVkIHRvIHdyYXAgdGhpcyBpbiB0aGUgZW52IGNoZWNrIGFnYWluIHNpbmNlIHJlZ2VuZXJhdG9yIHJ1bnRpbWVcbiAgICAgICAgICAgIC8vIG1vdmVzIHRoaXMgb24gaXRzIG93biBkdWUgdG8gdGhlIHJldHVyblxuICAgICAgICAgICAgaWYgKHByb2Nlc3MuZW52Ll9fTkVYVF9JMThOX1NVUFBPUlQpIHtcbiAgICAgICAgICAgICAgICAvLyBpZiB3ZSBhcmUgbmF2aWdhdGluZyB0byBhIGRvbWFpbiBsb2NhbGUgZW5zdXJlIHdlIHJlZGlyZWN0IHRvIHRoZVxuICAgICAgICAgICAgICAgIC8vIGNvcnJlY3QgZG9tYWluXG4gICAgICAgICAgICAgICAgaWYgKCFkaWROYXZpZ2F0ZSAmJiBkZXRlY3RlZERvbWFpbiAmJiB0aGlzLmlzTG9jYWxlRG9tYWluICYmIHNlbGYubG9jYXRpb24uaG9zdG5hbWUgIT09IGRldGVjdGVkRG9tYWluLmRvbWFpbikge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBhc05vQmFzZVBhdGggPSAoMCwgX3JlbW92ZWJhc2VwYXRoLnJlbW92ZUJhc2VQYXRoKShhcyk7XG4gICAgICAgICAgICAgICAgICAgIGhhbmRsZUhhcmROYXZpZ2F0aW9uKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHVybDogXCJodHRwXCIgKyAoZGV0ZWN0ZWREb21haW4uaHR0cCA/ICcnIDogJ3MnKSArIFwiOi8vXCIgKyBkZXRlY3RlZERvbWFpbi5kb21haW4gKyAoMCwgX2FkZGJhc2VwYXRoLmFkZEJhc2VQYXRoKShcIlwiICsgKG5leHRTdGF0ZS5sb2NhbGUgPT09IGRldGVjdGVkRG9tYWluLmRlZmF1bHRMb2NhbGUgPyAnJyA6IFwiL1wiICsgbmV4dFN0YXRlLmxvY2FsZSkgKyAoYXNOb0Jhc2VQYXRoID09PSAnLycgPyAnJyA6IGFzTm9CYXNlUGF0aCkgfHwgJy8nKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJvdXRlcjogdGhpc1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgLy8gdGhpcyB3YXMgcHJldmlvdXNseSBhIHJldHVybiBidXQgd2FzIHJlbW92ZWQgaW4gZmF2b3JcbiAgICAgICAgICAgICAgICAgICAgLy8gb2YgYmV0dGVyIGRlYWQgY29kZSBlbGltaW5hdGlvbiB3aXRoIHJlZ2VuZXJhdG9yIHJ1bnRpbWVcbiAgICAgICAgICAgICAgICAgICAgZGlkTmF2aWdhdGUgPSB0cnVlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChkaWROYXZpZ2F0ZSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgoKT0+e30pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIC8vIG1hcmtpbmcgcm91dGUgY2hhbmdlcyBhcyBhIG5hdmlnYXRpb24gc3RhcnQgZW50cnlcbiAgICAgICAgaWYgKF91dGlscy5TVCkge1xuICAgICAgICAgICAgcGVyZm9ybWFuY2UubWFyaygncm91dGVDaGFuZ2UnKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB7IHNoYWxsb3cgPSBmYWxzZSwgc2Nyb2xsID0gdHJ1ZSB9ID0gb3B0aW9ucztcbiAgICAgICAgY29uc3Qgcm91dGVQcm9wcyA9IHtcbiAgICAgICAgICAgIHNoYWxsb3dcbiAgICAgICAgfTtcbiAgICAgICAgaWYgKHRoaXMuX2luRmxpZ2h0Um91dGUgJiYgdGhpcy5jbGMpIHtcbiAgICAgICAgICAgIGlmICghaXNTc3IpIHtcbiAgICAgICAgICAgICAgICBSb3V0ZXIuZXZlbnRzLmVtaXQoJ3JvdXRlQ2hhbmdlRXJyb3InLCBidWlsZENhbmNlbGxhdGlvbkVycm9yKCksIHRoaXMuX2luRmxpZ2h0Um91dGUsIHJvdXRlUHJvcHMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5jbGMoKTtcbiAgICAgICAgICAgIHRoaXMuY2xjID0gbnVsbDtcbiAgICAgICAgfVxuICAgICAgICBhcyA9ICgwLCBfYWRkYmFzZXBhdGguYWRkQmFzZVBhdGgpKCgwLCBfYWRkbG9jYWxlLmFkZExvY2FsZSkoKDAsIF9oYXNiYXNlcGF0aC5oYXNCYXNlUGF0aCkoYXMpID8gKDAsIF9yZW1vdmViYXNlcGF0aC5yZW1vdmVCYXNlUGF0aCkoYXMpIDogYXMsIG9wdGlvbnMubG9jYWxlLCB0aGlzLmRlZmF1bHRMb2NhbGUpKTtcbiAgICAgICAgY29uc3QgY2xlYW5lZEFzID0gKDAsIF9yZW1vdmVsb2NhbGUucmVtb3ZlTG9jYWxlKSgoMCwgX2hhc2Jhc2VwYXRoLmhhc0Jhc2VQYXRoKShhcykgPyAoMCwgX3JlbW92ZWJhc2VwYXRoLnJlbW92ZUJhc2VQYXRoKShhcykgOiBhcywgbmV4dFN0YXRlLmxvY2FsZSk7XG4gICAgICAgIHRoaXMuX2luRmxpZ2h0Um91dGUgPSBhcztcbiAgICAgICAgY29uc3QgbG9jYWxlQ2hhbmdlID0gcHJldkxvY2FsZSAhPT0gbmV4dFN0YXRlLmxvY2FsZTtcbiAgICAgICAgLy8gSWYgdGhlIHVybCBjaGFuZ2UgaXMgb25seSByZWxhdGVkIHRvIGEgaGFzaCBjaGFuZ2VcbiAgICAgICAgLy8gV2Ugc2hvdWxkIG5vdCBwcm9jZWVkLiBXZSBzaG91bGQgb25seSBjaGFuZ2UgdGhlIHN0YXRlLlxuICAgICAgICBpZiAoIWlzUXVlcnlVcGRhdGluZyAmJiB0aGlzLm9ubHlBSGFzaENoYW5nZShjbGVhbmVkQXMpICYmICFsb2NhbGVDaGFuZ2UpIHtcbiAgICAgICAgICAgIG5leHRTdGF0ZS5hc1BhdGggPSBjbGVhbmVkQXM7XG4gICAgICAgICAgICBSb3V0ZXIuZXZlbnRzLmVtaXQoJ2hhc2hDaGFuZ2VTdGFydCcsIGFzLCByb3V0ZVByb3BzKTtcbiAgICAgICAgICAgIC8vIFRPRE86IGRvIHdlIG5lZWQgdGhlIHJlc29sdmVkIGhyZWYgd2hlbiBvbmx5IGEgaGFzaCBjaGFuZ2U/XG4gICAgICAgICAgICB0aGlzLmNoYW5nZVN0YXRlKG1ldGhvZCwgdXJsLCBhcywge1xuICAgICAgICAgICAgICAgIC4uLm9wdGlvbnMsXG4gICAgICAgICAgICAgICAgc2Nyb2xsOiBmYWxzZVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBpZiAoc2Nyb2xsKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5zY3JvbGxUb0hhc2goY2xlYW5lZEFzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgYXdhaXQgdGhpcy5zZXQobmV4dFN0YXRlLCB0aGlzLmNvbXBvbmVudHNbbmV4dFN0YXRlLnJvdXRlXSwgbnVsbCk7XG4gICAgICAgICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgICAgICBpZiAoKDAsIF9pc2Vycm9yLmRlZmF1bHQpKGVycikgJiYgZXJyLmNhbmNlbGxlZCkge1xuICAgICAgICAgICAgICAgICAgICBSb3V0ZXIuZXZlbnRzLmVtaXQoJ3JvdXRlQ2hhbmdlRXJyb3InLCBlcnIsIGNsZWFuZWRBcywgcm91dGVQcm9wcyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRocm93IGVycjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIFJvdXRlci5ldmVudHMuZW1pdCgnaGFzaENoYW5nZUNvbXBsZXRlJywgYXMsIHJvdXRlUHJvcHMpO1xuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgbGV0IHBhcnNlZCA9ICgwLCBfcGFyc2VyZWxhdGl2ZXVybC5wYXJzZVJlbGF0aXZlVXJsKSh1cmwpO1xuICAgICAgICBsZXQgeyBwYXRobmFtZSwgcXVlcnkgfSA9IHBhcnNlZDtcbiAgICAgICAgLy8gVGhlIGJ1aWxkIG1hbmlmZXN0IG5lZWRzIHRvIGJlIGxvYWRlZCBiZWZvcmUgYXV0by1zdGF0aWMgZHluYW1pYyBwYWdlc1xuICAgICAgICAvLyBnZXQgdGhlaXIgcXVlcnkgcGFyYW1ldGVycyB0byBhbGxvdyBlbnN1cmluZyB0aGV5IGNhbiBiZSBwYXJzZWQgcHJvcGVybHlcbiAgICAgICAgLy8gd2hlbiByZXdyaXR0ZW4gdG9cbiAgICAgICAgbGV0IHBhZ2VzLCByZXdyaXRlcztcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIDtcbiAgICAgICAgICAgIFtwYWdlcywgeyBfX3Jld3JpdGVzOiByZXdyaXRlcyB9XSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgICAgICAgICAgICB0aGlzLnBhZ2VMb2FkZXIuZ2V0UGFnZUxpc3QoKSxcbiAgICAgICAgICAgICAgICAoMCwgX3JvdXRlbG9hZGVyLmdldENsaWVudEJ1aWxkTWFuaWZlc3QpKCksXG4gICAgICAgICAgICAgICAgdGhpcy5wYWdlTG9hZGVyLmdldE1pZGRsZXdhcmUoKVxuICAgICAgICAgICAgXSk7XG4gICAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICAgICAgLy8gSWYgd2UgZmFpbCB0byByZXNvbHZlIHRoZSBwYWdlIGxpc3Qgb3IgY2xpZW50LWJ1aWxkIG1hbmlmZXN0LCB3ZSBtdXN0XG4gICAgICAgICAgICAvLyBkbyBhIHNlcnZlci1zaWRlIHRyYW5zaXRpb246XG4gICAgICAgICAgICBoYW5kbGVIYXJkTmF2aWdhdGlvbih7XG4gICAgICAgICAgICAgICAgdXJsOiBhcyxcbiAgICAgICAgICAgICAgICByb3V0ZXI6IHRoaXNcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIC8vIElmIGFza2VkIHRvIGNoYW5nZSB0aGUgY3VycmVudCBVUkwgd2Ugc2hvdWxkIHJlbG9hZCB0aGUgY3VycmVudCBwYWdlXG4gICAgICAgIC8vIChub3QgbG9jYXRpb24ucmVsb2FkKCkgYnV0IHJlbG9hZCBnZXRJbml0aWFsUHJvcHMgYW5kIG90aGVyIE5leHQuanMgc3R1ZmZzKVxuICAgICAgICAvLyBXZSBhbHNvIG5lZWQgdG8gc2V0IHRoZSBtZXRob2QgPSByZXBsYWNlU3RhdGUgYWx3YXlzXG4gICAgICAgIC8vIGFzIHRoaXMgc2hvdWxkIG5vdCBnbyBpbnRvIHRoZSBoaXN0b3J5IChUaGF0J3MgaG93IGJyb3dzZXJzIHdvcmspXG4gICAgICAgIC8vIFdlIHNob3VsZCBjb21wYXJlIHRoZSBuZXcgYXNQYXRoIHRvIHRoZSBjdXJyZW50IGFzUGF0aCwgbm90IHRoZSB1cmxcbiAgICAgICAgaWYgKCF0aGlzLnVybElzTmV3KGNsZWFuZWRBcykgJiYgIWxvY2FsZUNoYW5nZSkge1xuICAgICAgICAgICAgbWV0aG9kID0gJ3JlcGxhY2VTdGF0ZSc7XG4gICAgICAgIH1cbiAgICAgICAgLy8gd2UgbmVlZCB0byByZXNvbHZlIHRoZSBhcyB2YWx1ZSB1c2luZyByZXdyaXRlcyBmb3IgZHluYW1pYyBTU0dcbiAgICAgICAgLy8gcGFnZXMgdG8gYWxsb3cgYnVpbGRpbmcgdGhlIGRhdGEgVVJMIGNvcnJlY3RseVxuICAgICAgICBsZXQgcmVzb2x2ZWRBcyA9IGFzO1xuICAgICAgICAvLyB1cmwgYW5kIGFzIHNob3VsZCBhbHdheXMgYmUgcHJlZml4ZWQgd2l0aCBiYXNlUGF0aCBieSB0aGlzXG4gICAgICAgIC8vIHBvaW50IGJ5IGVpdGhlciBuZXh0L2xpbmsgb3Igcm91dGVyLnB1c2gvcmVwbGFjZSBzbyBzdHJpcCB0aGVcbiAgICAgICAgLy8gYmFzZVBhdGggZnJvbSB0aGUgcGF0aG5hbWUgdG8gbWF0Y2ggdGhlIHBhZ2VzIGRpciAxLXRvLTFcbiAgICAgICAgcGF0aG5hbWUgPSBwYXRobmFtZSA/ICgwLCBfcmVtb3ZldHJhaWxpbmdzbGFzaC5yZW1vdmVUcmFpbGluZ1NsYXNoKSgoMCwgX3JlbW92ZWJhc2VwYXRoLnJlbW92ZUJhc2VQYXRoKShwYXRobmFtZSkpIDogcGF0aG5hbWU7XG4gICAgICAgIGxldCByb3V0ZSA9ICgwLCBfcmVtb3ZldHJhaWxpbmdzbGFzaC5yZW1vdmVUcmFpbGluZ1NsYXNoKShwYXRobmFtZSk7XG4gICAgICAgIGNvbnN0IHBhcnNlZEFzUGF0aG5hbWUgPSBhcy5zdGFydHNXaXRoKCcvJykgJiYgKDAsIF9wYXJzZXJlbGF0aXZldXJsLnBhcnNlUmVsYXRpdmVVcmwpKGFzKS5wYXRobmFtZTtcbiAgICAgICAgLy8gaWYgd2UgZGV0ZWN0ZWQgdGhlIHBhdGggYXMgYXBwIHJvdXRlIGR1cmluZyBwcmVmZXRjaGluZ1xuICAgICAgICAvLyB0cmlnZ2VyIGhhcmQgbmF2aWdhdGlvblxuICAgICAgICBpZiAoKF90aGlzX2NvbXBvbmVudHNfcGF0aG5hbWUgPSB0aGlzLmNvbXBvbmVudHNbcGF0aG5hbWVdKSA9PSBudWxsID8gdm9pZCAwIDogX3RoaXNfY29tcG9uZW50c19wYXRobmFtZS5fX2FwcFJvdXRlcikge1xuICAgICAgICAgICAgaGFuZGxlSGFyZE5hdmlnYXRpb24oe1xuICAgICAgICAgICAgICAgIHVybDogYXMsXG4gICAgICAgICAgICAgICAgcm91dGVyOiB0aGlzXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgoKT0+e30pO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGlzTWlkZGxld2FyZVJld3JpdGUgPSAhIShwYXJzZWRBc1BhdGhuYW1lICYmIHJvdXRlICE9PSBwYXJzZWRBc1BhdGhuYW1lICYmICghKDAsIF9pc2R5bmFtaWMuaXNEeW5hbWljUm91dGUpKHJvdXRlKSB8fCAhKDAsIF9yb3V0ZW1hdGNoZXIuZ2V0Um91dGVNYXRjaGVyKSgoMCwgX3JvdXRlcmVnZXguZ2V0Um91dGVSZWdleCkocm91dGUpKShwYXJzZWRBc1BhdGhuYW1lKSkpO1xuICAgICAgICAvLyB3ZSBkb24ndCBhdHRlbXB0IHJlc29sdmUgYXNQYXRoIHdoZW4gd2UgbmVlZCB0byBleGVjdXRlXG4gICAgICAgIC8vIG1pZGRsZXdhcmUgYXMgdGhlIHJlc29sdmluZyB3aWxsIG9jY3VyIHNlcnZlci1zaWRlXG4gICAgICAgIGNvbnN0IGlzTWlkZGxld2FyZU1hdGNoID0gIW9wdGlvbnMuc2hhbGxvdyAmJiBhd2FpdCBtYXRjaGVzTWlkZGxld2FyZSh7XG4gICAgICAgICAgICBhc1BhdGg6IGFzLFxuICAgICAgICAgICAgbG9jYWxlOiBuZXh0U3RhdGUubG9jYWxlLFxuICAgICAgICAgICAgcm91dGVyOiB0aGlzXG4gICAgICAgIH0pO1xuICAgICAgICBpZiAoaXNRdWVyeVVwZGF0aW5nICYmIGlzTWlkZGxld2FyZU1hdGNoKSB7XG4gICAgICAgICAgICBzaG91bGRSZXNvbHZlSHJlZiA9IGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIGlmIChzaG91bGRSZXNvbHZlSHJlZiAmJiBwYXRobmFtZSAhPT0gJy9fZXJyb3InKSB7XG4gICAgICAgICAgICA7XG4gICAgICAgICAgICBvcHRpb25zLl9zaG91bGRSZXNvbHZlSHJlZiA9IHRydWU7XG4gICAgICAgICAgICBpZiAocHJvY2Vzcy5lbnYuX19ORVhUX0hBU19SRVdSSVRFUyAmJiBhcy5zdGFydHNXaXRoKCcvJykpIHtcbiAgICAgICAgICAgICAgICBjb25zdCByZXdyaXRlc1Jlc3VsdCA9ICgwLCBfcmVzb2x2ZXJld3JpdGVzLmRlZmF1bHQpKCgwLCBfYWRkYmFzZXBhdGguYWRkQmFzZVBhdGgpKCgwLCBfYWRkbG9jYWxlLmFkZExvY2FsZSkoY2xlYW5lZEFzLCBuZXh0U3RhdGUubG9jYWxlKSwgdHJ1ZSksIHBhZ2VzLCByZXdyaXRlcywgcXVlcnksIChwKT0+cmVzb2x2ZUR5bmFtaWNSb3V0ZShwLCBwYWdlcyksIHRoaXMubG9jYWxlcyk7XG4gICAgICAgICAgICAgICAgaWYgKHJld3JpdGVzUmVzdWx0LmV4dGVybmFsRGVzdCkge1xuICAgICAgICAgICAgICAgICAgICBoYW5kbGVIYXJkTmF2aWdhdGlvbih7XG4gICAgICAgICAgICAgICAgICAgICAgICB1cmw6IGFzLFxuICAgICAgICAgICAgICAgICAgICAgICAgcm91dGVyOiB0aGlzXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKCFpc01pZGRsZXdhcmVNYXRjaCkge1xuICAgICAgICAgICAgICAgICAgICByZXNvbHZlZEFzID0gcmV3cml0ZXNSZXN1bHQuYXNQYXRoO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAocmV3cml0ZXNSZXN1bHQubWF0Y2hlZFBhZ2UgJiYgcmV3cml0ZXNSZXN1bHQucmVzb2x2ZWRIcmVmKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIGlmIHRoaXMgZGlyZWN0bHkgbWF0Y2hlcyBhIHBhZ2Ugd2UgbmVlZCB0byB1cGRhdGUgdGhlIGhyZWYgdG9cbiAgICAgICAgICAgICAgICAgICAgLy8gYWxsb3cgdGhlIGNvcnJlY3QgcGFnZSBjaHVuayB0byBiZSBsb2FkZWRcbiAgICAgICAgICAgICAgICAgICAgcGF0aG5hbWUgPSByZXdyaXRlc1Jlc3VsdC5yZXNvbHZlZEhyZWY7XG4gICAgICAgICAgICAgICAgICAgIHBhcnNlZC5wYXRobmFtZSA9ICgwLCBfYWRkYmFzZXBhdGguYWRkQmFzZVBhdGgpKHBhdGhuYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFpc01pZGRsZXdhcmVNYXRjaCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdXJsID0gKDAsIF9mb3JtYXR1cmwuZm9ybWF0V2l0aFZhbGlkYXRpb24pKHBhcnNlZCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHBhcnNlZC5wYXRobmFtZSA9IHJlc29sdmVEeW5hbWljUm91dGUocGF0aG5hbWUsIHBhZ2VzKTtcbiAgICAgICAgICAgICAgICBpZiAocGFyc2VkLnBhdGhuYW1lICE9PSBwYXRobmFtZSkge1xuICAgICAgICAgICAgICAgICAgICBwYXRobmFtZSA9IHBhcnNlZC5wYXRobmFtZTtcbiAgICAgICAgICAgICAgICAgICAgcGFyc2VkLnBhdGhuYW1lID0gKDAsIF9hZGRiYXNlcGF0aC5hZGRCYXNlUGF0aCkocGF0aG5hbWUpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoIWlzTWlkZGxld2FyZU1hdGNoKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB1cmwgPSAoMCwgX2Zvcm1hdHVybC5mb3JtYXRXaXRoVmFsaWRhdGlvbikocGFyc2VkKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAoISgwLCBfaXNsb2NhbHVybC5pc0xvY2FsVVJMKShhcykpIHtcbiAgICAgICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgT2JqZWN0LmRlZmluZVByb3BlcnR5KG5ldyBFcnJvcignSW52YWxpZCBocmVmOiBcIicgKyB1cmwgKyAnXCIgYW5kIGFzOiBcIicgKyBhcyArICdcIiwgcmVjZWl2ZWQgcmVsYXRpdmUgaHJlZiBhbmQgZXh0ZXJuYWwgYXMnICsgXCJcXG5TZWUgbW9yZSBpbmZvOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9pbnZhbGlkLXJlbGF0aXZlLXVybC1leHRlcm5hbC1hc1wiKSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIkUzODBcIixcbiAgICAgICAgICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaGFuZGxlSGFyZE5hdmlnYXRpb24oe1xuICAgICAgICAgICAgICAgIHVybDogYXMsXG4gICAgICAgICAgICAgICAgcm91dGVyOiB0aGlzXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICByZXNvbHZlZEFzID0gKDAsIF9yZW1vdmVsb2NhbGUucmVtb3ZlTG9jYWxlKSgoMCwgX3JlbW92ZWJhc2VwYXRoLnJlbW92ZUJhc2VQYXRoKShyZXNvbHZlZEFzKSwgbmV4dFN0YXRlLmxvY2FsZSk7XG4gICAgICAgIHJvdXRlID0gKDAsIF9yZW1vdmV0cmFpbGluZ3NsYXNoLnJlbW92ZVRyYWlsaW5nU2xhc2gpKHBhdGhuYW1lKTtcbiAgICAgICAgbGV0IHJvdXRlTWF0Y2ggPSBmYWxzZTtcbiAgICAgICAgaWYgKCgwLCBfaXNkeW5hbWljLmlzRHluYW1pY1JvdXRlKShyb3V0ZSkpIHtcbiAgICAgICAgICAgIGNvbnN0IHBhcnNlZEFzID0gKDAsIF9wYXJzZXJlbGF0aXZldXJsLnBhcnNlUmVsYXRpdmVVcmwpKHJlc29sdmVkQXMpO1xuICAgICAgICAgICAgY29uc3QgYXNQYXRobmFtZSA9IHBhcnNlZEFzLnBhdGhuYW1lO1xuICAgICAgICAgICAgY29uc3Qgcm91dGVSZWdleCA9ICgwLCBfcm91dGVyZWdleC5nZXRSb3V0ZVJlZ2V4KShyb3V0ZSk7XG4gICAgICAgICAgICByb3V0ZU1hdGNoID0gKDAsIF9yb3V0ZW1hdGNoZXIuZ2V0Um91dGVNYXRjaGVyKShyb3V0ZVJlZ2V4KShhc1BhdGhuYW1lKTtcbiAgICAgICAgICAgIGNvbnN0IHNob3VsZEludGVycG9sYXRlID0gcm91dGUgPT09IGFzUGF0aG5hbWU7XG4gICAgICAgICAgICBjb25zdCBpbnRlcnBvbGF0ZWRBcyA9IHNob3VsZEludGVycG9sYXRlID8gKDAsIF9pbnRlcnBvbGF0ZWFzLmludGVycG9sYXRlQXMpKHJvdXRlLCBhc1BhdGhuYW1lLCBxdWVyeSkgOiB7fTtcbiAgICAgICAgICAgIGlmICghcm91dGVNYXRjaCB8fCBzaG91bGRJbnRlcnBvbGF0ZSAmJiAhaW50ZXJwb2xhdGVkQXMucmVzdWx0KSB7XG4gICAgICAgICAgICAgICAgY29uc3QgbWlzc2luZ1BhcmFtcyA9IE9iamVjdC5rZXlzKHJvdXRlUmVnZXguZ3JvdXBzKS5maWx0ZXIoKHBhcmFtKT0+IXF1ZXJ5W3BhcmFtXSAmJiAhcm91dGVSZWdleC5ncm91cHNbcGFyYW1dLm9wdGlvbmFsKTtcbiAgICAgICAgICAgICAgICBpZiAobWlzc2luZ1BhcmFtcy5sZW5ndGggPiAwICYmICFpc01pZGRsZXdhcmVNYXRjaCkge1xuICAgICAgICAgICAgICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKFwiXCIgKyAoc2hvdWxkSW50ZXJwb2xhdGUgPyBcIkludGVycG9sYXRpbmcgaHJlZlwiIDogXCJNaXNtYXRjaGluZyBgYXNgIGFuZCBgaHJlZmBcIikgKyBcIiBmYWlsZWQgdG8gbWFudWFsbHkgcHJvdmlkZSBcIiArIChcInRoZSBwYXJhbXM6IFwiICsgbWlzc2luZ1BhcmFtcy5qb2luKCcsICcpICsgXCIgaW4gdGhlIGBocmVmYCdzIGBxdWVyeWBcIikpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHRocm93IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuZXcgRXJyb3IoKHNob3VsZEludGVycG9sYXRlID8gXCJUaGUgcHJvdmlkZWQgYGhyZWZgIChcIiArIHVybCArIFwiKSB2YWx1ZSBpcyBtaXNzaW5nIHF1ZXJ5IHZhbHVlcyAoXCIgKyBtaXNzaW5nUGFyYW1zLmpvaW4oJywgJykgKyBcIikgdG8gYmUgaW50ZXJwb2xhdGVkIHByb3Blcmx5LiBcIiA6IFwiVGhlIHByb3ZpZGVkIGBhc2AgdmFsdWUgKFwiICsgYXNQYXRobmFtZSArIFwiKSBpcyBpbmNvbXBhdGlibGUgd2l0aCB0aGUgYGhyZWZgIHZhbHVlIChcIiArIHJvdXRlICsgXCIpLiBcIikgKyAoXCJSZWFkIG1vcmU6IGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL21lc3NhZ2VzL1wiICsgKHNob3VsZEludGVycG9sYXRlID8gJ2hyZWYtaW50ZXJwb2xhdGlvbi1mYWlsZWQnIDogJ2luY29tcGF0aWJsZS1ocmVmLWFzJykpKSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogXCJFMzQ0XCIsXG4gICAgICAgICAgICAgICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGVsc2UgaWYgKHNob3VsZEludGVycG9sYXRlKSB7XG4gICAgICAgICAgICAgICAgYXMgPSAoMCwgX2Zvcm1hdHVybC5mb3JtYXRXaXRoVmFsaWRhdGlvbikoT2JqZWN0LmFzc2lnbih7fSwgcGFyc2VkQXMsIHtcbiAgICAgICAgICAgICAgICAgICAgcGF0aG5hbWU6IGludGVycG9sYXRlZEFzLnJlc3VsdCxcbiAgICAgICAgICAgICAgICAgICAgcXVlcnk6ICgwLCBfb21pdC5vbWl0KShxdWVyeSwgaW50ZXJwb2xhdGVkQXMucGFyYW1zKVxuICAgICAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgLy8gTWVyZ2UgcGFyYW1zIGludG8gYHF1ZXJ5YCwgb3ZlcndyaXRpbmcgYW55IHNwZWNpZmllZCBpbiBzZWFyY2hcbiAgICAgICAgICAgICAgICBPYmplY3QuYXNzaWduKHF1ZXJ5LCByb3V0ZU1hdGNoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAoIWlzUXVlcnlVcGRhdGluZykge1xuICAgICAgICAgICAgUm91dGVyLmV2ZW50cy5lbWl0KCdyb3V0ZUNoYW5nZVN0YXJ0JywgYXMsIHJvdXRlUHJvcHMpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGlzRXJyb3JSb3V0ZSA9IHRoaXMucGF0aG5hbWUgPT09ICcvNDA0JyB8fCB0aGlzLnBhdGhuYW1lID09PSAnL19lcnJvcic7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICB2YXIgX3NlbGZfX19ORVhUX0RBVEFfX19wcm9wc19wYWdlUHJvcHMsIF9zZWxmX19fTkVYVF9EQVRBX19fcHJvcHMsIF9yb3V0ZUluZm9fcHJvcHM7XG4gICAgICAgICAgICBsZXQgcm91dGVJbmZvID0gYXdhaXQgdGhpcy5nZXRSb3V0ZUluZm8oe1xuICAgICAgICAgICAgICAgIHJvdXRlLFxuICAgICAgICAgICAgICAgIHBhdGhuYW1lLFxuICAgICAgICAgICAgICAgIHF1ZXJ5LFxuICAgICAgICAgICAgICAgIGFzLFxuICAgICAgICAgICAgICAgIHJlc29sdmVkQXMsXG4gICAgICAgICAgICAgICAgcm91dGVQcm9wcyxcbiAgICAgICAgICAgICAgICBsb2NhbGU6IG5leHRTdGF0ZS5sb2NhbGUsXG4gICAgICAgICAgICAgICAgaXNQcmV2aWV3OiBuZXh0U3RhdGUuaXNQcmV2aWV3LFxuICAgICAgICAgICAgICAgIGhhc01pZGRsZXdhcmU6IGlzTWlkZGxld2FyZU1hdGNoLFxuICAgICAgICAgICAgICAgIHVuc3RhYmxlX3NraXBDbGllbnRDYWNoZTogb3B0aW9ucy51bnN0YWJsZV9za2lwQ2xpZW50Q2FjaGUsXG4gICAgICAgICAgICAgICAgaXNRdWVyeVVwZGF0aW5nOiBpc1F1ZXJ5VXBkYXRpbmcgJiYgIXRoaXMuaXNGYWxsYmFjayxcbiAgICAgICAgICAgICAgICBpc01pZGRsZXdhcmVSZXdyaXRlXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGlmICghaXNRdWVyeVVwZGF0aW5nICYmICFvcHRpb25zLnNoYWxsb3cpIHtcbiAgICAgICAgICAgICAgICBhd2FpdCB0aGlzLl9iZmwoYXMsICdyZXNvbHZlZEFzJyBpbiByb3V0ZUluZm8gPyByb3V0ZUluZm8ucmVzb2x2ZWRBcyA6IHVuZGVmaW5lZCwgbmV4dFN0YXRlLmxvY2FsZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoJ3JvdXRlJyBpbiByb3V0ZUluZm8gJiYgaXNNaWRkbGV3YXJlTWF0Y2gpIHtcbiAgICAgICAgICAgICAgICBwYXRobmFtZSA9IHJvdXRlSW5mby5yb3V0ZSB8fCByb3V0ZTtcbiAgICAgICAgICAgICAgICByb3V0ZSA9IHBhdGhuYW1lO1xuICAgICAgICAgICAgICAgIGlmICghcm91dGVQcm9wcy5zaGFsbG93KSB7XG4gICAgICAgICAgICAgICAgICAgIHF1ZXJ5ID0gT2JqZWN0LmFzc2lnbih7fSwgcm91dGVJbmZvLnF1ZXJ5IHx8IHt9LCBxdWVyeSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IGNsZWFuZWRQYXJzZWRQYXRobmFtZSA9ICgwLCBfaGFzYmFzZXBhdGguaGFzQmFzZVBhdGgpKHBhcnNlZC5wYXRobmFtZSkgPyAoMCwgX3JlbW92ZWJhc2VwYXRoLnJlbW92ZUJhc2VQYXRoKShwYXJzZWQucGF0aG5hbWUpIDogcGFyc2VkLnBhdGhuYW1lO1xuICAgICAgICAgICAgICAgIGlmIChyb3V0ZU1hdGNoICYmIHBhdGhuYW1lICE9PSBjbGVhbmVkUGFyc2VkUGF0aG5hbWUpIHtcbiAgICAgICAgICAgICAgICAgICAgT2JqZWN0LmtleXMocm91dGVNYXRjaCkuZm9yRWFjaCgoa2V5KT0+e1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJvdXRlTWF0Y2ggJiYgcXVlcnlba2V5XSA9PT0gcm91dGVNYXRjaFtrZXldKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVsZXRlIHF1ZXJ5W2tleV07XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoKDAsIF9pc2R5bmFtaWMuaXNEeW5hbWljUm91dGUpKHBhdGhuYW1lKSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBwcmVmaXhlZEFzID0gIXJvdXRlUHJvcHMuc2hhbGxvdyAmJiByb3V0ZUluZm8ucmVzb2x2ZWRBcyA/IHJvdXRlSW5mby5yZXNvbHZlZEFzIDogKDAsIF9hZGRiYXNlcGF0aC5hZGRCYXNlUGF0aCkoKDAsIF9hZGRsb2NhbGUuYWRkTG9jYWxlKShuZXcgVVJMKGFzLCBsb2NhdGlvbi5ocmVmKS5wYXRobmFtZSwgbmV4dFN0YXRlLmxvY2FsZSksIHRydWUpO1xuICAgICAgICAgICAgICAgICAgICBsZXQgcmV3cml0ZUFzID0gcHJlZml4ZWRBcztcbiAgICAgICAgICAgICAgICAgICAgaWYgKCgwLCBfaGFzYmFzZXBhdGguaGFzQmFzZVBhdGgpKHJld3JpdGVBcykpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJld3JpdGVBcyA9ICgwLCBfcmVtb3ZlYmFzZXBhdGgucmVtb3ZlQmFzZVBhdGgpKHJld3JpdGVBcyk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgaWYgKHByb2Nlc3MuZW52Ll9fTkVYVF9JMThOX1NVUFBPUlQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGxvY2FsZVJlc3VsdCA9ICgwLCBfbm9ybWFsaXplbG9jYWxlcGF0aC5ub3JtYWxpemVMb2NhbGVQYXRoKShyZXdyaXRlQXMsIHRoaXMubG9jYWxlcyk7XG4gICAgICAgICAgICAgICAgICAgICAgICBuZXh0U3RhdGUubG9jYWxlID0gbG9jYWxlUmVzdWx0LmRldGVjdGVkTG9jYWxlIHx8IG5leHRTdGF0ZS5sb2NhbGU7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXdyaXRlQXMgPSBsb2NhbGVSZXN1bHQucGF0aG5hbWU7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgcm91dGVSZWdleCA9ICgwLCBfcm91dGVyZWdleC5nZXRSb3V0ZVJlZ2V4KShwYXRobmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGN1clJvdXRlTWF0Y2ggPSAoMCwgX3JvdXRlbWF0Y2hlci5nZXRSb3V0ZU1hdGNoZXIpKHJvdXRlUmVnZXgpKG5ldyBVUkwocmV3cml0ZUFzLCBsb2NhdGlvbi5ocmVmKS5wYXRobmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChjdXJSb3V0ZU1hdGNoKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBPYmplY3QuYXNzaWduKHF1ZXJ5LCBjdXJSb3V0ZU1hdGNoKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIElmIHRoZSByb3V0ZUluZm8gYnJpbmdzIGEgcmVkaXJlY3Qgd2Ugc2ltcGx5IGFwcGx5IGl0LlxuICAgICAgICAgICAgaWYgKCd0eXBlJyBpbiByb3V0ZUluZm8pIHtcbiAgICAgICAgICAgICAgICBpZiAocm91dGVJbmZvLnR5cGUgPT09ICdyZWRpcmVjdC1pbnRlcm5hbCcpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuY2hhbmdlKG1ldGhvZCwgcm91dGVJbmZvLm5ld1VybCwgcm91dGVJbmZvLm5ld0FzLCBvcHRpb25zKTtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBoYW5kbGVIYXJkTmF2aWdhdGlvbih7XG4gICAgICAgICAgICAgICAgICAgICAgICB1cmw6IHJvdXRlSW5mby5kZXN0aW5hdGlvbixcbiAgICAgICAgICAgICAgICAgICAgICAgIHJvdXRlcjogdGhpc1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKCgpPT57fSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgY29tcG9uZW50ID0gcm91dGVJbmZvLkNvbXBvbmVudDtcbiAgICAgICAgICAgIGlmIChjb21wb25lbnQgJiYgY29tcG9uZW50LnVuc3RhYmxlX3NjcmlwdExvYWRlcikge1xuICAgICAgICAgICAgICAgIGNvbnN0IHNjcmlwdHMgPSBbXS5jb25jYXQoY29tcG9uZW50LnVuc3RhYmxlX3NjcmlwdExvYWRlcigpKTtcbiAgICAgICAgICAgICAgICBzY3JpcHRzLmZvckVhY2goKHNjcmlwdCk9PntcbiAgICAgICAgICAgICAgICAgICAgKDAsIF9zY3JpcHQuaGFuZGxlQ2xpZW50U2NyaXB0TG9hZCkoc2NyaXB0LnByb3BzKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIGhhbmRsZSByZWRpcmVjdCBvbiBjbGllbnQtdHJhbnNpdGlvblxuICAgICAgICAgICAgaWYgKChyb3V0ZUluZm8uX19OX1NTRyB8fCByb3V0ZUluZm8uX19OX1NTUCkgJiYgcm91dGVJbmZvLnByb3BzKSB7XG4gICAgICAgICAgICAgICAgaWYgKHJvdXRlSW5mby5wcm9wcy5wYWdlUHJvcHMgJiYgcm91dGVJbmZvLnByb3BzLnBhZ2VQcm9wcy5fX05fUkVESVJFQ1QpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gVXNlIHRoZSBkZXN0aW5hdGlvbiBmcm9tIHJlZGlyZWN0IHdpdGhvdXQgYWRkaW5nIGxvY2FsZVxuICAgICAgICAgICAgICAgICAgICBvcHRpb25zLmxvY2FsZSA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBkZXN0aW5hdGlvbiA9IHJvdXRlSW5mby5wcm9wcy5wYWdlUHJvcHMuX19OX1JFRElSRUNUO1xuICAgICAgICAgICAgICAgICAgICAvLyBjaGVjayBpZiBkZXN0aW5hdGlvbiBpcyBpbnRlcm5hbCAocmVzb2x2ZXMgdG8gYSBwYWdlKSBhbmQgYXR0ZW1wdFxuICAgICAgICAgICAgICAgICAgICAvLyBjbGllbnQtbmF2aWdhdGlvbiBpZiBpdCBpcyBmYWxsaW5nIGJhY2sgdG8gaGFyZCBuYXZpZ2F0aW9uIGlmXG4gICAgICAgICAgICAgICAgICAgIC8vIGl0J3Mgbm90XG4gICAgICAgICAgICAgICAgICAgIGlmIChkZXN0aW5hdGlvbi5zdGFydHNXaXRoKCcvJykgJiYgcm91dGVJbmZvLnByb3BzLnBhZ2VQcm9wcy5fX05fUkVESVJFQ1RfQkFTRV9QQVRIICE9PSBmYWxzZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcGFyc2VkSHJlZiA9ICgwLCBfcGFyc2VyZWxhdGl2ZXVybC5wYXJzZVJlbGF0aXZlVXJsKShkZXN0aW5hdGlvbik7XG4gICAgICAgICAgICAgICAgICAgICAgICBwYXJzZWRIcmVmLnBhdGhuYW1lID0gcmVzb2x2ZUR5bmFtaWNSb3V0ZShwYXJzZWRIcmVmLnBhdGhuYW1lLCBwYWdlcyk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB7IHVybDogbmV3VXJsLCBhczogbmV3QXMgfSA9IHByZXBhcmVVcmxBcyh0aGlzLCBkZXN0aW5hdGlvbiwgZGVzdGluYXRpb24pO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuY2hhbmdlKG1ldGhvZCwgbmV3VXJsLCBuZXdBcywgb3B0aW9ucyk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgaGFuZGxlSGFyZE5hdmlnYXRpb24oe1xuICAgICAgICAgICAgICAgICAgICAgICAgdXJsOiBkZXN0aW5hdGlvbixcbiAgICAgICAgICAgICAgICAgICAgICAgIHJvdXRlcjogdGhpc1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKCgpPT57fSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIG5leHRTdGF0ZS5pc1ByZXZpZXcgPSAhIXJvdXRlSW5mby5wcm9wcy5fX05fUFJFVklFVztcbiAgICAgICAgICAgICAgICAvLyBoYW5kbGUgU1NHIGRhdGEgNDA0XG4gICAgICAgICAgICAgICAgaWYgKHJvdXRlSW5mby5wcm9wcy5ub3RGb3VuZCA9PT0gU1NHX0RBVEFfTk9UX0ZPVU5EKSB7XG4gICAgICAgICAgICAgICAgICAgIGxldCBub3RGb3VuZFJvdXRlO1xuICAgICAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgdGhpcy5mZXRjaENvbXBvbmVudCgnLzQwNCcpO1xuICAgICAgICAgICAgICAgICAgICAgICAgbm90Rm91bmRSb3V0ZSA9ICcvNDA0JztcbiAgICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoXykge1xuICAgICAgICAgICAgICAgICAgICAgICAgbm90Rm91bmRSb3V0ZSA9ICcvX2Vycm9yJztcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICByb3V0ZUluZm8gPSBhd2FpdCB0aGlzLmdldFJvdXRlSW5mbyh7XG4gICAgICAgICAgICAgICAgICAgICAgICByb3V0ZTogbm90Rm91bmRSb3V0ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhdGhuYW1lOiBub3RGb3VuZFJvdXRlLFxuICAgICAgICAgICAgICAgICAgICAgICAgcXVlcnksXG4gICAgICAgICAgICAgICAgICAgICAgICBhcyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc29sdmVkQXMsXG4gICAgICAgICAgICAgICAgICAgICAgICByb3V0ZVByb3BzOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2hhbGxvdzogZmFsc2VcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICBsb2NhbGU6IG5leHRTdGF0ZS5sb2NhbGUsXG4gICAgICAgICAgICAgICAgICAgICAgICBpc1ByZXZpZXc6IG5leHRTdGF0ZS5pc1ByZXZpZXcsXG4gICAgICAgICAgICAgICAgICAgICAgICBpc05vdEZvdW5kOiB0cnVlXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICBpZiAoJ3R5cGUnIGluIHJvdXRlSW5mbykge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgT2JqZWN0LmRlZmluZVByb3BlcnR5KG5ldyBFcnJvcihcIlVuZXhwZWN0ZWQgbWlkZGxld2FyZSBlZmZlY3Qgb24gLzQwNFwiKSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IFwiRTE1OFwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaXNRdWVyeVVwZGF0aW5nICYmIHRoaXMucGF0aG5hbWUgPT09ICcvX2Vycm9yJyAmJiAoKF9zZWxmX19fTkVYVF9EQVRBX19fcHJvcHMgPSBzZWxmLl9fTkVYVF9EQVRBX18ucHJvcHMpID09IG51bGwgPyB2b2lkIDAgOiAoX3NlbGZfX19ORVhUX0RBVEFfX19wcm9wc19wYWdlUHJvcHMgPSBfc2VsZl9fX05FWFRfREFUQV9fX3Byb3BzLnBhZ2VQcm9wcykgPT0gbnVsbCA/IHZvaWQgMCA6IF9zZWxmX19fTkVYVF9EQVRBX19fcHJvcHNfcGFnZVByb3BzLnN0YXR1c0NvZGUpID09PSA1MDAgJiYgKChfcm91dGVJbmZvX3Byb3BzID0gcm91dGVJbmZvLnByb3BzKSA9PSBudWxsID8gdm9pZCAwIDogX3JvdXRlSW5mb19wcm9wcy5wYWdlUHJvcHMpKSB7XG4gICAgICAgICAgICAgICAgLy8gZW5zdXJlIHN0YXR1c0NvZGUgaXMgc3RpbGwgY29ycmVjdCBmb3Igc3RhdGljIDUwMCBwYWdlXG4gICAgICAgICAgICAgICAgLy8gd2hlbiB1cGRhdGluZyBxdWVyeSBpbmZvcm1hdGlvblxuICAgICAgICAgICAgICAgIHJvdXRlSW5mby5wcm9wcy5wYWdlUHJvcHMuc3RhdHVzQ29kZSA9IDUwMDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHZhciBfcm91dGVJbmZvX3JvdXRlO1xuICAgICAgICAgICAgLy8gc2hhbGxvdyByb3V0aW5nIGlzIG9ubHkgYWxsb3dlZCBmb3Igc2FtZSBwYWdlIFVSTCBjaGFuZ2VzLlxuICAgICAgICAgICAgY29uc3QgaXNWYWxpZFNoYWxsb3dSb3V0ZSA9IG9wdGlvbnMuc2hhbGxvdyAmJiBuZXh0U3RhdGUucm91dGUgPT09ICgoX3JvdXRlSW5mb19yb3V0ZSA9IHJvdXRlSW5mby5yb3V0ZSkgIT0gbnVsbCA/IF9yb3V0ZUluZm9fcm91dGUgOiByb3V0ZSk7XG4gICAgICAgICAgICB2YXIgX29wdGlvbnNfc2Nyb2xsO1xuICAgICAgICAgICAgY29uc3Qgc2hvdWxkU2Nyb2xsID0gKF9vcHRpb25zX3Njcm9sbCA9IG9wdGlvbnMuc2Nyb2xsKSAhPSBudWxsID8gX29wdGlvbnNfc2Nyb2xsIDogIWlzUXVlcnlVcGRhdGluZyAmJiAhaXNWYWxpZFNoYWxsb3dSb3V0ZTtcbiAgICAgICAgICAgIGNvbnN0IHJlc2V0U2Nyb2xsID0gc2hvdWxkU2Nyb2xsID8ge1xuICAgICAgICAgICAgICAgIHg6IDAsXG4gICAgICAgICAgICAgICAgeTogMFxuICAgICAgICAgICAgfSA6IG51bGw7XG4gICAgICAgICAgICBjb25zdCB1cGNvbWluZ1Njcm9sbFN0YXRlID0gZm9yY2VkU2Nyb2xsICE9IG51bGwgPyBmb3JjZWRTY3JvbGwgOiByZXNldFNjcm9sbDtcbiAgICAgICAgICAgIC8vIHRoZSBuZXcgc3RhdGUgdGhhdCB0aGUgcm91dGVyIGdvbm5hIHNldFxuICAgICAgICAgICAgY29uc3QgdXBjb21pbmdSb3V0ZXJTdGF0ZSA9IHtcbiAgICAgICAgICAgICAgICAuLi5uZXh0U3RhdGUsXG4gICAgICAgICAgICAgICAgcm91dGUsXG4gICAgICAgICAgICAgICAgcGF0aG5hbWUsXG4gICAgICAgICAgICAgICAgcXVlcnksXG4gICAgICAgICAgICAgICAgYXNQYXRoOiBjbGVhbmVkQXMsXG4gICAgICAgICAgICAgICAgaXNGYWxsYmFjazogZmFsc2VcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICAvLyBXaGVuIHRoZSBwYWdlIGJlaW5nIHJlbmRlcmVkIGlzIHRoZSA0MDQgcGFnZSwgd2Ugc2hvdWxkIG9ubHkgdXBkYXRlIHRoZVxuICAgICAgICAgICAgLy8gcXVlcnkgcGFyYW1ldGVycy4gUm91dGUgY2hhbmdlcyBoZXJlIG1pZ2h0IGFkZCB0aGUgYmFzZVBhdGggd2hlbiBpdFxuICAgICAgICAgICAgLy8gd2Fzbid0IG9yaWdpbmFsbHkgcHJlc2VudC4gVGhpcyBpcyBhbHNvIHdoeSB0aGlzIGJsb2NrIGlzIGJlZm9yZSB0aGVcbiAgICAgICAgICAgIC8vIGJlbG93IGBjaGFuZ2VTdGF0ZWAgY2FsbCB3aGljaCB1cGRhdGVzIHRoZSBicm93c2VyJ3MgaGlzdG9yeSAoY2hhbmdpbmdcbiAgICAgICAgICAgIC8vIHRoZSBVUkwpLlxuICAgICAgICAgICAgaWYgKGlzUXVlcnlVcGRhdGluZyAmJiBpc0Vycm9yUm91dGUpIHtcbiAgICAgICAgICAgICAgICB2YXIgX3NlbGZfX19ORVhUX0RBVEFfX19wcm9wc19wYWdlUHJvcHMxLCBfc2VsZl9fX05FWFRfREFUQV9fX3Byb3BzMSwgX3JvdXRlSW5mb19wcm9wczE7XG4gICAgICAgICAgICAgICAgcm91dGVJbmZvID0gYXdhaXQgdGhpcy5nZXRSb3V0ZUluZm8oe1xuICAgICAgICAgICAgICAgICAgICByb3V0ZTogdGhpcy5wYXRobmFtZSxcbiAgICAgICAgICAgICAgICAgICAgcGF0aG5hbWU6IHRoaXMucGF0aG5hbWUsXG4gICAgICAgICAgICAgICAgICAgIHF1ZXJ5LFxuICAgICAgICAgICAgICAgICAgICBhcyxcbiAgICAgICAgICAgICAgICAgICAgcmVzb2x2ZWRBcyxcbiAgICAgICAgICAgICAgICAgICAgcm91dGVQcm9wczoge1xuICAgICAgICAgICAgICAgICAgICAgICAgc2hhbGxvdzogZmFsc2VcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgbG9jYWxlOiBuZXh0U3RhdGUubG9jYWxlLFxuICAgICAgICAgICAgICAgICAgICBpc1ByZXZpZXc6IG5leHRTdGF0ZS5pc1ByZXZpZXcsXG4gICAgICAgICAgICAgICAgICAgIGlzUXVlcnlVcGRhdGluZzogaXNRdWVyeVVwZGF0aW5nICYmICF0aGlzLmlzRmFsbGJhY2tcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBpZiAoJ3R5cGUnIGluIHJvdXRlSW5mbykge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKFwiVW5leHBlY3RlZCBtaWRkbGV3YXJlIGVmZmVjdCBvbiBcIiArIHRoaXMucGF0aG5hbWUpLCBcIl9fTkVYVF9FUlJPUl9DT0RFXCIsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIkUyMjVcIixcbiAgICAgICAgICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAodGhpcy5wYXRobmFtZSA9PT0gJy9fZXJyb3InICYmICgoX3NlbGZfX19ORVhUX0RBVEFfX19wcm9wczEgPSBzZWxmLl9fTkVYVF9EQVRBX18ucHJvcHMpID09IG51bGwgPyB2b2lkIDAgOiAoX3NlbGZfX19ORVhUX0RBVEFfX19wcm9wc19wYWdlUHJvcHMxID0gX3NlbGZfX19ORVhUX0RBVEFfX19wcm9wczEucGFnZVByb3BzKSA9PSBudWxsID8gdm9pZCAwIDogX3NlbGZfX19ORVhUX0RBVEFfX19wcm9wc19wYWdlUHJvcHMxLnN0YXR1c0NvZGUpID09PSA1MDAgJiYgKChfcm91dGVJbmZvX3Byb3BzMSA9IHJvdXRlSW5mby5wcm9wcykgPT0gbnVsbCA/IHZvaWQgMCA6IF9yb3V0ZUluZm9fcHJvcHMxLnBhZ2VQcm9wcykpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gZW5zdXJlIHN0YXR1c0NvZGUgaXMgc3RpbGwgY29ycmVjdCBmb3Igc3RhdGljIDUwMCBwYWdlXG4gICAgICAgICAgICAgICAgICAgIC8vIHdoZW4gdXBkYXRpbmcgcXVlcnkgaW5mb3JtYXRpb25cbiAgICAgICAgICAgICAgICAgICAgcm91dGVJbmZvLnByb3BzLnBhZ2VQcm9wcy5zdGF0dXNDb2RlID0gNTAwO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBhd2FpdCB0aGlzLnNldCh1cGNvbWluZ1JvdXRlclN0YXRlLCByb3V0ZUluZm8sIHVwY29taW5nU2Nyb2xsU3RhdGUpO1xuICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICAgICAgICAgICAgICBpZiAoKDAsIF9pc2Vycm9yLmRlZmF1bHQpKGVycikgJiYgZXJyLmNhbmNlbGxlZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgUm91dGVyLmV2ZW50cy5lbWl0KCdyb3V0ZUNoYW5nZUVycm9yJywgZXJyLCBjbGVhbmVkQXMsIHJvdXRlUHJvcHMpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHRocm93IGVycjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBSb3V0ZXIuZXZlbnRzLmVtaXQoJ2JlZm9yZUhpc3RvcnlDaGFuZ2UnLCBhcywgcm91dGVQcm9wcyk7XG4gICAgICAgICAgICB0aGlzLmNoYW5nZVN0YXRlKG1ldGhvZCwgdXJsLCBhcywgb3B0aW9ucyk7XG4gICAgICAgICAgICAvLyBmb3IgcXVlcnkgdXBkYXRlcyB3ZSBjYW4gc2tpcCBpdCBpZiB0aGUgc3RhdGUgaXMgdW5jaGFuZ2VkIGFuZCB3ZSBkb24ndFxuICAgICAgICAgICAgLy8gbmVlZCB0byBzY3JvbGxcbiAgICAgICAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS92ZXJjZWwvbmV4dC5qcy9pc3N1ZXMvMzcxMzlcbiAgICAgICAgICAgIGNvbnN0IGNhblNraXBVcGRhdGluZyA9IGlzUXVlcnlVcGRhdGluZyAmJiAhdXBjb21pbmdTY3JvbGxTdGF0ZSAmJiAhcmVhZHlTdGF0ZUNoYW5nZSAmJiAhbG9jYWxlQ2hhbmdlICYmICgwLCBfY29tcGFyZXN0YXRlcy5jb21wYXJlUm91dGVyU3RhdGVzKSh1cGNvbWluZ1JvdXRlclN0YXRlLCB0aGlzLnN0YXRlKTtcbiAgICAgICAgICAgIGlmICghY2FuU2tpcFVwZGF0aW5nKSB7XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgdGhpcy5zZXQodXBjb21pbmdSb3V0ZXJTdGF0ZSwgcm91dGVJbmZvLCB1cGNvbWluZ1Njcm9sbFN0YXRlKTtcbiAgICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChlLmNhbmNlbGxlZCkgcm91dGVJbmZvLmVycm9yID0gcm91dGVJbmZvLmVycm9yIHx8IGU7XG4gICAgICAgICAgICAgICAgICAgIGVsc2UgdGhyb3cgZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKHJvdXRlSW5mby5lcnJvcikge1xuICAgICAgICAgICAgICAgICAgICBpZiAoIWlzUXVlcnlVcGRhdGluZykge1xuICAgICAgICAgICAgICAgICAgICAgICAgUm91dGVyLmV2ZW50cy5lbWl0KCdyb3V0ZUNoYW5nZUVycm9yJywgcm91dGVJbmZvLmVycm9yLCBjbGVhbmVkQXMsIHJvdXRlUHJvcHMpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHRocm93IHJvdXRlSW5mby5lcnJvcjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKHByb2Nlc3MuZW52Ll9fTkVYVF9JMThOX1NVUFBPUlQpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKG5leHRTdGF0ZS5sb2NhbGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5sYW5nID0gbmV4dFN0YXRlLmxvY2FsZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIWlzUXVlcnlVcGRhdGluZykge1xuICAgICAgICAgICAgICAgICAgICBSb3V0ZXIuZXZlbnRzLmVtaXQoJ3JvdXRlQ2hhbmdlQ29tcGxldGUnLCBhcywgcm91dGVQcm9wcyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIEEgaGFzaCBtYXJrICMgaXMgdGhlIG9wdGlvbmFsIGxhc3QgcGFydCBvZiBhIFVSTFxuICAgICAgICAgICAgICAgIGNvbnN0IGhhc2hSZWdleCA9IC8jLiskLztcbiAgICAgICAgICAgICAgICBpZiAoc2hvdWxkU2Nyb2xsICYmIGhhc2hSZWdleC50ZXN0KGFzKSkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnNjcm9sbFRvSGFzaChhcyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICAgICAgaWYgKCgwLCBfaXNlcnJvci5kZWZhdWx0KShlcnIpICYmIGVyci5jYW5jZWxsZWQpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aHJvdyBlcnI7XG4gICAgICAgIH1cbiAgICB9XG4gICAgY2hhbmdlU3RhdGUobWV0aG9kLCB1cmwsIGFzLCBvcHRpb25zKSB7XG4gICAgICAgIGlmIChvcHRpb25zID09PSB2b2lkIDApIG9wdGlvbnMgPSB7fTtcbiAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgICAgICAgICAgIGlmICh0eXBlb2Ygd2luZG93Lmhpc3RvcnkgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIldhcm5pbmc6IHdpbmRvdy5oaXN0b3J5IGlzIG5vdCBhdmFpbGFibGUuXCIpO1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICh0eXBlb2Ygd2luZG93Lmhpc3RvcnlbbWV0aG9kXSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiV2FybmluZzogd2luZG93Lmhpc3RvcnkuXCIgKyBtZXRob2QgKyBcIiBpcyBub3QgYXZhaWxhYmxlXCIpO1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAobWV0aG9kICE9PSAncHVzaFN0YXRlJyB8fCAoMCwgX3V0aWxzLmdldFVSTCkoKSAhPT0gYXMpIHtcbiAgICAgICAgICAgIHRoaXMuX3NoYWxsb3cgPSBvcHRpb25zLnNoYWxsb3c7XG4gICAgICAgICAgICB3aW5kb3cuaGlzdG9yeVttZXRob2RdKHtcbiAgICAgICAgICAgICAgICB1cmwsXG4gICAgICAgICAgICAgICAgYXMsXG4gICAgICAgICAgICAgICAgb3B0aW9ucyxcbiAgICAgICAgICAgICAgICBfX046IHRydWUsXG4gICAgICAgICAgICAgICAga2V5OiB0aGlzLl9rZXkgPSBtZXRob2QgIT09ICdwdXNoU3RhdGUnID8gdGhpcy5fa2V5IDogY3JlYXRlS2V5KClcbiAgICAgICAgICAgIH0sIC8vIE1vc3QgYnJvd3NlcnMgY3VycmVudGx5IGlnbm9yZXMgdGhpcyBwYXJhbWV0ZXIsIGFsdGhvdWdoIHRoZXkgbWF5IHVzZSBpdCBpbiB0aGUgZnV0dXJlLlxuICAgICAgICAgICAgLy8gUGFzc2luZyB0aGUgZW1wdHkgc3RyaW5nIGhlcmUgc2hvdWxkIGJlIHNhZmUgYWdhaW5zdCBmdXR1cmUgY2hhbmdlcyB0byB0aGUgbWV0aG9kLlxuICAgICAgICAgICAgLy8gaHR0cHM6Ly9kZXZlbG9wZXIubW96aWxsYS5vcmcvZG9jcy9XZWIvQVBJL0hpc3RvcnkvcmVwbGFjZVN0YXRlXG4gICAgICAgICAgICAnJywgYXMpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGFzeW5jIGhhbmRsZVJvdXRlSW5mb0Vycm9yKGVyciwgcGF0aG5hbWUsIHF1ZXJ5LCBhcywgcm91dGVQcm9wcywgbG9hZEVycm9yRmFpbCkge1xuICAgICAgICBpZiAoZXJyLmNhbmNlbGxlZCkge1xuICAgICAgICAgICAgLy8gYnViYmxlIHVwIGNhbmNlbGxhdGlvbiBlcnJvcnNcbiAgICAgICAgICAgIHRocm93IGVycjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoKDAsIF9yb3V0ZWxvYWRlci5pc0Fzc2V0RXJyb3IpKGVycikgfHwgbG9hZEVycm9yRmFpbCkge1xuICAgICAgICAgICAgUm91dGVyLmV2ZW50cy5lbWl0KCdyb3V0ZUNoYW5nZUVycm9yJywgZXJyLCBhcywgcm91dGVQcm9wcyk7XG4gICAgICAgICAgICAvLyBJZiB3ZSBjYW4ndCBsb2FkIHRoZSBwYWdlIGl0IGNvdWxkIGJlIG9uZSBvZiBmb2xsb3dpbmcgcmVhc29uc1xuICAgICAgICAgICAgLy8gIDEuIFBhZ2UgZG9lc24ndCBleGlzdHNcbiAgICAgICAgICAgIC8vICAyLiBQYWdlIGRvZXMgZXhpc3QgaW4gYSBkaWZmZXJlbnQgem9uZVxuICAgICAgICAgICAgLy8gIDMuIEludGVybmFsIGVycm9yIHdoaWxlIGxvYWRpbmcgdGhlIHBhZ2VcbiAgICAgICAgICAgIC8vIFNvLCBkb2luZyBhIGhhcmQgcmVsb2FkIGlzIHRoZSBwcm9wZXIgd2F5IHRvIGRlYWwgd2l0aCB0aGlzLlxuICAgICAgICAgICAgaGFuZGxlSGFyZE5hdmlnYXRpb24oe1xuICAgICAgICAgICAgICAgIHVybDogYXMsXG4gICAgICAgICAgICAgICAgcm91dGVyOiB0aGlzXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIC8vIENoYW5naW5nIHRoZSBVUkwgZG9lc24ndCBibG9jayBleGVjdXRpbmcgdGhlIGN1cnJlbnQgY29kZSBwYXRoLlxuICAgICAgICAgICAgLy8gU28gbGV0J3MgdGhyb3cgYSBjYW5jZWxsYXRpb24gZXJyb3Igc3RvcCB0aGUgcm91dGluZyBsb2dpYy5cbiAgICAgICAgICAgIHRocm93IGJ1aWxkQ2FuY2VsbGF0aW9uRXJyb3IoKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zb2xlLmVycm9yKGVycik7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBsZXQgcHJvcHM7XG4gICAgICAgICAgICBjb25zdCB7IHBhZ2U6IENvbXBvbmVudCwgc3R5bGVTaGVldHMgfSA9IGF3YWl0IHRoaXMuZmV0Y2hDb21wb25lbnQoJy9fZXJyb3InKTtcbiAgICAgICAgICAgIGNvbnN0IHJvdXRlSW5mbyA9IHtcbiAgICAgICAgICAgICAgICBwcm9wcyxcbiAgICAgICAgICAgICAgICBDb21wb25lbnQsXG4gICAgICAgICAgICAgICAgc3R5bGVTaGVldHMsXG4gICAgICAgICAgICAgICAgZXJyLFxuICAgICAgICAgICAgICAgIGVycm9yOiBlcnJcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBpZiAoIXJvdXRlSW5mby5wcm9wcykge1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIHJvdXRlSW5mby5wcm9wcyA9IGF3YWl0IHRoaXMuZ2V0SW5pdGlhbFByb3BzKENvbXBvbmVudCwge1xuICAgICAgICAgICAgICAgICAgICAgICAgZXJyLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGF0aG5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICBxdWVyeVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9IGNhdGNoIChnaXBFcnIpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gZXJyb3IgcGFnZSBgZ2V0SW5pdGlhbFByb3BzYDogJywgZ2lwRXJyKTtcbiAgICAgICAgICAgICAgICAgICAgcm91dGVJbmZvLnByb3BzID0ge307XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHJvdXRlSW5mbztcbiAgICAgICAgfSBjYXRjaCAocm91dGVJbmZvRXJyKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5oYW5kbGVSb3V0ZUluZm9FcnJvcigoMCwgX2lzZXJyb3IuZGVmYXVsdCkocm91dGVJbmZvRXJyKSA/IHJvdXRlSW5mb0VyciA6IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuZXcgRXJyb3Iocm91dGVJbmZvRXJyICsgJycpLCBcIl9fTkVYVF9FUlJPUl9DT0RFXCIsIHtcbiAgICAgICAgICAgICAgICB2YWx1ZTogXCJFMzk0XCIsXG4gICAgICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgICAgICAgICB9KSwgcGF0aG5hbWUsIHF1ZXJ5LCBhcywgcm91dGVQcm9wcywgdHJ1ZSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgYXN5bmMgZ2V0Um91dGVJbmZvKHBhcmFtKSB7XG4gICAgICAgIGxldCB7IHJvdXRlOiByZXF1ZXN0ZWRSb3V0ZSwgcGF0aG5hbWUsIHF1ZXJ5LCBhcywgcmVzb2x2ZWRBcywgcm91dGVQcm9wcywgbG9jYWxlLCBoYXNNaWRkbGV3YXJlLCBpc1ByZXZpZXcsIHVuc3RhYmxlX3NraXBDbGllbnRDYWNoZSwgaXNRdWVyeVVwZGF0aW5nLCBpc01pZGRsZXdhcmVSZXdyaXRlLCBpc05vdEZvdW5kIH0gPSBwYXJhbTtcbiAgICAgICAgLyoqXG4gICAgICogVGhpcyBgcm91dGVgIGJpbmRpbmcgY2FuIGNoYW5nZSBpZiB0aGVyZSdzIGEgcmV3cml0ZVxuICAgICAqIHNvIHdlIGtlZXAgYSByZWZlcmVuY2UgdG8gdGhlIG9yaWdpbmFsIHJlcXVlc3RlZCByb3V0ZVxuICAgICAqIHNvIHdlIGNhbiBzdG9yZSB0aGUgY2FjaGUgZm9yIGl0IGFuZCBhdm9pZCByZS1yZXF1ZXN0aW5nIGV2ZXJ5IHRpbWVcbiAgICAgKiBmb3Igc2hhbGxvdyByb3V0aW5nIHB1cnBvc2VzLlxuICAgICAqLyBsZXQgcm91dGUgPSByZXF1ZXN0ZWRSb3V0ZTtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIHZhciBfZGF0YV9lZmZlY3QsIF9kYXRhX2VmZmVjdDEsIF9kYXRhX2VmZmVjdDIsIF9kYXRhX3Jlc3BvbnNlO1xuICAgICAgICAgICAgbGV0IGV4aXN0aW5nSW5mbyA9IHRoaXMuY29tcG9uZW50c1tyb3V0ZV07XG4gICAgICAgICAgICBpZiAocm91dGVQcm9wcy5zaGFsbG93ICYmIGV4aXN0aW5nSW5mbyAmJiB0aGlzLnJvdXRlID09PSByb3V0ZSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBleGlzdGluZ0luZm87XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBoYW5kbGVDYW5jZWxsZWQgPSBnZXRDYW5jZWxsZWRIYW5kbGVyKHtcbiAgICAgICAgICAgICAgICByb3V0ZSxcbiAgICAgICAgICAgICAgICByb3V0ZXI6IHRoaXNcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgaWYgKGhhc01pZGRsZXdhcmUpIHtcbiAgICAgICAgICAgICAgICBleGlzdGluZ0luZm8gPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBsZXQgY2FjaGVkUm91dGVJbmZvID0gZXhpc3RpbmdJbmZvICYmICEoJ2luaXRpYWwnIGluIGV4aXN0aW5nSW5mbykgJiYgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdkZXZlbG9wbWVudCcgPyBleGlzdGluZ0luZm8gOiB1bmRlZmluZWQ7XG4gICAgICAgICAgICBjb25zdCBpc0JhY2tncm91bmQgPSBpc1F1ZXJ5VXBkYXRpbmc7XG4gICAgICAgICAgICBjb25zdCBmZXRjaE5leHREYXRhUGFyYW1zID0ge1xuICAgICAgICAgICAgICAgIGRhdGFIcmVmOiB0aGlzLnBhZ2VMb2FkZXIuZ2V0RGF0YUhyZWYoe1xuICAgICAgICAgICAgICAgICAgICBocmVmOiAoMCwgX2Zvcm1hdHVybC5mb3JtYXRXaXRoVmFsaWRhdGlvbikoe1xuICAgICAgICAgICAgICAgICAgICAgICAgcGF0aG5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICBxdWVyeVxuICAgICAgICAgICAgICAgICAgICB9KSxcbiAgICAgICAgICAgICAgICAgICAgc2tpcEludGVycG9sYXRpb246IHRydWUsXG4gICAgICAgICAgICAgICAgICAgIGFzUGF0aDogaXNOb3RGb3VuZCA/ICcvNDA0JyA6IHJlc29sdmVkQXMsXG4gICAgICAgICAgICAgICAgICAgIGxvY2FsZVxuICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgICAgIGhhc01pZGRsZXdhcmU6IHRydWUsXG4gICAgICAgICAgICAgICAgaXNTZXJ2ZXJSZW5kZXI6IHRoaXMuaXNTc3IsXG4gICAgICAgICAgICAgICAgcGFyc2VKU09OOiB0cnVlLFxuICAgICAgICAgICAgICAgIGluZmxpZ2h0Q2FjaGU6IGlzQmFja2dyb3VuZCA/IHRoaXMuc2JjIDogdGhpcy5zZGMsXG4gICAgICAgICAgICAgICAgcGVyc2lzdENhY2hlOiAhaXNQcmV2aWV3LFxuICAgICAgICAgICAgICAgIGlzUHJlZmV0Y2g6IGZhbHNlLFxuICAgICAgICAgICAgICAgIHVuc3RhYmxlX3NraXBDbGllbnRDYWNoZSxcbiAgICAgICAgICAgICAgICBpc0JhY2tncm91bmRcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBsZXQgZGF0YSA9IGlzUXVlcnlVcGRhdGluZyAmJiAhaXNNaWRkbGV3YXJlUmV3cml0ZSA/IG51bGwgOiBhd2FpdCB3aXRoTWlkZGxld2FyZUVmZmVjdHMoe1xuICAgICAgICAgICAgICAgIGZldGNoRGF0YTogKCk9PmZldGNoTmV4dERhdGEoZmV0Y2hOZXh0RGF0YVBhcmFtcyksXG4gICAgICAgICAgICAgICAgYXNQYXRoOiBpc05vdEZvdW5kID8gJy80MDQnIDogcmVzb2x2ZWRBcyxcbiAgICAgICAgICAgICAgICBsb2NhbGU6IGxvY2FsZSxcbiAgICAgICAgICAgICAgICByb3V0ZXI6IHRoaXNcbiAgICAgICAgICAgIH0pLmNhdGNoKChlcnIpPT57XG4gICAgICAgICAgICAgICAgLy8gd2UgZG9uJ3QgaGFyZCBlcnJvciBkdXJpbmcgcXVlcnkgdXBkYXRpbmdcbiAgICAgICAgICAgICAgICAvLyBhcyBpdCdzIHVuLW5lY2Vzc2FyeSBhbmQgZG9lc24ndCBuZWVkIHRvIGJlIGZhdGFsXG4gICAgICAgICAgICAgICAgLy8gdW5sZXNzIGl0IGlzIGEgZmFsbGJhY2sgcm91dGUgYW5kIHRoZSBwcm9wcyBjYW4ndFxuICAgICAgICAgICAgICAgIC8vIGJlIGxvYWRlZFxuICAgICAgICAgICAgICAgIGlmIChpc1F1ZXJ5VXBkYXRpbmcpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRocm93IGVycjtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgLy8gd2hlbiByZW5kZXJpbmcgZXJyb3Igcm91dGVzIHdlIGRvbid0IGFwcGx5IG1pZGRsZXdhcmVcbiAgICAgICAgICAgIC8vIGVmZmVjdHNcbiAgICAgICAgICAgIGlmIChkYXRhICYmIChwYXRobmFtZSA9PT0gJy9fZXJyb3InIHx8IHBhdGhuYW1lID09PSAnLzQwNCcpKSB7XG4gICAgICAgICAgICAgICAgZGF0YS5lZmZlY3QgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaXNRdWVyeVVwZGF0aW5nKSB7XG4gICAgICAgICAgICAgICAgaWYgKCFkYXRhKSB7XG4gICAgICAgICAgICAgICAgICAgIGRhdGEgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBqc29uOiBzZWxmLl9fTkVYVF9EQVRBX18ucHJvcHNcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBkYXRhLmpzb24gPSBzZWxmLl9fTkVYVF9EQVRBX18ucHJvcHM7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaGFuZGxlQ2FuY2VsbGVkKCk7XG4gICAgICAgICAgICBpZiAoKGRhdGEgPT0gbnVsbCA/IHZvaWQgMCA6IChfZGF0YV9lZmZlY3QgPSBkYXRhLmVmZmVjdCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9kYXRhX2VmZmVjdC50eXBlKSA9PT0gJ3JlZGlyZWN0LWludGVybmFsJyB8fCAoZGF0YSA9PSBudWxsID8gdm9pZCAwIDogKF9kYXRhX2VmZmVjdDEgPSBkYXRhLmVmZmVjdCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9kYXRhX2VmZmVjdDEudHlwZSkgPT09ICdyZWRpcmVjdC1leHRlcm5hbCcpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZGF0YS5lZmZlY3Q7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoKGRhdGEgPT0gbnVsbCA/IHZvaWQgMCA6IChfZGF0YV9lZmZlY3QyID0gZGF0YS5lZmZlY3QpID09IG51bGwgPyB2b2lkIDAgOiBfZGF0YV9lZmZlY3QyLnR5cGUpID09PSAncmV3cml0ZScpIHtcbiAgICAgICAgICAgICAgICBjb25zdCByZXNvbHZlZFJvdXRlID0gKDAsIF9yZW1vdmV0cmFpbGluZ3NsYXNoLnJlbW92ZVRyYWlsaW5nU2xhc2gpKGRhdGEuZWZmZWN0LnJlc29sdmVkSHJlZik7XG4gICAgICAgICAgICAgICAgY29uc3QgcGFnZXMgPSBhd2FpdCB0aGlzLnBhZ2VMb2FkZXIuZ2V0UGFnZUxpc3QoKTtcbiAgICAgICAgICAgICAgICAvLyBkdXJpbmcgcXVlcnkgdXBkYXRpbmcgdGhlIHBhZ2UgbXVzdCBtYXRjaCBhbHRob3VnaCBkdXJpbmdcbiAgICAgICAgICAgICAgICAvLyBjbGllbnQtdHJhbnNpdGlvbiBhIHJlZGlyZWN0IHRoYXQgZG9lc24ndCBtYXRjaCBhIHBhZ2VcbiAgICAgICAgICAgICAgICAvLyBjYW4gYmUgcmV0dXJuZWQgYW5kIHRoaXMgc2hvdWxkIHRyaWdnZXIgYSBoYXJkIG5hdmlnYXRpb25cbiAgICAgICAgICAgICAgICAvLyB3aGljaCBpcyB2YWxpZCBmb3IgaW5jcmVtZW50YWwgbWlncmF0aW9uXG4gICAgICAgICAgICAgICAgaWYgKCFpc1F1ZXJ5VXBkYXRpbmcgfHwgcGFnZXMuaW5jbHVkZXMocmVzb2x2ZWRSb3V0ZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgcm91dGUgPSByZXNvbHZlZFJvdXRlO1xuICAgICAgICAgICAgICAgICAgICBwYXRobmFtZSA9IGRhdGEuZWZmZWN0LnJlc29sdmVkSHJlZjtcbiAgICAgICAgICAgICAgICAgICAgcXVlcnkgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5xdWVyeSxcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLmRhdGEuZWZmZWN0LnBhcnNlZEFzLnF1ZXJ5XG4gICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgIHJlc29sdmVkQXMgPSAoMCwgX3JlbW92ZWJhc2VwYXRoLnJlbW92ZUJhc2VQYXRoKSgoMCwgX25vcm1hbGl6ZWxvY2FsZXBhdGgubm9ybWFsaXplTG9jYWxlUGF0aCkoZGF0YS5lZmZlY3QucGFyc2VkQXMucGF0aG5hbWUsIHRoaXMubG9jYWxlcykucGF0aG5hbWUpO1xuICAgICAgICAgICAgICAgICAgICAvLyBDaGVjayBhZ2FpbiB0aGUgY2FjaGUgd2l0aCB0aGUgbmV3IGRlc3RpbmF0aW9uLlxuICAgICAgICAgICAgICAgICAgICBleGlzdGluZ0luZm8gPSB0aGlzLmNvbXBvbmVudHNbcm91dGVdO1xuICAgICAgICAgICAgICAgICAgICBpZiAocm91dGVQcm9wcy5zaGFsbG93ICYmIGV4aXN0aW5nSW5mbyAmJiB0aGlzLnJvdXRlID09PSByb3V0ZSAmJiAhaGFzTWlkZGxld2FyZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gSWYgd2UgaGF2ZSBhIG1hdGNoIHdpdGggdGhlIGN1cnJlbnQgcm91dGUgZHVlIHRvIHJld3JpdGUsXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyB3ZSBjYW4gY29weSB0aGUgZXhpc3RpbmcgaW5mb3JtYXRpb24gdG8gdGhlIHJld3JpdHRlbiBvbmUuXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBUaGVuLCB3ZSByZXR1cm4gdGhlIGluZm9ybWF0aW9uIGFsb25nIHdpdGggdGhlIG1hdGNoZWQgcm91dGUuXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLmV4aXN0aW5nSW5mbyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3V0ZVxuICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICgoMCwgX2lzYXBpcm91dGUuaXNBUElSb3V0ZSkocm91dGUpKSB7XG4gICAgICAgICAgICAgICAgaGFuZGxlSGFyZE5hdmlnYXRpb24oe1xuICAgICAgICAgICAgICAgICAgICB1cmw6IGFzLFxuICAgICAgICAgICAgICAgICAgICByb3V0ZXI6IHRoaXNcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKCk9Pnt9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHJvdXRlSW5mbyA9IGNhY2hlZFJvdXRlSW5mbyB8fCBhd2FpdCB0aGlzLmZldGNoQ29tcG9uZW50KHJvdXRlKS50aGVuKChyZXMpPT4oe1xuICAgICAgICAgICAgICAgICAgICBDb21wb25lbnQ6IHJlcy5wYWdlLFxuICAgICAgICAgICAgICAgICAgICBzdHlsZVNoZWV0czogcmVzLnN0eWxlU2hlZXRzLFxuICAgICAgICAgICAgICAgICAgICBfX05fU1NHOiByZXMubW9kLl9fTl9TU0csXG4gICAgICAgICAgICAgICAgICAgIF9fTl9TU1A6IHJlcy5tb2QuX19OX1NTUFxuICAgICAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgeyBpc1ZhbGlkRWxlbWVudFR5cGUgfSA9IHJlcXVpcmUoJ25leHQvZGlzdC9jb21waWxlZC9yZWFjdC1pcycpO1xuICAgICAgICAgICAgICAgIGlmICghaXNWYWxpZEVsZW1lbnRUeXBlKHJvdXRlSW5mby5Db21wb25lbnQpKSB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuZXcgRXJyb3IoJ1RoZSBkZWZhdWx0IGV4cG9ydCBpcyBub3QgYSBSZWFjdCBDb21wb25lbnQgaW4gcGFnZTogXCInICsgcGF0aG5hbWUgKyAnXCInKSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogXCJFMjg2XCIsXG4gICAgICAgICAgICAgICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCB3YXNCYWlsZWRQcmVmZXRjaCA9IGRhdGEgPT0gbnVsbCA/IHZvaWQgMCA6IChfZGF0YV9yZXNwb25zZSA9IGRhdGEucmVzcG9uc2UpID09IG51bGwgPyB2b2lkIDAgOiBfZGF0YV9yZXNwb25zZS5oZWFkZXJzLmdldCgneC1taWRkbGV3YXJlLXNraXAnKTtcbiAgICAgICAgICAgIGNvbnN0IHNob3VsZEZldGNoRGF0YSA9IHJvdXRlSW5mby5fX05fU1NHIHx8IHJvdXRlSW5mby5fX05fU1NQO1xuICAgICAgICAgICAgLy8gRm9yIG5vbi1TU0cgcHJlZmV0Y2hlcyB0aGF0IGJhaWxlZCBiZWZvcmUgc2VuZGluZyBkYXRhXG4gICAgICAgICAgICAvLyB3ZSBjbGVhciB0aGUgY2FjaGUgdG8gZmV0Y2ggZnVsbCByZXNwb25zZVxuICAgICAgICAgICAgaWYgKHdhc0JhaWxlZFByZWZldGNoICYmIChkYXRhID09IG51bGwgPyB2b2lkIDAgOiBkYXRhLmRhdGFIcmVmKSkge1xuICAgICAgICAgICAgICAgIGRlbGV0ZSB0aGlzLnNkY1tkYXRhLmRhdGFIcmVmXTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHsgcHJvcHMsIGNhY2hlS2V5IH0gPSBhd2FpdCB0aGlzLl9nZXREYXRhKGFzeW5jICgpPT57XG4gICAgICAgICAgICAgICAgaWYgKHNob3VsZEZldGNoRGF0YSkge1xuICAgICAgICAgICAgICAgICAgICBpZiAoKGRhdGEgPT0gbnVsbCA/IHZvaWQgMCA6IGRhdGEuanNvbikgJiYgIXdhc0JhaWxlZFByZWZldGNoKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhY2hlS2V5OiBkYXRhLmNhY2hlS2V5LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByb3BzOiBkYXRhLmpzb25cbiAgICAgICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZGF0YUhyZWYgPSAoZGF0YSA9PSBudWxsID8gdm9pZCAwIDogZGF0YS5kYXRhSHJlZikgPyBkYXRhLmRhdGFIcmVmIDogdGhpcy5wYWdlTG9hZGVyLmdldERhdGFIcmVmKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY6ICgwLCBfZm9ybWF0dXJsLmZvcm1hdFdpdGhWYWxpZGF0aW9uKSh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGF0aG5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcXVlcnlcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgICAgICAgICAgICAgYXNQYXRoOiByZXNvbHZlZEFzLFxuICAgICAgICAgICAgICAgICAgICAgICAgbG9jYWxlXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBmZXRjaGVkID0gYXdhaXQgZmV0Y2hOZXh0RGF0YSh7XG4gICAgICAgICAgICAgICAgICAgICAgICBkYXRhSHJlZixcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzU2VydmVyUmVuZGVyOiB0aGlzLmlzU3NyLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGFyc2VKU09OOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgaW5mbGlnaHRDYWNoZTogd2FzQmFpbGVkUHJlZmV0Y2ggPyB7fSA6IHRoaXMuc2RjLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGVyc2lzdENhY2hlOiAhaXNQcmV2aWV3LFxuICAgICAgICAgICAgICAgICAgICAgICAgaXNQcmVmZXRjaDogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgICAgICB1bnN0YWJsZV9za2lwQ2xpZW50Q2FjaGVcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjYWNoZUtleTogZmV0Y2hlZC5jYWNoZUtleSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHByb3BzOiBmZXRjaGVkLmpzb24gfHwge31cbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgaGVhZGVyczoge30sXG4gICAgICAgICAgICAgICAgICAgIHByb3BzOiBhd2FpdCB0aGlzLmdldEluaXRpYWxQcm9wcyhyb3V0ZUluZm8uQ29tcG9uZW50LCAvLyB3ZSBwcm92aWRlIEFwcFRyZWUgbGF0ZXIgc28gdGhpcyBuZWVkcyB0byBiZSBgYW55YFxuICAgICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgICAgICBwYXRobmFtZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHF1ZXJ5LFxuICAgICAgICAgICAgICAgICAgICAgICAgYXNQYXRoOiBhcyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGxvY2FsZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGxvY2FsZXM6IHRoaXMubG9jYWxlcyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGRlZmF1bHRMb2NhbGU6IHRoaXMuZGVmYXVsdExvY2FsZVxuICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIC8vIE9ubHkgYnVzdCB0aGUgZGF0YSBjYWNoZSBmb3IgU1NQIHJvdXRlcyBhbHRob3VnaFxuICAgICAgICAgICAgLy8gbWlkZGxld2FyZSBjYW4gc2tpcCBjYWNoZSBwZXIgcmVxdWVzdCB3aXRoXG4gICAgICAgICAgICAvLyB4LW1pZGRsZXdhcmUtY2FjaGU6IG5vLWNhY2hlIGFzIHdlbGxcbiAgICAgICAgICAgIGlmIChyb3V0ZUluZm8uX19OX1NTUCAmJiBmZXRjaE5leHREYXRhUGFyYW1zLmRhdGFIcmVmICYmIGNhY2hlS2V5KSB7XG4gICAgICAgICAgICAgICAgZGVsZXRlIHRoaXMuc2RjW2NhY2hlS2V5XTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIHdlIGtpY2sgb2ZmIGEgSEVBRCByZXF1ZXN0IGluIHRoZSBiYWNrZ3JvdW5kXG4gICAgICAgICAgICAvLyB3aGVuIGEgbm9uLXByZWZldGNoIHJlcXVlc3QgaXMgbWFkZSB0byBzaWduYWwgcmV2YWxpZGF0aW9uXG4gICAgICAgICAgICBpZiAoIXRoaXMuaXNQcmV2aWV3ICYmIHJvdXRlSW5mby5fX05fU1NHICYmIHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAnZGV2ZWxvcG1lbnQnICYmICFpc1F1ZXJ5VXBkYXRpbmcpIHtcbiAgICAgICAgICAgICAgICBmZXRjaE5leHREYXRhKE9iamVjdC5hc3NpZ24oe30sIGZldGNoTmV4dERhdGFQYXJhbXMsIHtcbiAgICAgICAgICAgICAgICAgICAgaXNCYWNrZ3JvdW5kOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICBwZXJzaXN0Q2FjaGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICBpbmZsaWdodENhY2hlOiB0aGlzLnNiY1xuICAgICAgICAgICAgICAgIH0pKS5jYXRjaCgoKT0+e30pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcHJvcHMucGFnZVByb3BzID0gT2JqZWN0LmFzc2lnbih7fSwgcHJvcHMucGFnZVByb3BzKTtcbiAgICAgICAgICAgIHJvdXRlSW5mby5wcm9wcyA9IHByb3BzO1xuICAgICAgICAgICAgcm91dGVJbmZvLnJvdXRlID0gcm91dGU7XG4gICAgICAgICAgICByb3V0ZUluZm8ucXVlcnkgPSBxdWVyeTtcbiAgICAgICAgICAgIHJvdXRlSW5mby5yZXNvbHZlZEFzID0gcmVzb2x2ZWRBcztcbiAgICAgICAgICAgIHRoaXMuY29tcG9uZW50c1tyb3V0ZV0gPSByb3V0ZUluZm87XG4gICAgICAgICAgICByZXR1cm4gcm91dGVJbmZvO1xuICAgICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmhhbmRsZVJvdXRlSW5mb0Vycm9yKCgwLCBfaXNlcnJvci5nZXRQcm9wZXJFcnJvcikoZXJyKSwgcGF0aG5hbWUsIHF1ZXJ5LCBhcywgcm91dGVQcm9wcyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc2V0KHN0YXRlLCBkYXRhLCByZXNldFNjcm9sbCkge1xuICAgICAgICB0aGlzLnN0YXRlID0gc3RhdGU7XG4gICAgICAgIHJldHVybiB0aGlzLnN1YihkYXRhLCB0aGlzLmNvbXBvbmVudHNbJy9fYXBwJ10uQ29tcG9uZW50LCByZXNldFNjcm9sbCk7XG4gICAgfVxuICAgIC8qKlxuICAgKiBDYWxsYmFjayB0byBleGVjdXRlIGJlZm9yZSByZXBsYWNpbmcgcm91dGVyIHN0YXRlXG4gICAqIEBwYXJhbSBjYiBjYWxsYmFjayB0byBiZSBleGVjdXRlZFxuICAgKi8gYmVmb3JlUG9wU3RhdGUoY2IpIHtcbiAgICAgICAgdGhpcy5fYnBzID0gY2I7XG4gICAgfVxuICAgIG9ubHlBSGFzaENoYW5nZShhcykge1xuICAgICAgICBpZiAoIXRoaXMuYXNQYXRoKSByZXR1cm4gZmFsc2U7XG4gICAgICAgIGNvbnN0IFtvbGRVcmxOb0hhc2gsIG9sZEhhc2hdID0gdGhpcy5hc1BhdGguc3BsaXQoJyMnLCAyKTtcbiAgICAgICAgY29uc3QgW25ld1VybE5vSGFzaCwgbmV3SGFzaF0gPSBhcy5zcGxpdCgnIycsIDIpO1xuICAgICAgICAvLyBNYWtlcyBzdXJlIHdlIHNjcm9sbCB0byB0aGUgcHJvdmlkZWQgaGFzaCBpZiB0aGUgdXJsL2hhc2ggYXJlIHRoZSBzYW1lXG4gICAgICAgIGlmIChuZXdIYXNoICYmIG9sZFVybE5vSGFzaCA9PT0gbmV3VXJsTm9IYXNoICYmIG9sZEhhc2ggPT09IG5ld0hhc2gpIHtcbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIC8vIElmIHRoZSB1cmxzIGFyZSBjaGFuZ2UsIHRoZXJlJ3MgbW9yZSB0aGFuIGEgaGFzaCBjaGFuZ2VcbiAgICAgICAgaWYgKG9sZFVybE5vSGFzaCAhPT0gbmV3VXJsTm9IYXNoKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgLy8gSWYgdGhlIGhhc2ggaGFzIGNoYW5nZWQsIHRoZW4gaXQncyBhIGhhc2ggb25seSBjaGFuZ2UuXG4gICAgICAgIC8vIFRoaXMgY2hlY2sgaXMgbmVjZXNzYXJ5IHRvIGhhbmRsZSBib3RoIHRoZSBlbnRlciBhbmRcbiAgICAgICAgLy8gbGVhdmUgaGFzaCA9PT0gJycgY2FzZXMuIFRoZSBpZGVudGl0eSBjYXNlIGZhbGxzIHRocm91Z2hcbiAgICAgICAgLy8gYW5kIGlzIHRyZWF0ZWQgYXMgYSBuZXh0IHJlbG9hZC5cbiAgICAgICAgcmV0dXJuIG9sZEhhc2ggIT09IG5ld0hhc2g7XG4gICAgfVxuICAgIHNjcm9sbFRvSGFzaChhcykge1xuICAgICAgICBjb25zdCBbLCBoYXNoID0gJyddID0gYXMuc3BsaXQoJyMnLCAyKTtcbiAgICAgICAgKDAsIF9oYW5kbGVzbW9vdGhzY3JvbGwuaGFuZGxlU21vb3RoU2Nyb2xsKSgoKT0+e1xuICAgICAgICAgICAgLy8gU2Nyb2xsIHRvIHRvcCBpZiB0aGUgaGFzaCBpcyBqdXN0IGAjYCB3aXRoIG5vIHZhbHVlIG9yIGAjdG9wYFxuICAgICAgICAgICAgLy8gVG8gbWlycm9yIGJyb3dzZXJzXG4gICAgICAgICAgICBpZiAoaGFzaCA9PT0gJycgfHwgaGFzaCA9PT0gJ3RvcCcpIHtcbiAgICAgICAgICAgICAgICB3aW5kb3cuc2Nyb2xsVG8oMCwgMCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gRGVjb2RlIGhhc2ggdG8gbWFrZSBub24tbGF0aW4gYW5jaG9yIHdvcmtzLlxuICAgICAgICAgICAgY29uc3QgcmF3SGFzaCA9IGRlY29kZVVSSUNvbXBvbmVudChoYXNoKTtcbiAgICAgICAgICAgIC8vIEZpcnN0IHdlIGNoZWNrIGlmIHRoZSBlbGVtZW50IGJ5IGlkIGlzIGZvdW5kXG4gICAgICAgICAgICBjb25zdCBpZEVsID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQocmF3SGFzaCk7XG4gICAgICAgICAgICBpZiAoaWRFbCkge1xuICAgICAgICAgICAgICAgIGlkRWwuc2Nyb2xsSW50b1ZpZXcoKTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBJZiB0aGVyZSdzIG5vIGVsZW1lbnQgd2l0aCB0aGUgaWQsIHdlIGNoZWNrIHRoZSBgbmFtZWAgcHJvcGVydHlcbiAgICAgICAgICAgIC8vIFRvIG1pcnJvciBicm93c2Vyc1xuICAgICAgICAgICAgY29uc3QgbmFtZUVsID0gZG9jdW1lbnQuZ2V0RWxlbWVudHNCeU5hbWUocmF3SGFzaClbMF07XG4gICAgICAgICAgICBpZiAobmFtZUVsKSB7XG4gICAgICAgICAgICAgICAgbmFtZUVsLnNjcm9sbEludG9WaWV3KCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sIHtcbiAgICAgICAgICAgIG9ubHlIYXNoQ2hhbmdlOiB0aGlzLm9ubHlBSGFzaENoYW5nZShhcylcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHVybElzTmV3KGFzUGF0aCkge1xuICAgICAgICByZXR1cm4gdGhpcy5hc1BhdGggIT09IGFzUGF0aDtcbiAgICB9XG4gICAgLyoqXG4gICAqIFByZWZldGNoIHBhZ2UgY29kZSwgeW91IG1heSB3YWl0IGZvciB0aGUgZGF0YSBkdXJpbmcgcGFnZSByZW5kZXJpbmcuXG4gICAqIFRoaXMgZmVhdHVyZSBvbmx5IHdvcmtzIGluIHByb2R1Y3Rpb24hXG4gICAqIEBwYXJhbSB1cmwgdGhlIGhyZWYgb2YgcHJlZmV0Y2hlZCBwYWdlXG4gICAqIEBwYXJhbSBhc1BhdGggdGhlIGFzIHBhdGggb2YgdGhlIHByZWZldGNoZWQgcGFnZVxuICAgKi8gYXN5bmMgcHJlZmV0Y2godXJsLCBhc1BhdGgsIG9wdGlvbnMpIHtcbiAgICAgICAgaWYgKGFzUGF0aCA9PT0gdm9pZCAwKSBhc1BhdGggPSB1cmw7XG4gICAgICAgIGlmIChvcHRpb25zID09PSB2b2lkIDApIG9wdGlvbnMgPSB7fTtcbiAgICAgICAgLy8gUHJlZmV0Y2ggaXMgbm90IHN1cHBvcnRlZCBpbiBkZXZlbG9wbWVudCBtb2RlIGJlY2F1c2UgaXQgd291bGQgdHJpZ2dlciBvbi1kZW1hbmQtZW50cmllc1xuICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiAoMCwgX2lzYm90LmlzQm90KSh3aW5kb3cubmF2aWdhdG9yLnVzZXJBZ2VudCkpIHtcbiAgICAgICAgICAgIC8vIE5vIHByZWZldGNoZXMgZm9yIGJvdHMgdGhhdCByZW5kZXIgdGhlIGxpbmsgc2luY2UgdGhleSBhcmUgdHlwaWNhbGx5IG5hdmlnYXRpbmdcbiAgICAgICAgICAgIC8vIGxpbmtzIHZpYSB0aGUgZXF1aXZhbGVudCBvZiBhIGhhcmQgbmF2aWdhdGlvbiBhbmQgaGVuY2UgbmV2ZXIgdXRpbGl6ZSB0aGVzZVxuICAgICAgICAgICAgLy8gcHJlZmV0Y2hlcy5cbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBsZXQgcGFyc2VkID0gKDAsIF9wYXJzZXJlbGF0aXZldXJsLnBhcnNlUmVsYXRpdmVVcmwpKHVybCk7XG4gICAgICAgIGNvbnN0IHVybFBhdGhuYW1lID0gcGFyc2VkLnBhdGhuYW1lO1xuICAgICAgICBsZXQgeyBwYXRobmFtZSwgcXVlcnkgfSA9IHBhcnNlZDtcbiAgICAgICAgY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IHBhdGhuYW1lO1xuICAgICAgICBpZiAocHJvY2Vzcy5lbnYuX19ORVhUX0kxOE5fU1VQUE9SVCkge1xuICAgICAgICAgICAgaWYgKG9wdGlvbnMubG9jYWxlID09PSBmYWxzZSkge1xuICAgICAgICAgICAgICAgIHBhdGhuYW1lID0gKDAsIF9ub3JtYWxpemVsb2NhbGVwYXRoLm5vcm1hbGl6ZUxvY2FsZVBhdGgpKHBhdGhuYW1lLCB0aGlzLmxvY2FsZXMpLnBhdGhuYW1lO1xuICAgICAgICAgICAgICAgIHBhcnNlZC5wYXRobmFtZSA9IHBhdGhuYW1lO1xuICAgICAgICAgICAgICAgIHVybCA9ICgwLCBfZm9ybWF0dXJsLmZvcm1hdFdpdGhWYWxpZGF0aW9uKShwYXJzZWQpO1xuICAgICAgICAgICAgICAgIGxldCBwYXJzZWRBcyA9ICgwLCBfcGFyc2VyZWxhdGl2ZXVybC5wYXJzZVJlbGF0aXZlVXJsKShhc1BhdGgpO1xuICAgICAgICAgICAgICAgIGNvbnN0IGxvY2FsZVBhdGhSZXN1bHQgPSAoMCwgX25vcm1hbGl6ZWxvY2FsZXBhdGgubm9ybWFsaXplTG9jYWxlUGF0aCkocGFyc2VkQXMucGF0aG5hbWUsIHRoaXMubG9jYWxlcyk7XG4gICAgICAgICAgICAgICAgcGFyc2VkQXMucGF0aG5hbWUgPSBsb2NhbGVQYXRoUmVzdWx0LnBhdGhuYW1lO1xuICAgICAgICAgICAgICAgIG9wdGlvbnMubG9jYWxlID0gbG9jYWxlUGF0aFJlc3VsdC5kZXRlY3RlZExvY2FsZSB8fCB0aGlzLmRlZmF1bHRMb2NhbGU7XG4gICAgICAgICAgICAgICAgYXNQYXRoID0gKDAsIF9mb3JtYXR1cmwuZm9ybWF0V2l0aFZhbGlkYXRpb24pKHBhcnNlZEFzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBjb25zdCBwYWdlcyA9IGF3YWl0IHRoaXMucGFnZUxvYWRlci5nZXRQYWdlTGlzdCgpO1xuICAgICAgICBsZXQgcmVzb2x2ZWRBcyA9IGFzUGF0aDtcbiAgICAgICAgY29uc3QgbG9jYWxlID0gdHlwZW9mIG9wdGlvbnMubG9jYWxlICE9PSAndW5kZWZpbmVkJyA/IG9wdGlvbnMubG9jYWxlIHx8IHVuZGVmaW5lZCA6IHRoaXMubG9jYWxlO1xuICAgICAgICBjb25zdCBpc01pZGRsZXdhcmVNYXRjaCA9IGF3YWl0IG1hdGNoZXNNaWRkbGV3YXJlKHtcbiAgICAgICAgICAgIGFzUGF0aDogYXNQYXRoLFxuICAgICAgICAgICAgbG9jYWxlOiBsb2NhbGUsXG4gICAgICAgICAgICByb3V0ZXI6IHRoaXNcbiAgICAgICAgfSk7XG4gICAgICAgIGlmIChwcm9jZXNzLmVudi5fX05FWFRfSEFTX1JFV1JJVEVTICYmIGFzUGF0aC5zdGFydHNXaXRoKCcvJykpIHtcbiAgICAgICAgICAgIGxldCByZXdyaXRlcztcbiAgICAgICAgICAgICh7IF9fcmV3cml0ZXM6IHJld3JpdGVzIH0gPSBhd2FpdCAoMCwgX3JvdXRlbG9hZGVyLmdldENsaWVudEJ1aWxkTWFuaWZlc3QpKCkpO1xuICAgICAgICAgICAgY29uc3QgcmV3cml0ZXNSZXN1bHQgPSAoMCwgX3Jlc29sdmVyZXdyaXRlcy5kZWZhdWx0KSgoMCwgX2FkZGJhc2VwYXRoLmFkZEJhc2VQYXRoKSgoMCwgX2FkZGxvY2FsZS5hZGRMb2NhbGUpKGFzUGF0aCwgdGhpcy5sb2NhbGUpLCB0cnVlKSwgcGFnZXMsIHJld3JpdGVzLCBwYXJzZWQucXVlcnksIChwKT0+cmVzb2x2ZUR5bmFtaWNSb3V0ZShwLCBwYWdlcyksIHRoaXMubG9jYWxlcyk7XG4gICAgICAgICAgICBpZiAocmV3cml0ZXNSZXN1bHQuZXh0ZXJuYWxEZXN0KSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFpc01pZGRsZXdhcmVNYXRjaCkge1xuICAgICAgICAgICAgICAgIHJlc29sdmVkQXMgPSAoMCwgX3JlbW92ZWxvY2FsZS5yZW1vdmVMb2NhbGUpKCgwLCBfcmVtb3ZlYmFzZXBhdGgucmVtb3ZlQmFzZVBhdGgpKHJld3JpdGVzUmVzdWx0LmFzUGF0aCksIHRoaXMubG9jYWxlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChyZXdyaXRlc1Jlc3VsdC5tYXRjaGVkUGFnZSAmJiByZXdyaXRlc1Jlc3VsdC5yZXNvbHZlZEhyZWYpIHtcbiAgICAgICAgICAgICAgICAvLyBpZiB0aGlzIGRpcmVjdGx5IG1hdGNoZXMgYSBwYWdlIHdlIG5lZWQgdG8gdXBkYXRlIHRoZSBocmVmIHRvXG4gICAgICAgICAgICAgICAgLy8gYWxsb3cgdGhlIGNvcnJlY3QgcGFnZSBjaHVuayB0byBiZSBsb2FkZWRcbiAgICAgICAgICAgICAgICBwYXRobmFtZSA9IHJld3JpdGVzUmVzdWx0LnJlc29sdmVkSHJlZjtcbiAgICAgICAgICAgICAgICBwYXJzZWQucGF0aG5hbWUgPSBwYXRobmFtZTtcbiAgICAgICAgICAgICAgICBpZiAoIWlzTWlkZGxld2FyZU1hdGNoKSB7XG4gICAgICAgICAgICAgICAgICAgIHVybCA9ICgwLCBfZm9ybWF0dXJsLmZvcm1hdFdpdGhWYWxpZGF0aW9uKShwYXJzZWQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBwYXJzZWQucGF0aG5hbWUgPSByZXNvbHZlRHluYW1pY1JvdXRlKHBhcnNlZC5wYXRobmFtZSwgcGFnZXMpO1xuICAgICAgICBpZiAoKDAsIF9pc2R5bmFtaWMuaXNEeW5hbWljUm91dGUpKHBhcnNlZC5wYXRobmFtZSkpIHtcbiAgICAgICAgICAgIHBhdGhuYW1lID0gcGFyc2VkLnBhdGhuYW1lO1xuICAgICAgICAgICAgcGFyc2VkLnBhdGhuYW1lID0gcGF0aG5hbWU7XG4gICAgICAgICAgICBPYmplY3QuYXNzaWduKHF1ZXJ5LCAoMCwgX3JvdXRlbWF0Y2hlci5nZXRSb3V0ZU1hdGNoZXIpKCgwLCBfcm91dGVyZWdleC5nZXRSb3V0ZVJlZ2V4KShwYXJzZWQucGF0aG5hbWUpKSgoMCwgX3BhcnNlcGF0aC5wYXJzZVBhdGgpKGFzUGF0aCkucGF0aG5hbWUpIHx8IHt9KTtcbiAgICAgICAgICAgIGlmICghaXNNaWRkbGV3YXJlTWF0Y2gpIHtcbiAgICAgICAgICAgICAgICB1cmwgPSAoMCwgX2Zvcm1hdHVybC5mb3JtYXRXaXRoVmFsaWRhdGlvbikocGFyc2VkKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBjb25zdCBkYXRhID0gcHJvY2Vzcy5lbnYuX19ORVhUX01JRERMRVdBUkVfUFJFRkVUQ0ggPT09ICdzdHJpY3QnID8gbnVsbCA6IGF3YWl0IHdpdGhNaWRkbGV3YXJlRWZmZWN0cyh7XG4gICAgICAgICAgICBmZXRjaERhdGE6ICgpPT5mZXRjaE5leHREYXRhKHtcbiAgICAgICAgICAgICAgICAgICAgZGF0YUhyZWY6IHRoaXMucGFnZUxvYWRlci5nZXREYXRhSHJlZih7XG4gICAgICAgICAgICAgICAgICAgICAgICBocmVmOiAoMCwgX2Zvcm1hdHVybC5mb3JtYXRXaXRoVmFsaWRhdGlvbikoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhdGhuYW1lOiBvcmlnaW5hbFBhdGhuYW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHF1ZXJ5XG4gICAgICAgICAgICAgICAgICAgICAgICB9KSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHNraXBJbnRlcnBvbGF0aW9uOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgYXNQYXRoOiByZXNvbHZlZEFzLFxuICAgICAgICAgICAgICAgICAgICAgICAgbG9jYWxlXG4gICAgICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgICAgICAgICBoYXNNaWRkbGV3YXJlOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICBpc1NlcnZlclJlbmRlcjogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIHBhcnNlSlNPTjogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgICAgaW5mbGlnaHRDYWNoZTogdGhpcy5zZGMsXG4gICAgICAgICAgICAgICAgICAgIHBlcnNpc3RDYWNoZTogIXRoaXMuaXNQcmV2aWV3LFxuICAgICAgICAgICAgICAgICAgICBpc1ByZWZldGNoOiB0cnVlXG4gICAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICBhc1BhdGg6IGFzUGF0aCxcbiAgICAgICAgICAgIGxvY2FsZTogbG9jYWxlLFxuICAgICAgICAgICAgcm91dGVyOiB0aGlzXG4gICAgICAgIH0pO1xuICAgICAgICAvKipcbiAgICAgKiBJZiB0aGVyZSB3YXMgYSByZXdyaXRlIHdlIGFwcGx5IHRoZSBlZmZlY3RzIG9mIHRoZSByZXdyaXRlIG9uIHRoZVxuICAgICAqIGN1cnJlbnQgcGFyYW1ldGVycyBmb3IgdGhlIHByZWZldGNoLlxuICAgICAqLyBpZiAoKGRhdGEgPT0gbnVsbCA/IHZvaWQgMCA6IGRhdGEuZWZmZWN0LnR5cGUpID09PSAncmV3cml0ZScpIHtcbiAgICAgICAgICAgIHBhcnNlZC5wYXRobmFtZSA9IGRhdGEuZWZmZWN0LnJlc29sdmVkSHJlZjtcbiAgICAgICAgICAgIHBhdGhuYW1lID0gZGF0YS5lZmZlY3QucmVzb2x2ZWRIcmVmO1xuICAgICAgICAgICAgcXVlcnkgPSB7XG4gICAgICAgICAgICAgICAgLi4ucXVlcnksXG4gICAgICAgICAgICAgICAgLi4uZGF0YS5lZmZlY3QucGFyc2VkQXMucXVlcnlcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICByZXNvbHZlZEFzID0gZGF0YS5lZmZlY3QucGFyc2VkQXMucGF0aG5hbWU7XG4gICAgICAgICAgICB1cmwgPSAoMCwgX2Zvcm1hdHVybC5mb3JtYXRXaXRoVmFsaWRhdGlvbikocGFyc2VkKTtcbiAgICAgICAgfVxuICAgICAgICAvKipcbiAgICAgKiBJZiB0aGVyZSBpcyBhIHJlZGlyZWN0IHRvIGFuIGV4dGVybmFsIGRlc3RpbmF0aW9uIHRoZW4gd2UgZG9uJ3QgaGF2ZVxuICAgICAqIHRvIHByZWZldGNoIGNvbnRlbnQgYXMgaXQgd2lsbCBiZSB1bnVzZWQuXG4gICAgICovIGlmICgoZGF0YSA9PSBudWxsID8gdm9pZCAwIDogZGF0YS5lZmZlY3QudHlwZSkgPT09ICdyZWRpcmVjdC1leHRlcm5hbCcpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCByb3V0ZSA9ICgwLCBfcmVtb3ZldHJhaWxpbmdzbGFzaC5yZW1vdmVUcmFpbGluZ1NsYXNoKShwYXRobmFtZSk7XG4gICAgICAgIGlmIChhd2FpdCB0aGlzLl9iZmwoYXNQYXRoLCByZXNvbHZlZEFzLCBvcHRpb25zLmxvY2FsZSwgdHJ1ZSkpIHtcbiAgICAgICAgICAgIHRoaXMuY29tcG9uZW50c1t1cmxQYXRobmFtZV0gPSB7XG4gICAgICAgICAgICAgICAgX19hcHBSb3V0ZXI6IHRydWVcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICAgICAgdGhpcy5wYWdlTG9hZGVyLl9pc1NzZyhyb3V0ZSkudGhlbigoaXNTc2cpPT57XG4gICAgICAgICAgICAgICAgcmV0dXJuIGlzU3NnID8gZmV0Y2hOZXh0RGF0YSh7XG4gICAgICAgICAgICAgICAgICAgIGRhdGFIcmVmOiAoZGF0YSA9PSBudWxsID8gdm9pZCAwIDogZGF0YS5qc29uKSA/IGRhdGEgPT0gbnVsbCA/IHZvaWQgMCA6IGRhdGEuZGF0YUhyZWYgOiB0aGlzLnBhZ2VMb2FkZXIuZ2V0RGF0YUhyZWYoe1xuICAgICAgICAgICAgICAgICAgICAgICAgaHJlZjogdXJsLFxuICAgICAgICAgICAgICAgICAgICAgICAgYXNQYXRoOiByZXNvbHZlZEFzLFxuICAgICAgICAgICAgICAgICAgICAgICAgbG9jYWxlOiBsb2NhbGVcbiAgICAgICAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICAgICAgICAgIGlzU2VydmVyUmVuZGVyOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgcGFyc2VKU09OOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICBpbmZsaWdodENhY2hlOiB0aGlzLnNkYyxcbiAgICAgICAgICAgICAgICAgICAgcGVyc2lzdENhY2hlOiAhdGhpcy5pc1ByZXZpZXcsXG4gICAgICAgICAgICAgICAgICAgIGlzUHJlZmV0Y2g6IHRydWUsXG4gICAgICAgICAgICAgICAgICAgIHVuc3RhYmxlX3NraXBDbGllbnRDYWNoZTogb3B0aW9ucy51bnN0YWJsZV9za2lwQ2xpZW50Q2FjaGUgfHwgb3B0aW9ucy5wcmlvcml0eSAmJiAhIXByb2Nlc3MuZW52Ll9fTkVYVF9PUFRJTUlTVElDX0NMSUVOVF9DQUNIRVxuICAgICAgICAgICAgICAgIH0pLnRoZW4oKCk9PmZhbHNlKS5jYXRjaCgoKT0+ZmFsc2UpIDogZmFsc2U7XG4gICAgICAgICAgICB9KSxcbiAgICAgICAgICAgIHRoaXMucGFnZUxvYWRlcltvcHRpb25zLnByaW9yaXR5ID8gJ2xvYWRQYWdlJyA6ICdwcmVmZXRjaCddKHJvdXRlKVxuICAgICAgICBdKTtcbiAgICB9XG4gICAgYXN5bmMgZmV0Y2hDb21wb25lbnQocm91dGUpIHtcbiAgICAgICAgY29uc3QgaGFuZGxlQ2FuY2VsbGVkID0gZ2V0Q2FuY2VsbGVkSGFuZGxlcih7XG4gICAgICAgICAgICByb3V0ZSxcbiAgICAgICAgICAgIHJvdXRlcjogdGhpc1xuICAgICAgICB9KTtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IGNvbXBvbmVudFJlc3VsdCA9IGF3YWl0IHRoaXMucGFnZUxvYWRlci5sb2FkUGFnZShyb3V0ZSk7XG4gICAgICAgICAgICBoYW5kbGVDYW5jZWxsZWQoKTtcbiAgICAgICAgICAgIHJldHVybiBjb21wb25lbnRSZXN1bHQ7XG4gICAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICAgICAgaGFuZGxlQ2FuY2VsbGVkKCk7XG4gICAgICAgICAgICB0aHJvdyBlcnI7XG4gICAgICAgIH1cbiAgICB9XG4gICAgX2dldERhdGEoZm4pIHtcbiAgICAgICAgbGV0IGNhbmNlbGxlZCA9IGZhbHNlO1xuICAgICAgICBjb25zdCBjYW5jZWwgPSAoKT0+e1xuICAgICAgICAgICAgY2FuY2VsbGVkID0gdHJ1ZTtcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5jbGMgPSBjYW5jZWw7XG4gICAgICAgIHJldHVybiBmbigpLnRoZW4oKGRhdGEpPT57XG4gICAgICAgICAgICBpZiAoY2FuY2VsID09PSB0aGlzLmNsYykge1xuICAgICAgICAgICAgICAgIHRoaXMuY2xjID0gbnVsbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChjYW5jZWxsZWQpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBlcnIgPSBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKCdMb2FkaW5nIGluaXRpYWwgcHJvcHMgY2FuY2VsbGVkJyksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZTogXCJFNDA1XCIsXG4gICAgICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBlcnIuY2FuY2VsbGVkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICB0aHJvdyBlcnI7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gZGF0YTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGdldEluaXRpYWxQcm9wcyhDb21wb25lbnQsIGN0eCkge1xuICAgICAgICBjb25zdCB7IENvbXBvbmVudDogQXBwIH0gPSB0aGlzLmNvbXBvbmVudHNbJy9fYXBwJ107XG4gICAgICAgIGNvbnN0IEFwcFRyZWUgPSB0aGlzLl93cmFwQXBwKEFwcCk7XG4gICAgICAgIGN0eC5BcHBUcmVlID0gQXBwVHJlZTtcbiAgICAgICAgcmV0dXJuICgwLCBfdXRpbHMubG9hZEdldEluaXRpYWxQcm9wcykoQXBwLCB7XG4gICAgICAgICAgICBBcHBUcmVlLFxuICAgICAgICAgICAgQ29tcG9uZW50LFxuICAgICAgICAgICAgcm91dGVyOiB0aGlzLFxuICAgICAgICAgICAgY3R4XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBnZXQgcm91dGUoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLnJvdXRlO1xuICAgIH1cbiAgICBnZXQgcGF0aG5hbWUoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLnBhdGhuYW1lO1xuICAgIH1cbiAgICBnZXQgcXVlcnkoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLnF1ZXJ5O1xuICAgIH1cbiAgICBnZXQgYXNQYXRoKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5zdGF0ZS5hc1BhdGg7XG4gICAgfVxuICAgIGdldCBsb2NhbGUoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLmxvY2FsZTtcbiAgICB9XG4gICAgZ2V0IGlzRmFsbGJhY2soKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLmlzRmFsbGJhY2s7XG4gICAgfVxuICAgIGdldCBpc1ByZXZpZXcoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLmlzUHJldmlldztcbiAgICB9XG4gICAgY29uc3RydWN0b3IocGF0aG5hbWUsIHF1ZXJ5LCBhcywgeyBpbml0aWFsUHJvcHMsIHBhZ2VMb2FkZXIsIEFwcCwgd3JhcEFwcCwgQ29tcG9uZW50LCBlcnIsIHN1YnNjcmlwdGlvbiwgaXNGYWxsYmFjaywgbG9jYWxlLCBsb2NhbGVzLCBkZWZhdWx0TG9jYWxlLCBkb21haW5Mb2NhbGVzLCBpc1ByZXZpZXcgfSl7XG4gICAgICAgIC8vIFNlcnZlciBEYXRhIENhY2hlIChmdWxsIGRhdGEgcmVxdWVzdHMpXG4gICAgICAgIHRoaXMuc2RjID0ge307XG4gICAgICAgIC8vIFNlcnZlciBCYWNrZ3JvdW5kIENhY2hlIChIRUFEIHJlcXVlc3RzKVxuICAgICAgICB0aGlzLnNiYyA9IHt9O1xuICAgICAgICB0aGlzLmlzRmlyc3RQb3BTdGF0ZUV2ZW50ID0gdHJ1ZTtcbiAgICAgICAgdGhpcy5fa2V5ID0gY3JlYXRlS2V5KCk7XG4gICAgICAgIHRoaXMub25Qb3BTdGF0ZSA9IChlKT0+e1xuICAgICAgICAgICAgY29uc3QgeyBpc0ZpcnN0UG9wU3RhdGVFdmVudCB9ID0gdGhpcztcbiAgICAgICAgICAgIHRoaXMuaXNGaXJzdFBvcFN0YXRlRXZlbnQgPSBmYWxzZTtcbiAgICAgICAgICAgIGNvbnN0IHN0YXRlID0gZS5zdGF0ZTtcbiAgICAgICAgICAgIGlmICghc3RhdGUpIHtcbiAgICAgICAgICAgICAgICAvLyBXZSBnZXQgc3RhdGUgYXMgdW5kZWZpbmVkIGZvciB0d28gcmVhc29ucy5cbiAgICAgICAgICAgICAgICAvLyAgMS4gV2l0aCBvbGRlciBzYWZhcmkgKDwgOCkgYW5kIG9sZGVyIGNocm9tZSAoPCAzNClcbiAgICAgICAgICAgICAgICAvLyAgMi4gV2hlbiB0aGUgVVJMIGNoYW5nZWQgd2l0aCAjXG4gICAgICAgICAgICAgICAgLy9cbiAgICAgICAgICAgICAgICAvLyBJbiB0aGUgYm90aCBjYXNlcywgd2UgZG9uJ3QgbmVlZCB0byBwcm9jZWVkIGFuZCBjaGFuZ2UgdGhlIHJvdXRlLlxuICAgICAgICAgICAgICAgIC8vIChhcyBpdCdzIGFscmVhZHkgY2hhbmdlZClcbiAgICAgICAgICAgICAgICAvLyBCdXQgd2UgY2FuIHNpbXBseSByZXBsYWNlIHRoZSBzdGF0ZSB3aXRoIHRoZSBuZXcgY2hhbmdlcy5cbiAgICAgICAgICAgICAgICAvLyBBY3R1YWxseSwgZm9yICgxKSB3ZSBkb24ndCBuZWVkIHRvIG5vdGhpbmcuIEJ1dCBpdCdzIGhhcmQgdG8gZGV0ZWN0IHRoYXQgZXZlbnQuXG4gICAgICAgICAgICAgICAgLy8gU28sIGRvaW5nIHRoZSBmb2xsb3dpbmcgZm9yICgxKSBkb2VzIG5vIGhhcm0uXG4gICAgICAgICAgICAgICAgY29uc3QgeyBwYXRobmFtZSwgcXVlcnkgfSA9IHRoaXM7XG4gICAgICAgICAgICAgICAgdGhpcy5jaGFuZ2VTdGF0ZSgncmVwbGFjZVN0YXRlJywgKDAsIF9mb3JtYXR1cmwuZm9ybWF0V2l0aFZhbGlkYXRpb24pKHtcbiAgICAgICAgICAgICAgICAgICAgcGF0aG5hbWU6ICgwLCBfYWRkYmFzZXBhdGguYWRkQmFzZVBhdGgpKHBhdGhuYW1lKSxcbiAgICAgICAgICAgICAgICAgICAgcXVlcnlcbiAgICAgICAgICAgICAgICB9KSwgKDAsIF91dGlscy5nZXRVUkwpKCkpO1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIF9fTkEgaXMgdXNlZCB0byBpZGVudGlmeSBpZiB0aGUgaGlzdG9yeSBlbnRyeSBjYW4gYmUgaGFuZGxlZCBieSB0aGUgYXBwLXJvdXRlci5cbiAgICAgICAgICAgIGlmIChzdGF0ZS5fX05BKSB7XG4gICAgICAgICAgICAgICAgd2luZG93LmxvY2F0aW9uLnJlbG9hZCgpO1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghc3RhdGUuX19OKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gU2FmYXJpIGZpcmVzIHBvcHN0YXRlZXZlbnQgd2hlbiByZW9wZW5pbmcgdGhlIGJyb3dzZXIuXG4gICAgICAgICAgICBpZiAoaXNGaXJzdFBvcFN0YXRlRXZlbnQgJiYgdGhpcy5sb2NhbGUgPT09IHN0YXRlLm9wdGlvbnMubG9jYWxlICYmIHN0YXRlLmFzID09PSB0aGlzLmFzUGF0aCkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGxldCBmb3JjZWRTY3JvbGw7XG4gICAgICAgICAgICBjb25zdCB7IHVybCwgYXMsIG9wdGlvbnMsIGtleSB9ID0gc3RhdGU7XG4gICAgICAgICAgICBpZiAocHJvY2Vzcy5lbnYuX19ORVhUX1NDUk9MTF9SRVNUT1JBVElPTikge1xuICAgICAgICAgICAgICAgIGlmIChtYW51YWxTY3JvbGxSZXN0b3JhdGlvbikge1xuICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5fa2V5ICE9PSBrZXkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFNuYXBzaG90IGN1cnJlbnQgc2Nyb2xsIHBvc2l0aW9uOlxuICAgICAgICAgICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXNzaW9uU3RvcmFnZS5zZXRJdGVtKCdfX25leHRfc2Nyb2xsXycgKyB0aGlzLl9rZXksIEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeDogc2VsZi5wYWdlWE9mZnNldCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeTogc2VsZi5wYWdlWU9mZnNldFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHt9XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBSZXN0b3JlIG9sZCBzY3JvbGwgcG9zaXRpb246XG4gICAgICAgICAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHYgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCdfX25leHRfc2Nyb2xsXycgKyBrZXkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvcmNlZFNjcm9sbCA9IEpTT04ucGFyc2Uodik7XG4gICAgICAgICAgICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9yY2VkU2Nyb2xsID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB4OiAwLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB5OiAwXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuX2tleSA9IGtleTtcbiAgICAgICAgICAgIGNvbnN0IHsgcGF0aG5hbWUgfSA9ICgwLCBfcGFyc2VyZWxhdGl2ZXVybC5wYXJzZVJlbGF0aXZlVXJsKSh1cmwpO1xuICAgICAgICAgICAgLy8gTWFrZSBzdXJlIHdlIGRvbid0IHJlLXJlbmRlciBvbiBpbml0aWFsIGxvYWQsXG4gICAgICAgICAgICAvLyBjYW4gYmUgY2F1c2VkIGJ5IG5hdmlnYXRpbmcgYmFjayBmcm9tIGFuIGV4dGVybmFsIHNpdGVcbiAgICAgICAgICAgIGlmICh0aGlzLmlzU3NyICYmIGFzID09PSAoMCwgX2FkZGJhc2VwYXRoLmFkZEJhc2VQYXRoKSh0aGlzLmFzUGF0aCkgJiYgcGF0aG5hbWUgPT09ICgwLCBfYWRkYmFzZXBhdGguYWRkQmFzZVBhdGgpKHRoaXMucGF0aG5hbWUpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gSWYgdGhlIGRvd25zdHJlYW0gYXBwbGljYXRpb24gcmV0dXJucyBmYWxzeSwgcmV0dXJuLlxuICAgICAgICAgICAgLy8gVGhleSB3aWxsIHRoZW4gYmUgcmVzcG9uc2libGUgZm9yIGhhbmRsaW5nIHRoZSBldmVudC5cbiAgICAgICAgICAgIGlmICh0aGlzLl9icHMgJiYgIXRoaXMuX2JwcyhzdGF0ZSkpIHtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLmNoYW5nZSgncmVwbGFjZVN0YXRlJywgdXJsLCBhcywgT2JqZWN0LmFzc2lnbih7fSwgb3B0aW9ucywge1xuICAgICAgICAgICAgICAgIHNoYWxsb3c6IG9wdGlvbnMuc2hhbGxvdyAmJiB0aGlzLl9zaGFsbG93LFxuICAgICAgICAgICAgICAgIGxvY2FsZTogb3B0aW9ucy5sb2NhbGUgfHwgdGhpcy5kZWZhdWx0TG9jYWxlLFxuICAgICAgICAgICAgICAgIC8vIEB0cy1pZ25vcmUgaW50ZXJuYWwgdmFsdWUgbm90IGV4cG9zZWQgb24gdHlwZXNcbiAgICAgICAgICAgICAgICBfaDogMFxuICAgICAgICAgICAgfSksIGZvcmNlZFNjcm9sbCk7XG4gICAgICAgIH07XG4gICAgICAgIC8vIHJlcHJlc2VudHMgdGhlIGN1cnJlbnQgY29tcG9uZW50IGtleVxuICAgICAgICBjb25zdCByb3V0ZSA9ICgwLCBfcmVtb3ZldHJhaWxpbmdzbGFzaC5yZW1vdmVUcmFpbGluZ1NsYXNoKShwYXRobmFtZSk7XG4gICAgICAgIC8vIHNldCB1cCB0aGUgY29tcG9uZW50IGNhY2hlIChieSByb3V0ZSBrZXlzKVxuICAgICAgICB0aGlzLmNvbXBvbmVudHMgPSB7fTtcbiAgICAgICAgLy8gV2Ugc2hvdWxkIG5vdCBrZWVwIHRoZSBjYWNoZSwgaWYgdGhlcmUncyBhbiBlcnJvclxuICAgICAgICAvLyBPdGhlcndpc2UsIHRoaXMgY2F1c2UgaXNzdWVzIHdoZW4gd2hlbiBnb2luZyBiYWNrIGFuZFxuICAgICAgICAvLyBjb21lIGFnYWluIHRvIHRoZSBlcnJvcmVkIHBhZ2UuXG4gICAgICAgIGlmIChwYXRobmFtZSAhPT0gJy9fZXJyb3InKSB7XG4gICAgICAgICAgICB0aGlzLmNvbXBvbmVudHNbcm91dGVdID0ge1xuICAgICAgICAgICAgICAgIENvbXBvbmVudCxcbiAgICAgICAgICAgICAgICBpbml0aWFsOiB0cnVlLFxuICAgICAgICAgICAgICAgIHByb3BzOiBpbml0aWFsUHJvcHMsXG4gICAgICAgICAgICAgICAgZXJyLFxuICAgICAgICAgICAgICAgIF9fTl9TU0c6IGluaXRpYWxQcm9wcyAmJiBpbml0aWFsUHJvcHMuX19OX1NTRyxcbiAgICAgICAgICAgICAgICBfX05fU1NQOiBpbml0aWFsUHJvcHMgJiYgaW5pdGlhbFByb3BzLl9fTl9TU1BcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5jb21wb25lbnRzWycvX2FwcCddID0ge1xuICAgICAgICAgICAgQ29tcG9uZW50OiBBcHAsXG4gICAgICAgICAgICBzdHlsZVNoZWV0czogW11cbiAgICAgICAgfTtcbiAgICAgICAgLy8gQmFja3dhcmRzIGNvbXBhdCBmb3IgUm91dGVyLnJvdXRlci5ldmVudHNcbiAgICAgICAgLy8gVE9ETzogU2hvdWxkIGJlIHJlbW92ZSB0aGUgZm9sbG93aW5nIG1ham9yIHZlcnNpb24gYXMgaXQgd2FzIG5ldmVyIGRvY3VtZW50ZWRcbiAgICAgICAgdGhpcy5ldmVudHMgPSBSb3V0ZXIuZXZlbnRzO1xuICAgICAgICB0aGlzLnBhZ2VMb2FkZXIgPSBwYWdlTG9hZGVyO1xuICAgICAgICAvLyBpZiBhdXRvIHByZXJlbmRlcmVkIGFuZCBkeW5hbWljIHJvdXRlIHdhaXQgdG8gdXBkYXRlIGFzUGF0aFxuICAgICAgICAvLyB1bnRpbCBhZnRlciBtb3VudCB0byBwcmV2ZW50IGh5ZHJhdGlvbiBtaXNtYXRjaFxuICAgICAgICBjb25zdCBhdXRvRXhwb3J0RHluYW1pYyA9ICgwLCBfaXNkeW5hbWljLmlzRHluYW1pY1JvdXRlKShwYXRobmFtZSkgJiYgc2VsZi5fX05FWFRfREFUQV9fLmF1dG9FeHBvcnQ7XG4gICAgICAgIHRoaXMuYmFzZVBhdGggPSBwcm9jZXNzLmVudi5fX05FWFRfUk9VVEVSX0JBU0VQQVRIIHx8ICcnO1xuICAgICAgICB0aGlzLnN1YiA9IHN1YnNjcmlwdGlvbjtcbiAgICAgICAgdGhpcy5jbGMgPSBudWxsO1xuICAgICAgICB0aGlzLl93cmFwQXBwID0gd3JhcEFwcDtcbiAgICAgICAgLy8gbWFrZSBzdXJlIHRvIGlnbm9yZSBleHRyYSBwb3BTdGF0ZSBpbiBzYWZhcmkgb24gbmF2aWdhdGluZ1xuICAgICAgICAvLyBiYWNrIGZyb20gZXh0ZXJuYWwgc2l0ZVxuICAgICAgICB0aGlzLmlzU3NyID0gdHJ1ZTtcbiAgICAgICAgdGhpcy5pc0xvY2FsZURvbWFpbiA9IGZhbHNlO1xuICAgICAgICB0aGlzLmlzUmVhZHkgPSAhIShzZWxmLl9fTkVYVF9EQVRBX18uZ3NzcCB8fCBzZWxmLl9fTkVYVF9EQVRBX18uZ2lwIHx8IHNlbGYuX19ORVhUX0RBVEFfXy5pc0V4cGVyaW1lbnRhbENvbXBpbGUgfHwgc2VsZi5fX05FWFRfREFUQV9fLmFwcEdpcCAmJiAhc2VsZi5fX05FWFRfREFUQV9fLmdzcCB8fCAhYXV0b0V4cG9ydER5bmFtaWMgJiYgIXNlbGYubG9jYXRpb24uc2VhcmNoICYmICFwcm9jZXNzLmVudi5fX05FWFRfSEFTX1JFV1JJVEVTKTtcbiAgICAgICAgaWYgKHByb2Nlc3MuZW52Ll9fTkVYVF9JMThOX1NVUFBPUlQpIHtcbiAgICAgICAgICAgIHRoaXMubG9jYWxlcyA9IGxvY2FsZXM7XG4gICAgICAgICAgICB0aGlzLmRlZmF1bHRMb2NhbGUgPSBkZWZhdWx0TG9jYWxlO1xuICAgICAgICAgICAgdGhpcy5kb21haW5Mb2NhbGVzID0gZG9tYWluTG9jYWxlcztcbiAgICAgICAgICAgIHRoaXMuaXNMb2NhbGVEb21haW4gPSAhISgwLCBfZGV0ZWN0ZG9tYWlubG9jYWxlLmRldGVjdERvbWFpbkxvY2FsZSkoZG9tYWluTG9jYWxlcywgc2VsZi5sb2NhdGlvbi5ob3N0bmFtZSk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zdGF0ZSA9IHtcbiAgICAgICAgICAgIHJvdXRlLFxuICAgICAgICAgICAgcGF0aG5hbWUsXG4gICAgICAgICAgICBxdWVyeSxcbiAgICAgICAgICAgIGFzUGF0aDogYXV0b0V4cG9ydER5bmFtaWMgPyBwYXRobmFtZSA6IGFzLFxuICAgICAgICAgICAgaXNQcmV2aWV3OiAhIWlzUHJldmlldyxcbiAgICAgICAgICAgIGxvY2FsZTogcHJvY2Vzcy5lbnYuX19ORVhUX0kxOE5fU1VQUE9SVCA/IGxvY2FsZSA6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIGlzRmFsbGJhY2tcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5faW5pdGlhbE1hdGNoZXNNaWRkbGV3YXJlUHJvbWlzZSA9IFByb21pc2UucmVzb2x2ZShmYWxzZSk7XG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgLy8gbWFrZSBzdXJlIFwiYXNcIiBkb2Vzbid0IHN0YXJ0IHdpdGggZG91YmxlIHNsYXNoZXMgb3IgZWxzZSBpdCBjYW5cbiAgICAgICAgICAgIC8vIHRocm93IGFuIGVycm9yIGFzIGl0J3MgY29uc2lkZXJlZCBpbnZhbGlkXG4gICAgICAgICAgICBpZiAoIWFzLnN0YXJ0c1dpdGgoJy8vJykpIHtcbiAgICAgICAgICAgICAgICAvLyBpbiBvcmRlciBmb3IgYGUuc3RhdGVgIHRvIHdvcmsgb24gdGhlIGBvbnBvcHN0YXRlYCBldmVudFxuICAgICAgICAgICAgICAgIC8vIHdlIGhhdmUgdG8gcmVnaXN0ZXIgdGhlIGluaXRpYWwgcm91dGUgdXBvbiBpbml0aWFsaXphdGlvblxuICAgICAgICAgICAgICAgIGNvbnN0IG9wdGlvbnMgPSB7XG4gICAgICAgICAgICAgICAgICAgIGxvY2FsZVxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgY29uc3QgYXNQYXRoID0gKDAsIF91dGlscy5nZXRVUkwpKCk7XG4gICAgICAgICAgICAgICAgdGhpcy5faW5pdGlhbE1hdGNoZXNNaWRkbGV3YXJlUHJvbWlzZSA9IG1hdGNoZXNNaWRkbGV3YXJlKHtcbiAgICAgICAgICAgICAgICAgICAgcm91dGVyOiB0aGlzLFxuICAgICAgICAgICAgICAgICAgICBsb2NhbGUsXG4gICAgICAgICAgICAgICAgICAgIGFzUGF0aFxuICAgICAgICAgICAgICAgIH0pLnRoZW4oKG1hdGNoZXMpPT57XG4gICAgICAgICAgICAgICAgICAgIC8vIGlmIG1pZGRsZXdhcmUgbWF0Y2hlcyB3ZSBsZWF2ZSByZXNvbHZpbmcgdG8gdGhlIGNoYW5nZSBmdW5jdGlvblxuICAgICAgICAgICAgICAgICAgICAvLyBhcyB0aGUgc2VydmVyIG5lZWRzIHRvIHJlc29sdmUgZm9yIGNvcnJlY3QgcHJpb3JpdHlcbiAgICAgICAgICAgICAgICAgICAgO1xuICAgICAgICAgICAgICAgICAgICBvcHRpb25zLl9zaG91bGRSZXNvbHZlSHJlZiA9IGFzICE9PSBwYXRobmFtZTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jaGFuZ2VTdGF0ZSgncmVwbGFjZVN0YXRlJywgbWF0Y2hlcyA/IGFzUGF0aCA6ICgwLCBfZm9ybWF0dXJsLmZvcm1hdFdpdGhWYWxpZGF0aW9uKSh7XG4gICAgICAgICAgICAgICAgICAgICAgICBwYXRobmFtZTogKDAsIF9hZGRiYXNlcGF0aC5hZGRCYXNlUGF0aCkocGF0aG5hbWUpLFxuICAgICAgICAgICAgICAgICAgICAgICAgcXVlcnlcbiAgICAgICAgICAgICAgICAgICAgfSksIGFzUGF0aCwgb3B0aW9ucyk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBtYXRjaGVzO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3BvcHN0YXRlJywgdGhpcy5vblBvcFN0YXRlKTtcbiAgICAgICAgICAgIC8vIGVuYWJsZSBjdXN0b20gc2Nyb2xsIHJlc3RvcmF0aW9uIGhhbmRsaW5nIHdoZW4gYXZhaWxhYmxlXG4gICAgICAgICAgICAvLyBvdGhlcndpc2UgZmFsbGJhY2sgdG8gYnJvd3NlcidzIGRlZmF1bHQgaGFuZGxpbmdcbiAgICAgICAgICAgIGlmIChwcm9jZXNzLmVudi5fX05FWFRfU0NST0xMX1JFU1RPUkFUSU9OKSB7XG4gICAgICAgICAgICAgICAgaWYgKG1hbnVhbFNjcm9sbFJlc3RvcmF0aW9uKSB7XG4gICAgICAgICAgICAgICAgICAgIHdpbmRvdy5oaXN0b3J5LnNjcm9sbFJlc3RvcmF0aW9uID0gJ21hbnVhbCc7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxufVxuUm91dGVyLmV2ZW50cyA9ICgwLCBfbWl0dC5kZWZhdWx0KSgpO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1yb3V0ZXIuanMubWFwIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwibW9kdWxlIiwiY3JlYXRlS2V5IiwiZGVmYXVsdCIsIm1hdGNoZXNNaWRkbGV3YXJlIiwiX2V4cG9ydCIsInRhcmdldCIsImFsbCIsIm5hbWUiLCJlbnVtZXJhYmxlIiwiZ2V0IiwiUm91dGVyIiwiX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0IiwicmVxdWlyZSIsIl9pbnRlcm9wX3JlcXVpcmVfd2lsZGNhcmQiLCJfcmVtb3ZldHJhaWxpbmdzbGFzaCIsIl9yb3V0ZWxvYWRlciIsIl9zY3JpcHQiLCJfaXNlcnJvciIsIl8iLCJfZGVub3JtYWxpemVwYWdlcGF0aCIsIl9ub3JtYWxpemVsb2NhbGVwYXRoIiwiX21pdHQiLCJfdXRpbHMiLCJfaXNkeW5hbWljIiwiX3BhcnNlcmVsYXRpdmV1cmwiLCJfcmVzb2x2ZXJld3JpdGVzIiwiX3JvdXRlbWF0Y2hlciIsIl9yb3V0ZXJlZ2V4IiwiX2Zvcm1hdHVybCIsIl9kZXRlY3Rkb21haW5sb2NhbGUiLCJfcGFyc2VwYXRoIiwiX2FkZGxvY2FsZSIsIl9yZW1vdmVsb2NhbGUiLCJfcmVtb3ZlYmFzZXBhdGgiLCJfYWRkYmFzZXBhdGgiLCJfaGFzYmFzZXBhdGgiLCJfcmVzb2x2ZWhyZWYiLCJfaXNhcGlyb3V0ZSIsIl9nZXRuZXh0cGF0aG5hbWVpbmZvIiwiX2Zvcm1hdG5leHRwYXRobmFtZWluZm8iLCJfY29tcGFyZXN0YXRlcyIsIl9pc2xvY2FsdXJsIiwiX2lzYm90IiwiX29taXQiLCJfaW50ZXJwb2xhdGVhcyIsIl9oYW5kbGVzbW9vdGhzY3JvbGwiLCJfY29uc3RhbnRzIiwiYnVpbGRDYW5jZWxsYXRpb25FcnJvciIsImFzc2lnbiIsIkVycm9yIiwiY29uZmlndXJhYmxlIiwiY2FuY2VsbGVkIiwib3B0aW9ucyIsIm1hdGNoZXJzIiwiUHJvbWlzZSIsInJlc29sdmUiLCJyb3V0ZXIiLCJwYWdlTG9hZGVyIiwiZ2V0TWlkZGxld2FyZSIsInBhdGhuYW1lIiwiYXNQYXRobmFtZSIsInBhcnNlUGF0aCIsImFzUGF0aCIsImNsZWFuZWRBcyIsImhhc0Jhc2VQYXRoIiwicmVtb3ZlQmFzZVBhdGgiLCJhc1dpdGhCYXNlUGF0aEFuZExvY2FsZSIsImFkZEJhc2VQYXRoIiwiYWRkTG9jYWxlIiwibG9jYWxlIiwic29tZSIsIm0iLCJSZWdFeHAiLCJyZWdleHAiLCJ0ZXN0Iiwic3RyaXBPcmlnaW4iLCJ1cmwiLCJvcmlnaW4iLCJnZXRMb2NhdGlvbk9yaWdpbiIsInN0YXJ0c1dpdGgiLCJzdWJzdHJpbmciLCJsZW5ndGgiLCJwcmVwYXJlVXJsQXMiLCJhcyIsInJlc29sdmVkSHJlZiIsInJlc29sdmVkQXMiLCJyZXNvbHZlSHJlZiIsImhyZWZXYXNBYnNvbHV0ZSIsImFzV2FzQWJzb2x1dGUiLCJwcmVwYXJlZFVybCIsInByZXBhcmVkQXMiLCJyZXNvbHZlRHluYW1pY1JvdXRlIiwicGFnZXMiLCJjbGVhblBhdGhuYW1lIiwicmVtb3ZlVHJhaWxpbmdTbGFzaCIsImRlbm9ybWFsaXplUGFnZVBhdGgiLCJpbmNsdWRlcyIsInBhZ2UiLCJpc0R5bmFtaWNSb3V0ZSIsImdldFJvdXRlUmVnZXgiLCJyZSIsImdldE1pZGRsZXdhcmVEYXRhIiwic291cmNlIiwicmVzcG9uc2UiLCJuZXh0Q29uZmlnIiwiYmFzZVBhdGgiLCJpMThuIiwibG9jYWxlcyIsInRyYWlsaW5nU2xhc2giLCJCb29sZWFuIiwicHJvY2VzcyIsImVudiIsIl9fTkVYVF9UUkFJTElOR19TTEFTSCIsInJld3JpdGVIZWFkZXIiLCJoZWFkZXJzIiwicmV3cml0ZVRhcmdldCIsIm1hdGNoZWRQYXRoIiwiTUFUQ0hFRF9QQVRIX0hFQURFUiIsIl9fTkVYVF9FWFRFUk5BTF9NSURETEVXQVJFX1JFV1JJVEVfUkVTT0xWRSIsInBhcnNlZFJld3JpdGVUYXJnZXQiLCJwYXJzZVJlbGF0aXZlVXJsIiwicGF0aG5hbWVJbmZvIiwiZ2V0TmV4dFBhdGhuYW1lSW5mbyIsInBhcnNlRGF0YSIsImZzUGF0aG5hbWUiLCJnZXRQYWdlTGlzdCIsImdldENsaWVudEJ1aWxkTWFuaWZlc3QiLCJ0aGVuIiwicGFyYW0iLCJfX3Jld3JpdGVzIiwicmV3cml0ZXMiLCJub3JtYWxpemVMb2NhbGVQYXRoIiwicGFyc2VkU291cmNlIiwiX19ORVhUX0hBU19SRVdSSVRFUyIsInVuZGVmaW5lZCIsInJlc3VsdCIsInF1ZXJ5IiwicGF0aCIsIm1hdGNoZWRQYWdlIiwicGFyc2VkQXMiLCJyZXNvbHZlZFBhdGhuYW1lIiwibWF0Y2hlcyIsImdldFJvdXRlTWF0Y2hlciIsInR5cGUiLCJzcmMiLCJmb3JtYXROZXh0UGF0aG5hbWVJbmZvIiwiZGVmYXVsdExvY2FsZSIsImJ1aWxkSWQiLCJkZXN0aW5hdGlvbiIsImhhc2giLCJyZWRpcmVjdFRhcmdldCIsIm5ld0FzIiwibmV3VXJsIiwid2l0aE1pZGRsZXdhcmVFZmZlY3RzIiwiZmV0Y2hEYXRhIiwiZGF0YSIsImVmZmVjdCIsImRhdGFIcmVmIiwianNvbiIsInRleHQiLCJjYWNoZUtleSIsIm1hbnVhbFNjcm9sbFJlc3RvcmF0aW9uIiwiX19ORVhUX1NDUk9MTF9SRVNUT1JBVElPTiIsIndpbmRvdyIsImhpc3RvcnkiLCJ2Iiwic2Vzc2lvblN0b3JhZ2UiLCJzZXRJdGVtIiwicmVtb3ZlSXRlbSIsIm4iLCJTU0dfREFUQV9OT1RfRk9VTkQiLCJTeW1ib2wiLCJmZXRjaFJldHJ5IiwiYXR0ZW1wdHMiLCJmZXRjaCIsImNyZWRlbnRpYWxzIiwibWV0aG9kIiwib2siLCJzdGF0dXMiLCJ0cnlUb1BhcnNlQXNKU09OIiwiSlNPTiIsInBhcnNlIiwiZXJyb3IiLCJmZXRjaE5leHREYXRhIiwiaW5mbGlnaHRDYWNoZSIsImlzUHJlZmV0Y2giLCJoYXNNaWRkbGV3YXJlIiwiaXNTZXJ2ZXJSZW5kZXIiLCJwYXJzZUpTT04iLCJwZXJzaXN0Q2FjaGUiLCJpc0JhY2tncm91bmQiLCJ1bnN0YWJsZV9za2lwQ2xpZW50Q2FjaGUiLCJocmVmIiwiVVJMIiwibG9jYXRpb24iLCJnZXREYXRhIiwicGFyYW1zIiwiX3BhcmFtc19tZXRob2QiLCJwdXJwb3NlIiwiTkVYVF9ERVBMT1lNRU5UX0lEIiwiX3RyeVRvUGFyc2VBc0pTT04iLCJub3RGb3VuZCIsIm1hcmtBc3NldEVycm9yIiwiY2F0Y2giLCJlcnIiLCJtZXNzYWdlIiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic2xpY2UiLCJoYW5kbGVIYXJkTmF2aWdhdGlvbiIsImdldENhbmNlbGxlZEhhbmRsZXIiLCJyb3V0ZSIsImNhbmNlbCIsImNsYyIsImhhbmRsZUNhbmNlbGxlZCIsInJlbG9hZCIsImJhY2siLCJmb3J3YXJkIiwicHVzaCIsIl9rZXkiLCJzdHJpbmdpZnkiLCJ4Iiwic2VsZiIsInBhZ2VYT2Zmc2V0IiwieSIsInBhZ2VZT2Zmc2V0IiwiZSIsImNoYW5nZSIsInJlcGxhY2UiLCJfYmZsIiwic2tpcE5hdmlnYXRlIiwiX19ORVhUX0NMSUVOVF9ST1VURVJfRklMVEVSX0VOQUJMRUQiLCJfYmZsX3MiLCJfYmZsX2QiLCJCbG9vbUZpbHRlciIsInN0YXRpY0ZpbHRlckRhdGEiLCJkeW5hbWljRmlsdGVyRGF0YSIsIl9fcm91dGVyRmlsdGVyU3RhdGljIiwiX19yb3V0ZXJGaWx0ZXJEeW5hbWljIiwiY29uc29sZSIsInJvdXRlckZpbHRlclNWYWx1ZSIsIl9fTkVYVF9DTElFTlRfUk9VVEVSX1NfRklMVEVSIiwicm91dGVyRmlsdGVyRFZhbHVlIiwiX19ORVhUX0NMSUVOVF9ST1VURVJfRF9GSUxURVIiLCJudW1IYXNoZXMiLCJudW1JdGVtcyIsImVycm9yUmF0ZSIsImltcG9ydCIsIm1hdGNoZXNCZmxTdGF0aWMiLCJtYXRjaGVzQmZsRHluYW1pYyIsInBhdGhzVG9DaGVjayIsImN1ckFzIiwiYWxsb3dNYXRjaEN1cnJlbnQiLCJhc05vU2xhc2giLCJhc05vU2xhc2hMb2NhbGUiLCJfdGhpc19fYmZsX3MiLCJfdGhpc19fYmZsX3MxIiwiY29udGFpbnMiLCJub3JtYWxpemVkQVMiLCJjdXJBc1BhcnRzIiwic3BsaXQiLCJpIiwiX3RoaXNfX2JmbF9kIiwiY3VycmVudFBhcnQiLCJqb2luIiwiZm9yY2VkU2Nyb2xsIiwiX3RoaXNfY29tcG9uZW50c19wYXRobmFtZSIsImlzTG9jYWxVUkwiLCJpc1F1ZXJ5VXBkYXRpbmciLCJfaCIsInNoYWxsb3ciLCJzaG91bGRSZXNvbHZlSHJlZiIsIl9zaG91bGRSZXNvbHZlSHJlZiIsIm5leHRTdGF0ZSIsInN0YXRlIiwicmVhZHlTdGF0ZUNoYW5nZSIsImlzUmVhZHkiLCJpc1NzciIsInByZXZMb2NhbGUiLCJfX05FWFRfSTE4Tl9TVVBQT1JUIiwibG9jYWxlUGF0aFJlc3VsdCIsImRldGVjdGVkTG9jYWxlIiwiZm9ybWF0V2l0aFZhbGlkYXRpb24iLCJkaWROYXZpZ2F0ZSIsIl90aGlzX2xvY2FsZXMiLCJkZXRlY3RlZERvbWFpbiIsImRldGVjdERvbWFpbkxvY2FsZSIsImRvbWFpbkxvY2FsZXMiLCJpc0xvY2FsZURvbWFpbiIsImhvc3RuYW1lIiwiZG9tYWluIiwiYXNOb0Jhc2VQYXRoIiwiaHR0cCIsIlNUIiwicGVyZm9ybWFuY2UiLCJtYXJrIiwic2Nyb2xsIiwicm91dGVQcm9wcyIsIl9pbkZsaWdodFJvdXRlIiwiZXZlbnRzIiwiZW1pdCIsInJlbW92ZUxvY2FsZSIsImxvY2FsZUNoYW5nZSIsIm9ubHlBSGFzaENoYW5nZSIsImNoYW5nZVN0YXRlIiwic2Nyb2xsVG9IYXNoIiwic2V0IiwiY29tcG9uZW50cyIsInBhcnNlZCIsInVybElzTmV3IiwicGFyc2VkQXNQYXRobmFtZSIsIl9fYXBwUm91dGVyIiwiaXNNaWRkbGV3YXJlUmV3cml0ZSIsImlzTWlkZGxld2FyZU1hdGNoIiwicmV3cml0ZXNSZXN1bHQiLCJwIiwiZXh0ZXJuYWxEZXN0Iiwicm91dGVNYXRjaCIsInJvdXRlUmVnZXgiLCJzaG91bGRJbnRlcnBvbGF0ZSIsImludGVycG9sYXRlZEFzIiwiaW50ZXJwb2xhdGVBcyIsIm1pc3NpbmdQYXJhbXMiLCJrZXlzIiwiZ3JvdXBzIiwiZmlsdGVyIiwib3B0aW9uYWwiLCJ3YXJuIiwib21pdCIsImlzRXJyb3JSb3V0ZSIsIl9zZWxmX19fTkVYVF9EQVRBX19fcHJvcHNfcGFnZVByb3BzIiwiX3NlbGZfX19ORVhUX0RBVEFfX19wcm9wcyIsIl9yb3V0ZUluZm9fcHJvcHMiLCJyb3V0ZUluZm8iLCJnZXRSb3V0ZUluZm8iLCJpc1ByZXZpZXciLCJpc0ZhbGxiYWNrIiwiY2xlYW5lZFBhcnNlZFBhdGhuYW1lIiwiZm9yRWFjaCIsImtleSIsInByZWZpeGVkQXMiLCJyZXdyaXRlQXMiLCJsb2NhbGVSZXN1bHQiLCJjdXJSb3V0ZU1hdGNoIiwiY29tcG9uZW50IiwiQ29tcG9uZW50IiwidW5zdGFibGVfc2NyaXB0TG9hZGVyIiwic2NyaXB0cyIsImNvbmNhdCIsInNjcmlwdCIsImhhbmRsZUNsaWVudFNjcmlwdExvYWQiLCJwcm9wcyIsIl9fTl9TU0ciLCJfX05fU1NQIiwicGFnZVByb3BzIiwiX19OX1JFRElSRUNUIiwiX19OX1JFRElSRUNUX0JBU0VfUEFUSCIsInBhcnNlZEhyZWYiLCJfX05fUFJFVklFVyIsIm5vdEZvdW5kUm91dGUiLCJmZXRjaENvbXBvbmVudCIsImlzTm90Rm91bmQiLCJfX05FWFRfREFUQV9fIiwic3RhdHVzQ29kZSIsIl9yb3V0ZUluZm9fcm91dGUiLCJpc1ZhbGlkU2hhbGxvd1JvdXRlIiwiX29wdGlvbnNfc2Nyb2xsIiwic2hvdWxkU2Nyb2xsIiwicmVzZXRTY3JvbGwiLCJ1cGNvbWluZ1Njcm9sbFN0YXRlIiwidXBjb21pbmdSb3V0ZXJTdGF0ZSIsIl9zZWxmX19fTkVYVF9EQVRBX19fcHJvcHNfcGFnZVByb3BzMSIsIl9zZWxmX19fTkVYVF9EQVRBX19fcHJvcHMxIiwiX3JvdXRlSW5mb19wcm9wczEiLCJjYW5Ta2lwVXBkYXRpbmciLCJjb21wYXJlUm91dGVyU3RhdGVzIiwiZG9jdW1lbnQiLCJkb2N1bWVudEVsZW1lbnQiLCJsYW5nIiwiaGFzaFJlZ2V4IiwiZ2V0VVJMIiwiX3NoYWxsb3ciLCJfX04iLCJoYW5kbGVSb3V0ZUluZm9FcnJvciIsImxvYWRFcnJvckZhaWwiLCJpc0Fzc2V0RXJyb3IiLCJzdHlsZVNoZWV0cyIsImdldEluaXRpYWxQcm9wcyIsImdpcEVyciIsInJvdXRlSW5mb0VyciIsInJlcXVlc3RlZFJvdXRlIiwiX2RhdGFfZWZmZWN0IiwiX2RhdGFfZWZmZWN0MSIsIl9kYXRhX2VmZmVjdDIiLCJfZGF0YV9yZXNwb25zZSIsImV4aXN0aW5nSW5mbyIsImNhY2hlZFJvdXRlSW5mbyIsImZldGNoTmV4dERhdGFQYXJhbXMiLCJnZXREYXRhSHJlZiIsInNraXBJbnRlcnBvbGF0aW9uIiwic2JjIiwic2RjIiwicmVzb2x2ZWRSb3V0ZSIsImlzQVBJUm91dGUiLCJyZXMiLCJtb2QiLCJpc1ZhbGlkRWxlbWVudFR5cGUiLCJ3YXNCYWlsZWRQcmVmZXRjaCIsInNob3VsZEZldGNoRGF0YSIsIl9nZXREYXRhIiwiZmV0Y2hlZCIsImdldFByb3BlckVycm9yIiwic3ViIiwiYmVmb3JlUG9wU3RhdGUiLCJjYiIsIl9icHMiLCJvbGRVcmxOb0hhc2giLCJvbGRIYXNoIiwibmV3VXJsTm9IYXNoIiwibmV3SGFzaCIsImhhbmRsZVNtb290aFNjcm9sbCIsInNjcm9sbFRvIiwicmF3SGFzaCIsImRlY29kZVVSSUNvbXBvbmVudCIsImlkRWwiLCJnZXRFbGVtZW50QnlJZCIsInNjcm9sbEludG9WaWV3IiwibmFtZUVsIiwiZ2V0RWxlbWVudHNCeU5hbWUiLCJvbmx5SGFzaENoYW5nZSIsInByZWZldGNoIiwiaXNCb3QiLCJuYXZpZ2F0b3IiLCJ1c2VyQWdlbnQiLCJ1cmxQYXRobmFtZSIsIm9yaWdpbmFsUGF0aG5hbWUiLCJfX05FWFRfTUlERExFV0FSRV9QUkVGRVRDSCIsIl9pc1NzZyIsImlzU3NnIiwicHJpb3JpdHkiLCJfX05FWFRfT1BUSU1JU1RJQ19DTElFTlRfQ0FDSEUiLCJjb21wb25lbnRSZXN1bHQiLCJsb2FkUGFnZSIsImZuIiwiY3R4IiwiQXBwIiwiQXBwVHJlZSIsIl93cmFwQXBwIiwibG9hZEdldEluaXRpYWxQcm9wcyIsImNvbnN0cnVjdG9yIiwiaW5pdGlhbFByb3BzIiwid3JhcEFwcCIsInN1YnNjcmlwdGlvbiIsImlzRmlyc3RQb3BTdGF0ZUV2ZW50Iiwib25Qb3BTdGF0ZSIsIl9fTkEiLCJnZXRJdGVtIiwiaW5pdGlhbCIsImF1dG9FeHBvcnREeW5hbWljIiwiYXV0b0V4cG9ydCIsIl9fTkVYVF9ST1VURVJfQkFTRVBBVEgiLCJnc3NwIiwiZ2lwIiwiaXNFeHBlcmltZW50YWxDb21waWxlIiwiYXBwR2lwIiwiZ3NwIiwic2VhcmNoIiwiX2luaXRpYWxNYXRjaGVzTWlkZGxld2FyZVByb21pc2UiLCJhZGRFdmVudExpc3RlbmVyIiwic2Nyb2xsUmVzdG9yYXRpb24iXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/router.js\n"));

/***/ })

});